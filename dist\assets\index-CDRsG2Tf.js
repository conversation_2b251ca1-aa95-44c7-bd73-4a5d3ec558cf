var fo=Object.defineProperty;var po=(e,t,n)=>t in e?fo(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n;var V=(e,t,n)=>po(e,typeof t!="symbol"?t+"":t,n);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))s(i);new MutationObserver(i=>{for(const l of i)if(l.type==="childList")for(const o of l.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&s(o)}).observe(document,{childList:!0,subtree:!0});function n(i){const l={};return i.integrity&&(l.integrity=i.integrity),i.referrerPolicy&&(l.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?l.credentials="include":i.crossOrigin==="anonymous"?l.credentials="omit":l.credentials="same-origin",l}function s(i){if(i.ep)return;i.ep=!0;const l=n(i);fetch(i.href,l)}})();/**
* @vue/shared v3.5.8
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function As(e){const t=Object.create(null);for(const n of e.split(","))t[n]=1;return n=>n in t}const ie={},Kt=[],Qe=()=>{},ho=()=>!1,Gn=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Ms=e=>e.startsWith("onUpdate:"),be=Object.assign,Rs=(e,t)=>{const n=e.indexOf(t);n>-1&&e.splice(n,1)},mo=Object.prototype.hasOwnProperty,ee=(e,t)=>mo.call(e,t),$=Array.isArray,Xt=e=>wn(e)==="[object Map]",tn=e=>wn(e)==="[object Set]",Qs=e=>wn(e)==="[object Date]",K=e=>typeof e=="function",de=e=>typeof e=="string",tt=e=>typeof e=="symbol",ae=e=>e!==null&&typeof e=="object",Ui=e=>(ae(e)||K(e))&&K(e.then)&&K(e.catch),Oi=Object.prototype.toString,wn=e=>Oi.call(e),zo=e=>wn(e).slice(8,-1),Wi=e=>wn(e)==="[object Object]",Is=e=>de(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,gn=As(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),qn=e=>{const t=Object.create(null);return n=>t[n]||(t[n]=e(n))},vo=/-(\w)/g,Vt=qn(e=>e.replace(vo,(t,n)=>n?n.toUpperCase():"")),yo=/\B([A-Z])/g,$t=qn(e=>e.replace(yo,"-$1").toLowerCase()),Di=qn(e=>e.charAt(0).toUpperCase()+e.slice(1)),gs=qn(e=>e?`on${Di(e)}`:""),Et=(e,t)=>!Object.is(e,t),On=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},Vi=(e,t,n,s=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:s,value:n})},$n=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let ei;const Hi=()=>ei||(ei=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Gt(e){if($(e)){const t={};for(let n=0;n<e.length;n++){const s=e[n],i=de(s)?So(s):Gt(s);if(i)for(const l in i)t[l]=i[l]}return t}else if(de(e)||ae(e))return e}const bo=/;(?![^(]*\))/g,Co=/:([^]+)/,wo=/\/\*[^]*?\*\//g;function So(e){const t={};return e.replace(wo,"").split(bo).forEach(n=>{if(n){const s=n.split(Co);s.length>1&&(t[s[0].trim()]=s[1].trim())}}),t}function _e(e){let t="";if(de(e))t=e;else if($(e))for(let n=0;n<e.length;n++){const s=_e(e[n]);s&&(t+=s+" ")}else if(ae(e))for(const n in e)e[n]&&(t+=n+" ");return t.trim()}const xo="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",To=As(xo);function Ni(e){return!!e||e===""}function Po(e,t){if(e.length!==t.length)return!1;let n=!0;for(let s=0;n&&s<e.length;s++)n=Sn(e[s],t[s]);return n}function Sn(e,t){if(e===t)return!0;let n=Qs(e),s=Qs(t);if(n||s)return n&&s?e.getTime()===t.getTime():!1;if(n=tt(e),s=tt(t),n||s)return e===t;if(n=$(e),s=$(t),n||s)return n&&s?Po(e,t):!1;if(n=ae(e),s=ae(t),n||s){if(!n||!s)return!1;const i=Object.keys(e).length,l=Object.keys(t).length;if(i!==l)return!1;for(const o in e){const r=e.hasOwnProperty(o),g=t.hasOwnProperty(o);if(r&&!g||!r&&g||!Sn(e[o],t[o]))return!1}}return String(e)===String(t)}function Us(e,t){return e.findIndex(n=>Sn(n,t))}const $i=e=>!!(e&&e.__v_isRef===!0),oe=e=>de(e)?e:e==null?"":$(e)||ae(e)&&(e.toString===Oi||!K(e.toString))?$i(e)?oe(e.value):JSON.stringify(e,ki,2):String(e),ki=(e,t)=>$i(t)?ki(e,t.value):Xt(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((n,[s,i],l)=>(n[cs(s,l)+" =>"]=i,n),{})}:tn(t)?{[`Set(${t.size})`]:[...t.values()].map(n=>cs(n))}:tt(t)?cs(t):ae(t)&&!$(t)&&!Wi(t)?String(t):t,cs=(e,t="")=>{var n;return tt(e)?`Symbol(${(n=e.description)!=null?n:t})`:e};/**
* @vue/reactivity v3.5.8
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let We;class Eo{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=We,!t&&We&&(this.index=(We.scopes||(We.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].pause();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,n;if(this.scopes)for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].resume();for(t=0,n=this.effects.length;t<n;t++)this.effects[t].resume()}}run(t){if(this._active){const n=We;try{return We=this,t()}finally{We=n}}}on(){We=this}off(){We=this.parent}stop(t){if(this._active){let n,s;for(n=0,s=this.effects.length;n<s;n++)this.effects[n].stop();for(n=0,s=this.cleanups.length;n<s;n++)this.cleanups[n]();if(this.scopes)for(n=0,s=this.scopes.length;n<s;n++)this.scopes[n].stop(!0);if(!this.detached&&this.parent&&!t){const i=this.parent.scopes.pop();i&&i!==this&&(this.parent.scopes[this.index]=i,i.index=this.index)}this.parent=void 0,this._active=!1}}}function _o(){return We}let se;const fs=new WeakSet;class ji{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,We&&We.active&&We.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,fs.has(this)&&(fs.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Bi(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,ti(this),Ki(this);const t=se,n=Xe;se=this,Xe=!0;try{return this.fn()}finally{Xi(this),se=t,Xe=n,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Ds(t);this.deps=this.depsTail=void 0,ti(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?fs.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Cs(this)&&this.run()}get dirty(){return Cs(this)}}let Yi=0,cn;function Bi(e){e.flags|=8,e.next=cn,cn=e}function Os(){Yi++}function Ws(){if(--Yi>0)return;let e;for(;cn;){let t=cn;for(cn=void 0;t;){const n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(s){e||(e=s)}t=n}}if(e)throw e}function Ki(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Xi(e,t=!1){let n,s=e.depsTail,i=s;for(;i;){const l=i.prevDep;i.version===-1?(i===s&&(s=l),Ds(i,t),Lo(i)):n=i,i.dep.activeLink=i.prevActiveLink,i.prevActiveLink=void 0,i=l}e.deps=n,e.depsTail=s}function Cs(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Gi(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Gi(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===mn))return;e.globalVersion=mn;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!Cs(e)){e.flags&=-3;return}const n=se,s=Xe;se=e,Xe=!0;try{Ki(e);const i=e.fn(e._value);(t.version===0||Et(i,e._value))&&(e._value=i,t.version++)}catch(i){throw t.version++,i}finally{se=n,Xe=s,Xi(e,!0),e.flags&=-3}}function Ds(e,t=!1){const{dep:n,prevSub:s,nextSub:i}=e;if(s&&(s.nextSub=i,e.prevSub=void 0),i&&(i.prevSub=s,e.nextSub=void 0),n.subs===e&&(n.subs=s),!n.subs)if(n.computed){n.computed.flags&=-5;for(let l=n.computed.deps;l;l=l.nextDep)Ds(l,!0)}else n.map&&!t&&(n.map.delete(n.key),n.map.size||kn.delete(n.target))}function Lo(e){const{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let Xe=!0;const qi=[];function _t(){qi.push(Xe),Xe=!1}function Lt(){const e=qi.pop();Xe=e===void 0?!0:e}function ti(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const n=se;se=void 0;try{t()}finally{se=n}}}let mn=0;class Fo{constructor(t,n){this.sub=t,this.dep=n,this.version=n.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Vs{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.target=void 0,this.map=void 0,this.key=void 0}track(t){if(!se||!Xe||se===this.computed)return;let n=this.activeLink;if(n===void 0||n.sub!==se)n=this.activeLink=new Fo(se,this),se.deps?(n.prevDep=se.depsTail,se.depsTail.nextDep=n,se.depsTail=n):se.deps=se.depsTail=n,se.flags&4&&Ji(n);else if(n.version===-1&&(n.version=this.version,n.nextDep)){const s=n.nextDep;s.prevDep=n.prevDep,n.prevDep&&(n.prevDep.nextDep=s),n.prevDep=se.depsTail,n.nextDep=void 0,se.depsTail.nextDep=n,se.depsTail=n,se.deps===n&&(se.deps=s)}return n}trigger(t){this.version++,mn++,this.notify(t)}notify(t){Os();try{for(let n=this.subs;n;n=n.prevSub)n.sub.notify()&&n.sub.dep.notify()}finally{Ws()}}}function Ji(e){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let s=t.deps;s;s=s.nextDep)Ji(s)}const n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}const kn=new WeakMap,Dt=Symbol(""),ws=Symbol(""),zn=Symbol("");function xe(e,t,n){if(Xe&&se){let s=kn.get(e);s||kn.set(e,s=new Map);let i=s.get(n);i||(s.set(n,i=new Vs),i.target=e,i.map=s,i.key=n),i.track()}}function zt(e,t,n,s,i,l){const o=kn.get(e);if(!o){mn++;return}const r=g=>{g&&g.trigger()};if(Os(),t==="clear")o.forEach(r);else{const g=$(e),h=g&&Is(n);if(g&&n==="length"){const f=Number(s);o.forEach((m,z)=>{(z==="length"||z===zn||!tt(z)&&z>=f)&&r(m)})}else switch(n!==void 0&&r(o.get(n)),h&&r(o.get(zn)),t){case"add":g?h&&r(o.get("length")):(r(o.get(Dt)),Xt(e)&&r(o.get(ws)));break;case"delete":g||(r(o.get(Dt)),Xt(e)&&r(o.get(ws)));break;case"set":Xt(e)&&r(o.get(Dt));break}}Ws()}function Yt(e){const t=te(e);return t===e?t:(xe(t,"iterate",zn),$e(e)?t:t.map(we))}function Jn(e){return xe(e=te(e),"iterate",zn),e}const Ao={__proto__:null,[Symbol.iterator](){return ds(this,Symbol.iterator,we)},concat(...e){return Yt(this).concat(...e.map(t=>$(t)?Yt(t):t))},entries(){return ds(this,"entries",e=>(e[1]=we(e[1]),e))},every(e,t){return dt(this,"every",e,t,void 0,arguments)},filter(e,t){return dt(this,"filter",e,t,n=>n.map(we),arguments)},find(e,t){return dt(this,"find",e,t,we,arguments)},findIndex(e,t){return dt(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return dt(this,"findLast",e,t,we,arguments)},findLastIndex(e,t){return dt(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return dt(this,"forEach",e,t,void 0,arguments)},includes(...e){return ps(this,"includes",e)},indexOf(...e){return ps(this,"indexOf",e)},join(e){return Yt(this).join(e)},lastIndexOf(...e){return ps(this,"lastIndexOf",e)},map(e,t){return dt(this,"map",e,t,void 0,arguments)},pop(){return on(this,"pop")},push(...e){return on(this,"push",e)},reduce(e,...t){return ni(this,"reduce",e,t)},reduceRight(e,...t){return ni(this,"reduceRight",e,t)},shift(){return on(this,"shift")},some(e,t){return dt(this,"some",e,t,void 0,arguments)},splice(...e){return on(this,"splice",e)},toReversed(){return Yt(this).toReversed()},toSorted(e){return Yt(this).toSorted(e)},toSpliced(...e){return Yt(this).toSpliced(...e)},unshift(...e){return on(this,"unshift",e)},values(){return ds(this,"values",we)}};function ds(e,t,n){const s=Jn(e),i=s[t]();return s!==e&&!$e(e)&&(i._next=i.next,i.next=()=>{const l=i._next();return l.value&&(l.value=n(l.value)),l}),i}const Mo=Array.prototype;function dt(e,t,n,s,i,l){const o=Jn(e),r=o!==e&&!$e(e),g=o[t];if(g!==Mo[t]){const m=g.apply(e,l);return r?we(m):m}let h=n;o!==e&&(r?h=function(m,z){return n.call(this,we(m),z,e)}:n.length>2&&(h=function(m,z){return n.call(this,m,z,e)}));const f=g.call(o,h,s);return r&&i?i(f):f}function ni(e,t,n,s){const i=Jn(e);let l=n;return i!==e&&($e(e)?n.length>3&&(l=function(o,r,g){return n.call(this,o,r,g,e)}):l=function(o,r,g){return n.call(this,o,we(r),g,e)}),i[t](l,...s)}function ps(e,t,n){const s=te(e);xe(s,"iterate",zn);const i=s[t](...n);return(i===-1||i===!1)&&js(n[0])?(n[0]=te(n[0]),s[t](...n)):i}function on(e,t,n=[]){_t(),Os();const s=te(e)[t].apply(e,n);return Ws(),Lt(),s}const Ro=As("__proto__,__v_isRef,__isVue"),Zi=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(tt));function Io(e){tt(e)||(e=String(e));const t=te(this);return xe(t,"has",e),t.hasOwnProperty(e)}class Qi{constructor(t=!1,n=!1){this._isReadonly=t,this._isShallow=n}get(t,n,s){const i=this._isReadonly,l=this._isShallow;if(n==="__v_isReactive")return!i;if(n==="__v_isReadonly")return i;if(n==="__v_isShallow")return l;if(n==="__v_raw")return s===(i?l?Ko:sl:l?nl:tl).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(s)?t:void 0;const o=$(t);if(!i){let g;if(o&&(g=Ao[n]))return g;if(n==="hasOwnProperty")return Io}const r=Reflect.get(t,n,Se(t)?t:s);return(tt(n)?Zi.has(n):Ro(n))||(i||xe(t,"get",n),l)?r:Se(r)?o&&Is(n)?r:r.value:ae(r)?i?il(r):$s(r):r}}class el extends Qi{constructor(t=!1){super(!1,t)}set(t,n,s,i){let l=t[n];if(!this._isShallow){const g=Ht(l);if(!$e(s)&&!Ht(s)&&(l=te(l),s=te(s)),!$(t)&&Se(l)&&!Se(s))return g?!1:(l.value=s,!0)}const o=$(t)&&Is(n)?Number(n)<t.length:ee(t,n),r=Reflect.set(t,n,s,Se(t)?t:i);return t===te(i)&&(o?Et(s,l)&&zt(t,"set",n,s):zt(t,"add",n,s)),r}deleteProperty(t,n){const s=ee(t,n);t[n];const i=Reflect.deleteProperty(t,n);return i&&s&&zt(t,"delete",n,void 0),i}has(t,n){const s=Reflect.has(t,n);return(!tt(n)||!Zi.has(n))&&xe(t,"has",n),s}ownKeys(t){return xe(t,"iterate",$(t)?"length":Dt),Reflect.ownKeys(t)}}class Uo extends Qi{constructor(t=!1){super(!0,t)}set(t,n){return!0}deleteProperty(t,n){return!0}}const Oo=new el,Wo=new Uo,Do=new el(!0);const Hs=e=>e,Zn=e=>Reflect.getPrototypeOf(e);function Ln(e,t,n=!1,s=!1){e=e.__v_raw;const i=te(e),l=te(t);n||(Et(t,l)&&xe(i,"get",t),xe(i,"get",l));const{has:o}=Zn(i),r=s?Hs:n?Ys:we;if(o.call(i,t))return r(e.get(t));if(o.call(i,l))return r(e.get(l));e!==i&&e.get(t)}function Fn(e,t=!1){const n=this.__v_raw,s=te(n),i=te(e);return t||(Et(e,i)&&xe(s,"has",e),xe(s,"has",i)),e===i?n.has(e):n.has(e)||n.has(i)}function An(e,t=!1){return e=e.__v_raw,!t&&xe(te(e),"iterate",Dt),Reflect.get(e,"size",e)}function si(e,t=!1){!t&&!$e(e)&&!Ht(e)&&(e=te(e));const n=te(this);return Zn(n).has.call(n,e)||(n.add(e),zt(n,"add",e,e)),this}function ii(e,t,n=!1){!n&&!$e(t)&&!Ht(t)&&(t=te(t));const s=te(this),{has:i,get:l}=Zn(s);let o=i.call(s,e);o||(e=te(e),o=i.call(s,e));const r=l.call(s,e);return s.set(e,t),o?Et(t,r)&&zt(s,"set",e,t):zt(s,"add",e,t),this}function li(e){const t=te(this),{has:n,get:s}=Zn(t);let i=n.call(t,e);i||(e=te(e),i=n.call(t,e)),s&&s.call(t,e);const l=t.delete(e);return i&&zt(t,"delete",e,void 0),l}function oi(){const e=te(this),t=e.size!==0,n=e.clear();return t&&zt(e,"clear",void 0,void 0),n}function Mn(e,t){return function(s,i){const l=this,o=l.__v_raw,r=te(o),g=t?Hs:e?Ys:we;return!e&&xe(r,"iterate",Dt),o.forEach((h,f)=>s.call(i,g(h),g(f),l))}}function Rn(e,t,n){return function(...s){const i=this.__v_raw,l=te(i),o=Xt(l),r=e==="entries"||e===Symbol.iterator&&o,g=e==="keys"&&o,h=i[e](...s),f=n?Hs:t?Ys:we;return!t&&xe(l,"iterate",g?ws:Dt),{next(){const{value:m,done:z}=h.next();return z?{value:m,done:z}:{value:r?[f(m[0]),f(m[1])]:f(m),done:z}},[Symbol.iterator](){return this}}}}function wt(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Vo(){const e={get(l){return Ln(this,l)},get size(){return An(this)},has:Fn,add:si,set:ii,delete:li,clear:oi,forEach:Mn(!1,!1)},t={get(l){return Ln(this,l,!1,!0)},get size(){return An(this)},has:Fn,add(l){return si.call(this,l,!0)},set(l,o){return ii.call(this,l,o,!0)},delete:li,clear:oi,forEach:Mn(!1,!0)},n={get(l){return Ln(this,l,!0)},get size(){return An(this,!0)},has(l){return Fn.call(this,l,!0)},add:wt("add"),set:wt("set"),delete:wt("delete"),clear:wt("clear"),forEach:Mn(!0,!1)},s={get(l){return Ln(this,l,!0,!0)},get size(){return An(this,!0)},has(l){return Fn.call(this,l,!0)},add:wt("add"),set:wt("set"),delete:wt("delete"),clear:wt("clear"),forEach:Mn(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach(l=>{e[l]=Rn(l,!1,!1),n[l]=Rn(l,!0,!1),t[l]=Rn(l,!1,!0),s[l]=Rn(l,!0,!0)}),[e,n,t,s]}const[Ho,No,$o,ko]=Vo();function Ns(e,t){const n=t?e?ko:$o:e?No:Ho;return(s,i,l)=>i==="__v_isReactive"?!e:i==="__v_isReadonly"?e:i==="__v_raw"?s:Reflect.get(ee(n,i)&&i in s?n:s,i,l)}const jo={get:Ns(!1,!1)},Yo={get:Ns(!1,!0)},Bo={get:Ns(!0,!1)};const tl=new WeakMap,nl=new WeakMap,sl=new WeakMap,Ko=new WeakMap;function Xo(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Go(e){return e.__v_skip||!Object.isExtensible(e)?0:Xo(zo(e))}function $s(e){return Ht(e)?e:ks(e,!1,Oo,jo,tl)}function qo(e){return ks(e,!1,Do,Yo,nl)}function il(e){return ks(e,!0,Wo,Bo,sl)}function ks(e,t,n,s,i){if(!ae(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const l=i.get(e);if(l)return l;const o=Go(e);if(o===0)return e;const r=new Proxy(e,o===2?s:n);return i.set(e,r),r}function qt(e){return Ht(e)?qt(e.__v_raw):!!(e&&e.__v_isReactive)}function Ht(e){return!!(e&&e.__v_isReadonly)}function $e(e){return!!(e&&e.__v_isShallow)}function js(e){return e?!!e.__v_raw:!1}function te(e){const t=e&&e.__v_raw;return t?te(t):e}function Jo(e){return!ee(e,"__v_skip")&&Object.isExtensible(e)&&Vi(e,"__v_skip",!0),e}const we=e=>ae(e)?$s(e):e,Ys=e=>ae(e)?il(e):e;function Se(e){return e?e.__v_isRef===!0:!1}function _(e){return Zo(e,!1)}function Zo(e,t){return Se(e)?e:new Qo(e,t)}class Qo{constructor(t,n){this.dep=new Vs,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=n?t:te(t),this._value=n?t:we(t),this.__v_isShallow=n}get value(){return this.dep.track(),this._value}set value(t){const n=this._rawValue,s=this.__v_isShallow||$e(t)||Ht(t);t=s?t:te(t),Et(t,n)&&(this._rawValue=t,this._value=s?t:we(t),this.dep.trigger())}}function ea(e){return Se(e)?e.value:e}const ta={get:(e,t,n)=>t==="__v_raw"?e:ea(Reflect.get(e,t,n)),set:(e,t,n,s)=>{const i=e[t];return Se(i)&&!Se(n)?(i.value=n,!0):Reflect.set(e,t,n,s)}};function ll(e){return qt(e)?e:new Proxy(e,ta)}class na{constructor(t,n,s){this.fn=t,this.setter=n,this._value=void 0,this.dep=new Vs(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=mn-1,this.effect=this,this.__v_isReadonly=!n,this.isSSR=s}notify(){if(this.flags|=16,!(this.flags&8)&&se!==this)return Bi(this),!0}get value(){const t=this.dep.track();return Gi(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function sa(e,t,n=!1){let s,i;return K(e)?s=e:(s=e.get,i=e.set),new na(s,i,n)}const In={},jn=new WeakMap;let Wt;function ia(e,t=!1,n=Wt){if(n){let s=jn.get(n);s||jn.set(n,s=[]),s.push(e)}}function la(e,t,n=ie){const{immediate:s,deep:i,once:l,scheduler:o,augmentJob:r,call:g}=n,h=U=>i?U:$e(U)||i===!1||i===0?mt(U,1):mt(U);let f,m,z,x,R=!1,O=!1;if(Se(e)?(m=()=>e.value,R=$e(e)):qt(e)?(m=()=>h(e),R=!0):$(e)?(O=!0,R=e.some(U=>qt(U)||$e(U)),m=()=>e.map(U=>{if(Se(U))return U.value;if(qt(U))return h(U);if(K(U))return g?g(U,2):U()})):K(e)?t?m=g?()=>g(e,2):e:m=()=>{if(z){_t();try{z()}finally{Lt()}}const U=Wt;Wt=f;try{return g?g(e,3,[x]):e(x)}finally{Wt=U}}:m=Qe,t&&i){const U=m,q=i===!0?1/0:i;m=()=>mt(U(),q)}const B=_o(),D=()=>{f.stop(),B&&Rs(B.effects,f)};if(l&&t){const U=t;t=(...q)=>{U(...q),D()}}let j=O?new Array(e.length).fill(In):In;const Y=U=>{if(!(!(f.flags&1)||!f.dirty&&!U))if(t){const q=f.run();if(i||R||(O?q.some((pe,he)=>Et(pe,j[he])):Et(q,j))){z&&z();const pe=Wt;Wt=f;try{const he=[q,j===In?void 0:O&&j[0]===In?[]:j,x];g?g(t,3,he):t(...he),j=q}finally{Wt=pe}}}else f.run()};return r&&r(Y),f=new ji(m),f.scheduler=o?()=>o(Y,!1):Y,x=U=>ia(U,!1,f),z=f.onStop=()=>{const U=jn.get(f);if(U){if(g)g(U,4);else for(const q of U)q();jn.delete(f)}},t?s?Y(!0):j=f.run():o?o(Y.bind(null,!0),!0):f.run(),D.pause=f.pause.bind(f),D.resume=f.resume.bind(f),D.stop=D,D}function mt(e,t=1/0,n){if(t<=0||!ae(e)||e.__v_skip||(n=n||new Set,n.has(e)))return e;if(n.add(e),t--,Se(e))mt(e.value,t,n);else if($(e))for(let s=0;s<e.length;s++)mt(e[s],t,n);else if(tn(e)||Xt(e))e.forEach(s=>{mt(s,t,n)});else if(Wi(e)){for(const s in e)mt(e[s],t,n);for(const s of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,s)&&mt(e[s],t,n)}return e}/**
* @vue/runtime-core v3.5.8
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function xn(e,t,n,s){try{return s?e(...s):e()}catch(i){Qn(i,t,n)}}function nt(e,t,n,s){if(K(e)){const i=xn(e,t,n,s);return i&&Ui(i)&&i.catch(l=>{Qn(l,t,n)}),i}if($(e)){const i=[];for(let l=0;l<e.length;l++)i.push(nt(e[l],t,n,s));return i}}function Qn(e,t,n,s=!0){const i=t?t.vnode:null,{errorHandler:l,throwUnhandledErrorInProduction:o}=t&&t.appContext.config||ie;if(t){let r=t.parent;const g=t.proxy,h=`https://vuejs.org/error-reference/#runtime-${n}`;for(;r;){const f=r.ec;if(f){for(let m=0;m<f.length;m++)if(f[m](e,g,h)===!1)return}r=r.parent}if(l){_t(),xn(l,null,10,[e,g,h]),Lt();return}}oa(e,n,i,s,o)}function oa(e,t,n,s=!0,i=!1){if(i)throw e;console.error(e)}let vn=!1,Ss=!1;const Le=[];let Je=0;const Jt=[];let xt=null,Bt=0;const ol=Promise.resolve();let Bs=null;function al(e){const t=Bs||ol;return e?t.then(this?e.bind(this):e):t}function aa(e){let t=vn?Je+1:0,n=Le.length;for(;t<n;){const s=t+n>>>1,i=Le[s],l=yn(i);l<e||l===e&&i.flags&2?t=s+1:n=s}return t}function Ks(e){if(!(e.flags&1)){const t=yn(e),n=Le[Le.length-1];!n||!(e.flags&2)&&t>=yn(n)?Le.push(e):Le.splice(aa(t),0,e),e.flags|=1,rl()}}function rl(){!vn&&!Ss&&(Ss=!0,Bs=ol.then(gl))}function ra(e){$(e)?Jt.push(...e):xt&&e.id===-1?xt.splice(Bt+1,0,e):e.flags&1||(Jt.push(e),e.flags|=1),rl()}function ai(e,t,n=vn?Je+1:0){for(;n<Le.length;n++){const s=Le[n];if(s&&s.flags&2){if(e&&s.id!==e.uid)continue;Le.splice(n,1),n--,s.flags&4&&(s.flags&=-2),s(),s.flags&4||(s.flags&=-2)}}}function ul(e){if(Jt.length){const t=[...new Set(Jt)].sort((n,s)=>yn(n)-yn(s));if(Jt.length=0,xt){xt.push(...t);return}for(xt=t,Bt=0;Bt<xt.length;Bt++){const n=xt[Bt];n.flags&4&&(n.flags&=-2),n.flags&8||n(),n.flags&=-2}xt=null,Bt=0}}const yn=e=>e.id==null?e.flags&2?-1:1/0:e.id;function gl(e){Ss=!1,vn=!0;try{for(Je=0;Je<Le.length;Je++){const t=Le[Je];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),xn(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Je<Le.length;Je++){const t=Le[Je];t&&(t.flags&=-2)}Je=0,Le.length=0,ul(),vn=!1,Bs=null,(Le.length||Jt.length)&&gl()}}let Ne=null,cl=null;function Yn(e){const t=Ne;return Ne=e,cl=e&&e.type.__scopeId||null,t}function ua(e,t=Ne,n){if(!t||e._n)return e;const s=(...i)=>{s._d&&hi(-1);const l=Yn(t);let o;try{o=e(...i)}finally{Yn(l),s._d&&hi(1)}return o};return s._n=!0,s._c=!0,s._d=!0,s}function F(e,t){if(Ne===null)return e;const n=is(Ne),s=e.dirs||(e.dirs=[]);for(let i=0;i<t.length;i++){let[l,o,r,g=ie]=t[i];l&&(K(l)&&(l={mounted:l,updated:l}),l.deep&&mt(o),s.push({dir:l,instance:n,value:o,oldValue:void 0,arg:r,modifiers:g}))}return e}function It(e,t,n,s){const i=e.dirs,l=t&&t.dirs;for(let o=0;o<i.length;o++){const r=i[o];l&&(r.oldValue=l[o].value);let g=r.dir[s];g&&(_t(),nt(g,n,8,[e.el,r,e,t]),Lt())}}const ga=Symbol("_vte"),ca=e=>e.__isTeleport;function Xs(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Xs(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function fl(e,t){return K(e)?be({name:e.name},t,{setup:e}):e}function dl(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function xs(e,t,n,s,i=!1){if($(e)){e.forEach((R,O)=>xs(R,t&&($(t)?t[O]:t),n,s,i));return}if(fn(s)&&!i)return;const l=s.shapeFlag&4?is(s.component):s.el,o=i?null:l,{i:r,r:g}=e,h=t&&t.r,f=r.refs===ie?r.refs={}:r.refs,m=r.setupState,z=te(m),x=m===ie?()=>!1:R=>ee(z,R);if(h!=null&&h!==g&&(de(h)?(f[h]=null,x(h)&&(m[h]=null)):Se(h)&&(h.value=null)),K(g))xn(g,r,12,[o,f]);else{const R=de(g),O=Se(g);if(R||O){const B=()=>{if(e.f){const D=R?x(g)?m[g]:f[g]:g.value;i?$(D)&&Rs(D,l):$(D)?D.includes(l)||D.push(l):R?(f[g]=[l],x(g)&&(m[g]=f[g])):(g.value=[l],e.k&&(f[e.k]=g.value))}else R?(f[g]=o,x(g)&&(m[g]=o)):O&&(g.value=o,e.k&&(f[e.k]=o))};o?(B.id=-1,Oe(B,n)):B()}}}const fn=e=>!!e.type.__asyncLoader,pl=e=>e.type.__isKeepAlive;function fa(e,t){hl(e,"a",t)}function da(e,t){hl(e,"da",t)}function hl(e,t,n=Fe){const s=e.__wdc||(e.__wdc=()=>{let i=n;for(;i;){if(i.isDeactivated)return;i=i.parent}return e()});if(es(t,s,n),n){let i=n.parent;for(;i&&i.parent;)pl(i.parent.vnode)&&pa(s,t,n,i),i=i.parent}}function pa(e,t,n,s){const i=es(t,e,s,!0);ml(()=>{Rs(s[t],i)},n)}function es(e,t,n=Fe,s=!1){if(n){const i=n[e]||(n[e]=[]),l=t.__weh||(t.__weh=(...o)=>{_t();const r=Tn(n),g=nt(t,n,e,o);return r(),Lt(),g});return s?i.unshift(l):i.push(l),l}}const yt=e=>(t,n=Fe)=>{(!ss||e==="sp")&&es(e,(...s)=>t(...s),n)},ha=yt("bm"),Wn=yt("m"),ma=yt("bu"),za=yt("u"),va=yt("bum"),ml=yt("um"),ya=yt("sp"),ba=yt("rtg"),Ca=yt("rtc");function wa(e,t=Fe){es("ec",e,t)}const Sa=Symbol.for("v-ndc");function pt(e,t,n,s){let i;const l=n,o=$(e);if(o||de(e)){const r=o&&qt(e);let g=!1;r&&(g=!$e(e),e=Jn(e)),i=new Array(e.length);for(let h=0,f=e.length;h<f;h++)i[h]=t(g?we(e[h]):e[h],h,void 0,l)}else if(typeof e=="number"){i=new Array(e);for(let r=0;r<e;r++)i[r]=t(r+1,r,void 0,l)}else if(ae(e))if(e[Symbol.iterator])i=Array.from(e,(r,g)=>t(r,g,void 0,l));else{const r=Object.keys(e);i=new Array(r.length);for(let g=0,h=r.length;g<h;g++){const f=r[g];i[g]=t(e[f],f,g,l)}}else i=[];return i}const Ts=e=>e?Wl(e)?is(e):Ts(e.parent):null,dn=be(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Ts(e.parent),$root:e=>Ts(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Gs(e),$forceUpdate:e=>e.f||(e.f=()=>{Ks(e.update)}),$nextTick:e=>e.n||(e.n=al.bind(e.proxy)),$watch:e=>Ya.bind(e)}),hs=(e,t)=>e!==ie&&!e.__isScriptSetup&&ee(e,t),xa={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:n,setupState:s,data:i,props:l,accessCache:o,type:r,appContext:g}=e;let h;if(t[0]!=="$"){const x=o[t];if(x!==void 0)switch(x){case 1:return s[t];case 2:return i[t];case 4:return n[t];case 3:return l[t]}else{if(hs(s,t))return o[t]=1,s[t];if(i!==ie&&ee(i,t))return o[t]=2,i[t];if((h=e.propsOptions[0])&&ee(h,t))return o[t]=3,l[t];if(n!==ie&&ee(n,t))return o[t]=4,n[t];Ps&&(o[t]=0)}}const f=dn[t];let m,z;if(f)return t==="$attrs"&&xe(e.attrs,"get",""),f(e);if((m=r.__cssModules)&&(m=m[t]))return m;if(n!==ie&&ee(n,t))return o[t]=4,n[t];if(z=g.config.globalProperties,ee(z,t))return z[t]},set({_:e},t,n){const{data:s,setupState:i,ctx:l}=e;return hs(i,t)?(i[t]=n,!0):s!==ie&&ee(s,t)?(s[t]=n,!0):ee(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(l[t]=n,!0)},has({_:{data:e,setupState:t,accessCache:n,ctx:s,appContext:i,propsOptions:l}},o){let r;return!!n[o]||e!==ie&&ee(e,o)||hs(t,o)||(r=l[0])&&ee(r,o)||ee(s,o)||ee(dn,o)||ee(i.config.globalProperties,o)},defineProperty(e,t,n){return n.get!=null?e._.accessCache[t]=0:ee(n,"value")&&this.set(e,t,n.value,null),Reflect.defineProperty(e,t,n)}};function ri(e){return $(e)?e.reduce((t,n)=>(t[n]=null,t),{}):e}let Ps=!0;function Ta(e){const t=Gs(e),n=e.proxy,s=e.ctx;Ps=!1,t.beforeCreate&&ui(t.beforeCreate,e,"bc");const{data:i,computed:l,methods:o,watch:r,provide:g,inject:h,created:f,beforeMount:m,mounted:z,beforeUpdate:x,updated:R,activated:O,deactivated:B,beforeDestroy:D,beforeUnmount:j,destroyed:Y,unmounted:U,render:q,renderTracked:pe,renderTriggered:he,errorCaptured:ve,serverPrefetch:Me,expose:Re,inheritAttrs:ke,components:Ft,directives:st,filters:it}=t;if(h&&Pa(h,s,null),o)for(const le in o){const Q=o[le];K(Q)&&(s[le]=Q.bind(n))}if(i){const le=i.call(n,n);ae(le)&&(e.data=$s(le))}if(Ps=!0,l)for(const le in l){const Q=l[le],Ie=K(Q)?Q.bind(n,n):K(Q.get)?Q.get.bind(n,n):Qe,Ve=!K(Q)&&K(Q.set)?Q.set.bind(n):Qe,Ue=f0({get:Ie,set:Ve});Object.defineProperty(s,le,{enumerable:!0,configurable:!0,get:()=>Ue.value,set:Ae=>Ue.value=Ae})}if(r)for(const le in r)zl(r[le],s,n,le);if(g){const le=K(g)?g.call(n):g;Reflect.ownKeys(le).forEach(Q=>{Ma(Q,le[Q])})}f&&ui(f,e,"c");function me(le,Q){$(Q)?Q.forEach(Ie=>le(Ie.bind(n))):Q&&le(Q.bind(n))}if(me(ha,m),me(Wn,z),me(ma,x),me(za,R),me(fa,O),me(da,B),me(wa,ve),me(Ca,pe),me(ba,he),me(va,j),me(ml,U),me(ya,Me),$(Re))if(Re.length){const le=e.exposed||(e.exposed={});Re.forEach(Q=>{Object.defineProperty(le,Q,{get:()=>n[Q],set:Ie=>n[Q]=Ie})})}else e.exposed||(e.exposed={});q&&e.render===Qe&&(e.render=q),ke!=null&&(e.inheritAttrs=ke),Ft&&(e.components=Ft),st&&(e.directives=st),Me&&dl(e)}function Pa(e,t,n=Qe){$(e)&&(e=Es(e));for(const s in e){const i=e[s];let l;ae(i)?"default"in i?l=Dn(i.from||s,i.default,!0):l=Dn(i.from||s):l=Dn(i),Se(l)?Object.defineProperty(t,s,{enumerable:!0,configurable:!0,get:()=>l.value,set:o=>l.value=o}):t[s]=l}}function ui(e,t,n){nt($(e)?e.map(s=>s.bind(t.proxy)):e.bind(t.proxy),t,n)}function zl(e,t,n,s){let i=s.includes(".")?Al(n,s):()=>n[s];if(de(e)){const l=t[e];K(l)&&pn(i,l)}else if(K(e))pn(i,e.bind(n));else if(ae(e))if($(e))e.forEach(l=>zl(l,t,n,s));else{const l=K(e.handler)?e.handler.bind(n):t[e.handler];K(l)&&pn(i,l,e)}}function Gs(e){const t=e.type,{mixins:n,extends:s}=t,{mixins:i,optionsCache:l,config:{optionMergeStrategies:o}}=e.appContext,r=l.get(t);let g;return r?g=r:!i.length&&!n&&!s?g=t:(g={},i.length&&i.forEach(h=>Bn(g,h,o,!0)),Bn(g,t,o)),ae(t)&&l.set(t,g),g}function Bn(e,t,n,s=!1){const{mixins:i,extends:l}=t;l&&Bn(e,l,n,!0),i&&i.forEach(o=>Bn(e,o,n,!0));for(const o in t)if(!(s&&o==="expose")){const r=Ea[o]||n&&n[o];e[o]=r?r(e[o],t[o]):t[o]}return e}const Ea={data:gi,props:ci,emits:ci,methods:un,computed:un,beforeCreate:Ee,created:Ee,beforeMount:Ee,mounted:Ee,beforeUpdate:Ee,updated:Ee,beforeDestroy:Ee,beforeUnmount:Ee,destroyed:Ee,unmounted:Ee,activated:Ee,deactivated:Ee,errorCaptured:Ee,serverPrefetch:Ee,components:un,directives:un,watch:La,provide:gi,inject:_a};function gi(e,t){return t?e?function(){return be(K(e)?e.call(this,this):e,K(t)?t.call(this,this):t)}:t:e}function _a(e,t){return un(Es(e),Es(t))}function Es(e){if($(e)){const t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function Ee(e,t){return e?[...new Set([].concat(e,t))]:t}function un(e,t){return e?be(Object.create(null),e,t):t}function ci(e,t){return e?$(e)&&$(t)?[...new Set([...e,...t])]:be(Object.create(null),ri(e),ri(t??{})):t}function La(e,t){if(!e)return t;if(!t)return e;const n=be(Object.create(null),e);for(const s in t)n[s]=Ee(e[s],t[s]);return n}function vl(){return{app:null,config:{isNativeTag:ho,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Fa=0;function Aa(e,t){return function(s,i=null){K(s)||(s=be({},s)),i!=null&&!ae(i)&&(i=null);const l=vl(),o=new WeakSet,r=[];let g=!1;const h=l.app={_uid:Fa++,_component:s,_props:i,_container:null,_context:l,_instance:null,version:d0,get config(){return l.config},set config(f){},use(f,...m){return o.has(f)||(f&&K(f.install)?(o.add(f),f.install(h,...m)):K(f)&&(o.add(f),f(h,...m))),h},mixin(f){return l.mixins.includes(f)||l.mixins.push(f),h},component(f,m){return m?(l.components[f]=m,h):l.components[f]},directive(f,m){return m?(l.directives[f]=m,h):l.directives[f]},mount(f,m,z){if(!g){const x=h._ceVNode||et(s,i);return x.appContext=l,z===!0?z="svg":z===!1&&(z=void 0),m&&t?t(x,f):e(x,f,z),g=!0,h._container=f,f.__vue_app__=h,is(x.component)}},onUnmount(f){r.push(f)},unmount(){g&&(nt(r,h._instance,16),e(null,h._container),delete h._container.__vue_app__)},provide(f,m){return l.provides[f]=m,h},runWithContext(f){const m=Zt;Zt=h;try{return f()}finally{Zt=m}}};return h}}let Zt=null;function Ma(e,t){if(Fe){let n=Fe.provides;const s=Fe.parent&&Fe.parent.provides;s===n&&(n=Fe.provides=Object.create(s)),n[e]=t}}function Dn(e,t,n=!1){const s=Fe||Ne;if(s||Zt){const i=Zt?Zt._context.provides:s?s.parent==null?s.vnode.appContext&&s.vnode.appContext.provides:s.parent.provides:void 0;if(i&&e in i)return i[e];if(arguments.length>1)return n&&K(t)?t.call(s&&s.proxy):t}}const yl={},bl=()=>Object.create(yl),Cl=e=>Object.getPrototypeOf(e)===yl;function Ra(e,t,n,s=!1){const i={},l=bl();e.propsDefaults=Object.create(null),wl(e,t,i,l);for(const o in e.propsOptions[0])o in i||(i[o]=void 0);n?e.props=s?i:qo(i):e.type.props?e.props=i:e.props=l,e.attrs=l}function Ia(e,t,n,s){const{props:i,attrs:l,vnode:{patchFlag:o}}=e,r=te(i),[g]=e.propsOptions;let h=!1;if((s||o>0)&&!(o&16)){if(o&8){const f=e.vnode.dynamicProps;for(let m=0;m<f.length;m++){let z=f[m];if(ts(e.emitsOptions,z))continue;const x=t[z];if(g)if(ee(l,z))x!==l[z]&&(l[z]=x,h=!0);else{const R=Vt(z);i[R]=_s(g,r,R,x,e,!1)}else x!==l[z]&&(l[z]=x,h=!0)}}}else{wl(e,t,i,l)&&(h=!0);let f;for(const m in r)(!t||!ee(t,m)&&((f=$t(m))===m||!ee(t,f)))&&(g?n&&(n[m]!==void 0||n[f]!==void 0)&&(i[m]=_s(g,r,m,void 0,e,!0)):delete i[m]);if(l!==r)for(const m in l)(!t||!ee(t,m))&&(delete l[m],h=!0)}h&&zt(e.attrs,"set","")}function wl(e,t,n,s){const[i,l]=e.propsOptions;let o=!1,r;if(t)for(let g in t){if(gn(g))continue;const h=t[g];let f;i&&ee(i,f=Vt(g))?!l||!l.includes(f)?n[f]=h:(r||(r={}))[f]=h:ts(e.emitsOptions,g)||(!(g in s)||h!==s[g])&&(s[g]=h,o=!0)}if(l){const g=te(n),h=r||ie;for(let f=0;f<l.length;f++){const m=l[f];n[m]=_s(i,g,m,h[m],e,!ee(h,m))}}return o}function _s(e,t,n,s,i,l){const o=e[n];if(o!=null){const r=ee(o,"default");if(r&&s===void 0){const g=o.default;if(o.type!==Function&&!o.skipFactory&&K(g)){const{propsDefaults:h}=i;if(n in h)s=h[n];else{const f=Tn(i);s=h[n]=g.call(null,t),f()}}else s=g;i.ce&&i.ce._setProp(n,s)}o[0]&&(l&&!r?s=!1:o[1]&&(s===""||s===$t(n))&&(s=!0))}return s}const Ua=new WeakMap;function Sl(e,t,n=!1){const s=n?Ua:t.propsCache,i=s.get(e);if(i)return i;const l=e.props,o={},r=[];let g=!1;if(!K(e)){const f=m=>{g=!0;const[z,x]=Sl(m,t,!0);be(o,z),x&&r.push(...x)};!n&&t.mixins.length&&t.mixins.forEach(f),e.extends&&f(e.extends),e.mixins&&e.mixins.forEach(f)}if(!l&&!g)return ae(e)&&s.set(e,Kt),Kt;if($(l))for(let f=0;f<l.length;f++){const m=Vt(l[f]);fi(m)&&(o[m]=ie)}else if(l)for(const f in l){const m=Vt(f);if(fi(m)){const z=l[f],x=o[m]=$(z)||K(z)?{type:z}:be({},z),R=x.type;let O=!1,B=!0;if($(R))for(let D=0;D<R.length;++D){const j=R[D],Y=K(j)&&j.name;if(Y==="Boolean"){O=!0;break}else Y==="String"&&(B=!1)}else O=K(R)&&R.name==="Boolean";x[0]=O,x[1]=B,(O||ee(x,"default"))&&r.push(m)}}const h=[o,r];return ae(e)&&s.set(e,h),h}function fi(e){return e[0]!=="$"&&!gn(e)}const xl=e=>e[0]==="_"||e==="$stable",qs=e=>$(e)?e.map(Ze):[Ze(e)],Oa=(e,t,n)=>{if(t._n)return t;const s=ua((...i)=>qs(t(...i)),n);return s._c=!1,s},Tl=(e,t,n)=>{const s=e._ctx;for(const i in e){if(xl(i))continue;const l=e[i];if(K(l))t[i]=Oa(i,l,s);else if(l!=null){const o=qs(l);t[i]=()=>o}}},Pl=(e,t)=>{const n=qs(t);e.slots.default=()=>n},El=(e,t,n)=>{for(const s in t)(n||s!=="_")&&(e[s]=t[s])},Wa=(e,t,n)=>{const s=e.slots=bl();if(e.vnode.shapeFlag&32){const i=t._;i?(El(s,t,n),n&&Vi(s,"_",i,!0)):Tl(t,s)}else t&&Pl(e,t)},Da=(e,t,n)=>{const{vnode:s,slots:i}=e;let l=!0,o=ie;if(s.shapeFlag&32){const r=t._;r?n&&r===1?l=!1:El(i,t,n):(l=!t.$stable,Tl(t,i)),o=t}else t&&(Pl(e,t),o={default:1});if(l)for(const r in i)!xl(r)&&o[r]==null&&delete i[r]},Oe=Za;function Va(e){return Ha(e)}function Ha(e,t){const n=Hi();n.__VUE__=!0;const{insert:s,remove:i,patchProp:l,createElement:o,createText:r,createComment:g,setText:h,setElementText:f,parentNode:m,nextSibling:z,setScopeId:x=Qe,insertStaticContent:R}=e,O=(c,p,v,S=null,b=null,w=null,L=void 0,E=null,P=!!p.dynamicChildren)=>{if(c===p)return;c&&!an(c,p)&&(S=Mt(c),Ae(c,b,w,!0),c=null),p.patchFlag===-2&&(P=!1,p.dynamicChildren=null);const{type:C,ref:W,shapeFlag:A}=p;switch(C){case ns:B(c,p,v,S);break;case Nt:D(c,p,v,S);break;case Vn:c==null&&j(p,v,S,L);break;case ze:Ft(c,p,v,S,b,w,L,E,P);break;default:A&1?q(c,p,v,S,b,w,L,E,P):A&6?st(c,p,v,S,b,w,L,E,P):(A&64||A&128)&&C.process(c,p,v,S,b,w,L,E,P,He)}W!=null&&b&&xs(W,c&&c.ref,w,p||c,!p)},B=(c,p,v,S)=>{if(c==null)s(p.el=r(p.children),v,S);else{const b=p.el=c.el;p.children!==c.children&&h(b,p.children)}},D=(c,p,v,S)=>{c==null?s(p.el=g(p.children||""),v,S):p.el=c.el},j=(c,p,v,S)=>{[c.el,c.anchor]=R(c.children,p,v,S,c.el,c.anchor)},Y=({el:c,anchor:p},v,S)=>{let b;for(;c&&c!==p;)b=z(c),s(c,v,S),c=b;s(p,v,S)},U=({el:c,anchor:p})=>{let v;for(;c&&c!==p;)v=z(c),i(c),c=v;i(p)},q=(c,p,v,S,b,w,L,E,P)=>{p.type==="svg"?L="svg":p.type==="math"&&(L="mathml"),c==null?pe(p,v,S,b,w,L,E,P):Me(c,p,b,w,L,E,P)},pe=(c,p,v,S,b,w,L,E)=>{let P,C;const{props:W,shapeFlag:A,transition:I,dirs:H}=c;if(P=c.el=o(c.type,w,W&&W.is,W),A&8?f(P,c.children):A&16&&ve(c.children,P,null,S,b,ms(c,w),L,E),H&&It(c,null,S,"created"),he(P,c,c.scopeId,L,S),W){for(const J in W)J!=="value"&&!gn(J)&&l(P,J,null,W[J],w,S);"value"in W&&l(P,"value",null,W.value,w),(C=W.onVnodeBeforeMount)&&Ge(C,S,c)}H&&It(c,null,S,"beforeMount");const X=Na(b,I);X&&I.beforeEnter(P),s(P,p,v),((C=W&&W.onVnodeMounted)||X||H)&&Oe(()=>{C&&Ge(C,S,c),X&&I.enter(P),H&&It(c,null,S,"mounted")},b)},he=(c,p,v,S,b)=>{if(v&&x(c,v),S)for(let w=0;w<S.length;w++)x(c,S[w]);if(b){let w=b.subTree;if(p===w||Rl(w.type)&&(w.ssContent===p||w.ssFallback===p)){const L=b.vnode;he(c,L,L.scopeId,L.slotScopeIds,b.parent)}}},ve=(c,p,v,S,b,w,L,E,P=0)=>{for(let C=P;C<c.length;C++){const W=c[C]=E?Tt(c[C]):Ze(c[C]);O(null,W,p,v,S,b,w,L,E)}},Me=(c,p,v,S,b,w,L)=>{const E=p.el=c.el;let{patchFlag:P,dynamicChildren:C,dirs:W}=p;P|=c.patchFlag&16;const A=c.props||ie,I=p.props||ie;let H;if(v&&Ut(v,!1),(H=I.onVnodeBeforeUpdate)&&Ge(H,v,p,c),W&&It(p,c,v,"beforeUpdate"),v&&Ut(v,!0),(A.innerHTML&&I.innerHTML==null||A.textContent&&I.textContent==null)&&f(E,""),C?Re(c.dynamicChildren,C,E,v,S,ms(p,b),w):L||Q(c,p,E,null,v,S,ms(p,b),w,!1),P>0){if(P&16)ke(E,A,I,v,b);else if(P&2&&A.class!==I.class&&l(E,"class",null,I.class,b),P&4&&l(E,"style",A.style,I.style,b),P&8){const X=p.dynamicProps;for(let J=0;J<X.length;J++){const G=X[J],ue=A[G],fe=I[G];(fe!==ue||G==="value")&&l(E,G,ue,fe,b,v)}}P&1&&c.children!==p.children&&f(E,p.children)}else!L&&C==null&&ke(E,A,I,v,b);((H=I.onVnodeUpdated)||W)&&Oe(()=>{H&&Ge(H,v,p,c),W&&It(p,c,v,"updated")},S)},Re=(c,p,v,S,b,w,L)=>{for(let E=0;E<p.length;E++){const P=c[E],C=p[E],W=P.el&&(P.type===ze||!an(P,C)||P.shapeFlag&70)?m(P.el):v;O(P,C,W,null,S,b,w,L,!0)}},ke=(c,p,v,S,b)=>{if(p!==v){if(p!==ie)for(const w in p)!gn(w)&&!(w in v)&&l(c,w,p[w],null,b,S);for(const w in v){if(gn(w))continue;const L=v[w],E=p[w];L!==E&&w!=="value"&&l(c,w,E,L,b,S)}"value"in v&&l(c,"value",p.value,v.value,b)}},Ft=(c,p,v,S,b,w,L,E,P)=>{const C=p.el=c?c.el:r(""),W=p.anchor=c?c.anchor:r("");let{patchFlag:A,dynamicChildren:I,slotScopeIds:H}=p;H&&(E=E?E.concat(H):H),c==null?(s(C,v,S),s(W,v,S),ve(p.children||[],v,W,b,w,L,E,P)):A>0&&A&64&&I&&c.dynamicChildren?(Re(c.dynamicChildren,I,v,b,w,L,E),(p.key!=null||b&&p===b.subTree)&&_l(c,p,!0)):Q(c,p,v,W,b,w,L,E,P)},st=(c,p,v,S,b,w,L,E,P)=>{p.slotScopeIds=E,c==null?p.shapeFlag&512?b.ctx.activate(p,v,S,L,P):it(p,v,S,b,w,L,P):kt(c,p,P)},it=(c,p,v,S,b,w,L)=>{const E=c.component=o0(c,S,b);if(pl(c)&&(E.ctx.renderer=He),a0(E,!1,L),E.asyncDep){if(b&&b.registerDep(E,me,L),!c.el){const P=E.subTree=et(Nt);D(null,P,p,v)}}else me(E,c,p,v,b,w,L)},kt=(c,p,v)=>{const S=p.component=c.component;if(qa(c,p,v))if(S.asyncDep&&!S.asyncResolved){le(S,p,v);return}else S.next=p,S.update();else p.el=c.el,S.vnode=p},me=(c,p,v,S,b,w,L)=>{const E=()=>{if(c.isMounted){let{next:A,bu:I,u:H,parent:X,vnode:J}=c;{const ye=Ll(c);if(ye){A&&(A.el=J.el,le(c,A,L)),ye.asyncDep.then(()=>{c.isUnmounted||E()});return}}let G=A,ue;Ut(c,!1),A?(A.el=J.el,le(c,A,L)):A=J,I&&On(I),(ue=A.props&&A.props.onVnodeBeforeUpdate)&&Ge(ue,X,A,J),Ut(c,!0);const fe=zs(c),Te=c.subTree;c.subTree=fe,O(Te,fe,m(Te.el),Mt(Te),c,b,w),A.el=fe.el,G===null&&Ja(c,fe.el),H&&Oe(H,b),(ue=A.props&&A.props.onVnodeUpdated)&&Oe(()=>Ge(ue,X,A,J),b)}else{let A;const{el:I,props:H}=p,{bm:X,m:J,parent:G,root:ue,type:fe}=c,Te=fn(p);if(Ut(c,!1),X&&On(X),!Te&&(A=H&&H.onVnodeBeforeMount)&&Ge(A,G,p),Ut(c,!0),I&&rt){const ye=()=>{c.subTree=zs(c),rt(I,c.subTree,c,b,null)};Te&&fe.__asyncHydrate?fe.__asyncHydrate(I,c,ye):ye()}else{ue.ce&&ue.ce._injectChildStyle(fe);const ye=c.subTree=zs(c);O(null,ye,v,S,c,b,w),p.el=ye.el}if(J&&Oe(J,b),!Te&&(A=H&&H.onVnodeMounted)){const ye=p;Oe(()=>Ge(A,G,ye),b)}(p.shapeFlag&256||G&&fn(G.vnode)&&G.vnode.shapeFlag&256)&&c.a&&Oe(c.a,b),c.isMounted=!0,p=v=S=null}};c.scope.on();const P=c.effect=new ji(E);c.scope.off();const C=c.update=P.run.bind(P),W=c.job=P.runIfDirty.bind(P);W.i=c,W.id=c.uid,P.scheduler=()=>Ks(W),Ut(c,!0),C()},le=(c,p,v)=>{p.component=c;const S=c.vnode.props;c.vnode=p,c.next=null,Ia(c,p.props,S,v),Da(c,p.children,v),_t(),ai(c),Lt()},Q=(c,p,v,S,b,w,L,E,P=!1)=>{const C=c&&c.children,W=c?c.shapeFlag:0,A=p.children,{patchFlag:I,shapeFlag:H}=p;if(I>0){if(I&128){Ve(C,A,v,S,b,w,L,E,P);return}else if(I&256){Ie(C,A,v,S,b,w,L,E,P);return}}H&8?(W&16&&je(C,b,w),A!==C&&f(v,A)):W&16?H&16?Ve(C,A,v,S,b,w,L,E,P):je(C,b,w,!0):(W&8&&f(v,""),H&16&&ve(A,v,S,b,w,L,E,P))},Ie=(c,p,v,S,b,w,L,E,P)=>{c=c||Kt,p=p||Kt;const C=c.length,W=p.length,A=Math.min(C,W);let I;for(I=0;I<A;I++){const H=p[I]=P?Tt(p[I]):Ze(p[I]);O(c[I],H,v,null,b,w,L,E,P)}C>W?je(c,b,w,!0,!1,A):ve(p,v,S,b,w,L,E,P,A)},Ve=(c,p,v,S,b,w,L,E,P)=>{let C=0;const W=p.length;let A=c.length-1,I=W-1;for(;C<=A&&C<=I;){const H=c[C],X=p[C]=P?Tt(p[C]):Ze(p[C]);if(an(H,X))O(H,X,v,null,b,w,L,E,P);else break;C++}for(;C<=A&&C<=I;){const H=c[A],X=p[I]=P?Tt(p[I]):Ze(p[I]);if(an(H,X))O(H,X,v,null,b,w,L,E,P);else break;A--,I--}if(C>A){if(C<=I){const H=I+1,X=H<W?p[H].el:S;for(;C<=I;)O(null,p[C]=P?Tt(p[C]):Ze(p[C]),v,X,b,w,L,E,P),C++}}else if(C>I)for(;C<=A;)Ae(c[C],b,w,!0),C++;else{const H=C,X=C,J=new Map;for(C=X;C<=I;C++){const Ce=p[C]=P?Tt(p[C]):Ze(p[C]);Ce.key!=null&&J.set(Ce.key,C)}let G,ue=0;const fe=I-X+1;let Te=!1,ye=0;const ut=new Array(fe);for(C=0;C<fe;C++)ut[C]=0;for(C=H;C<=A;C++){const Ce=c[C];if(ue>=fe){Ae(Ce,b,w,!0);continue}let ge;if(Ce.key!=null)ge=J.get(Ce.key);else for(G=X;G<=I;G++)if(ut[G-X]===0&&an(Ce,p[G])){ge=G;break}ge===void 0?Ae(Ce,b,w,!0):(ut[ge-X]=C+1,ge>=ye?ye=ge:Te=!0,O(Ce,p[ge],v,null,b,w,L,E,P),ue++)}const gt=Te?$a(ut):Kt;for(G=gt.length-1,C=fe-1;C>=0;C--){const Ce=X+C,ge=p[Ce],Pn=Ce+1<W?p[Ce+1].el:S;ut[C]===0?O(null,ge,v,Pn,b,w,L,E,P):Te&&(G<0||C!==gt[G]?Ue(ge,v,Pn,2):G--)}}},Ue=(c,p,v,S,b=null)=>{const{el:w,type:L,transition:E,children:P,shapeFlag:C}=c;if(C&6){Ue(c.component.subTree,p,v,S);return}if(C&128){c.suspense.move(p,v,S);return}if(C&64){L.move(c,p,v,He);return}if(L===ze){s(w,p,v);for(let A=0;A<P.length;A++)Ue(P[A],p,v,S);s(c.anchor,p,v);return}if(L===Vn){Y(c,p,v);return}if(S!==2&&C&1&&E)if(S===0)E.beforeEnter(w),s(w,p,v),Oe(()=>E.enter(w),b);else{const{leave:A,delayLeave:I,afterLeave:H}=E,X=()=>s(w,p,v),J=()=>{A(w,()=>{X(),H&&H()})};I?I(w,X,J):J()}else s(w,p,v)},Ae=(c,p,v,S=!1,b=!1)=>{const{type:w,props:L,ref:E,children:P,dynamicChildren:C,shapeFlag:W,patchFlag:A,dirs:I,cacheIndex:H}=c;if(A===-2&&(b=!1),E!=null&&xs(E,null,v,c,!0),H!=null&&(p.renderCache[H]=void 0),W&256){p.ctx.deactivate(c);return}const X=W&1&&I,J=!fn(c);let G;if(J&&(G=L&&L.onVnodeBeforeUnmount)&&Ge(G,p,c),W&6)ls(c.component,v,S);else{if(W&128){c.suspense.unmount(v,S);return}X&&It(c,null,p,"beforeUnmount"),W&64?c.type.remove(c,p,v,He,S):C&&!C.hasOnce&&(w!==ze||A>0&&A&64)?je(C,p,v,!1,!0):(w===ze&&A&384||!b&&W&16)&&je(P,p,v),S&&lt(c)}(J&&(G=L&&L.onVnodeUnmounted)||X)&&Oe(()=>{G&&Ge(G,p,c),X&&It(c,null,p,"unmounted")},v)},lt=c=>{const{type:p,el:v,anchor:S,transition:b}=c;if(p===ze){At(v,S);return}if(p===Vn){U(c);return}const w=()=>{i(v),b&&!b.persisted&&b.afterLeave&&b.afterLeave()};if(c.shapeFlag&1&&b&&!b.persisted){const{leave:L,delayLeave:E}=b,P=()=>L(v,w);E?E(c.el,w,P):P()}else w()},At=(c,p)=>{let v;for(;c!==p;)v=z(c),i(c),c=v;i(p)},ls=(c,p,v)=>{const{bum:S,scope:b,job:w,subTree:L,um:E,m:P,a:C}=c;di(P),di(C),S&&On(S),b.stop(),w&&(w.flags|=8,Ae(L,c,p,v)),E&&Oe(E,p),Oe(()=>{c.isUnmounted=!0},p),p&&p.pendingBranch&&!p.isUnmounted&&c.asyncDep&&!c.asyncResolved&&c.suspenseId===p.pendingId&&(p.deps--,p.deps===0&&p.resolve())},je=(c,p,v,S=!1,b=!1,w=0)=>{for(let L=w;L<c.length;L++)Ae(c[L],p,v,S,b)},Mt=c=>{if(c.shapeFlag&6)return Mt(c.component.subTree);if(c.shapeFlag&128)return c.suspense.next();const p=z(c.anchor||c.el),v=p&&p[ga];return v?z(v):p};let ot=!1;const bt=(c,p,v)=>{c==null?p._vnode&&Ae(p._vnode,null,null,!0):O(p._vnode||null,c,p,null,null,null,v),p._vnode=c,ot||(ot=!0,ai(),ul(),ot=!1)},He={p:O,um:Ae,m:Ue,r:lt,mt:it,mc:ve,pc:Q,pbc:Re,n:Mt,o:e};let at,rt;return{render:bt,hydrate:at,createApp:Aa(bt,at)}}function ms({type:e,props:t},n){return n==="svg"&&e==="foreignObject"||n==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:n}function Ut({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Na(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function _l(e,t,n=!1){const s=e.children,i=t.children;if($(s)&&$(i))for(let l=0;l<s.length;l++){const o=s[l];let r=i[l];r.shapeFlag&1&&!r.dynamicChildren&&((r.patchFlag<=0||r.patchFlag===32)&&(r=i[l]=Tt(i[l]),r.el=o.el),!n&&r.patchFlag!==-2&&_l(o,r)),r.type===ns&&(r.el=o.el)}}function $a(e){const t=e.slice(),n=[0];let s,i,l,o,r;const g=e.length;for(s=0;s<g;s++){const h=e[s];if(h!==0){if(i=n[n.length-1],e[i]<h){t[s]=i,n.push(s);continue}for(l=0,o=n.length-1;l<o;)r=l+o>>1,e[n[r]]<h?l=r+1:o=r;h<e[n[l]]&&(l>0&&(t[s]=n[l-1]),n[l]=s)}}for(l=n.length,o=n[l-1];l-- >0;)n[l]=o,o=t[o];return n}function Ll(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Ll(t)}function di(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const ka=Symbol.for("v-scx"),ja=()=>Dn(ka);function pn(e,t,n){return Fl(e,t,n)}function Fl(e,t,n=ie){const{immediate:s,deep:i,flush:l,once:o}=n,r=be({},n);let g;if(ss)if(l==="sync"){const z=ja();g=z.__watcherHandles||(z.__watcherHandles=[])}else if(!t||s)r.once=!0;else{const z=()=>{};return z.stop=Qe,z.resume=Qe,z.pause=Qe,z}const h=Fe;r.call=(z,x,R)=>nt(z,h,x,R);let f=!1;l==="post"?r.scheduler=z=>{Oe(z,h&&h.suspense)}:l!=="sync"&&(f=!0,r.scheduler=(z,x)=>{x?z():Ks(z)}),r.augmentJob=z=>{t&&(z.flags|=4),f&&(z.flags|=2,h&&(z.id=h.uid,z.i=h))};const m=la(e,t,r);return g&&g.push(m),m}function Ya(e,t,n){const s=this.proxy,i=de(e)?e.includes(".")?Al(s,e):()=>s[e]:e.bind(s,s);let l;K(t)?l=t:(l=t.handler,n=t);const o=Tn(this),r=Fl(i,l.bind(s),n);return o(),r}function Al(e,t){const n=t.split(".");return()=>{let s=e;for(let i=0;i<n.length&&s;i++)s=s[n[i]];return s}}const Ba=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${Vt(t)}Modifiers`]||e[`${$t(t)}Modifiers`];function Ka(e,t,...n){if(e.isUnmounted)return;const s=e.vnode.props||ie;let i=n;const l=t.startsWith("update:"),o=l&&Ba(s,t.slice(7));o&&(o.trim&&(i=n.map(f=>de(f)?f.trim():f)),o.number&&(i=n.map($n)));let r,g=s[r=gs(t)]||s[r=gs(Vt(t))];!g&&l&&(g=s[r=gs($t(t))]),g&&nt(g,e,6,i);const h=s[r+"Once"];if(h){if(!e.emitted)e.emitted={};else if(e.emitted[r])return;e.emitted[r]=!0,nt(h,e,6,i)}}function Ml(e,t,n=!1){const s=t.emitsCache,i=s.get(e);if(i!==void 0)return i;const l=e.emits;let o={},r=!1;if(!K(e)){const g=h=>{const f=Ml(h,t,!0);f&&(r=!0,be(o,f))};!n&&t.mixins.length&&t.mixins.forEach(g),e.extends&&g(e.extends),e.mixins&&e.mixins.forEach(g)}return!l&&!r?(ae(e)&&s.set(e,null),null):($(l)?l.forEach(g=>o[g]=null):be(o,l),ae(e)&&s.set(e,o),o)}function ts(e,t){return!e||!Gn(t)?!1:(t=t.slice(2).replace(/Once$/,""),ee(e,t[0].toLowerCase()+t.slice(1))||ee(e,$t(t))||ee(e,t))}function zs(e){const{type:t,vnode:n,proxy:s,withProxy:i,propsOptions:[l],slots:o,attrs:r,emit:g,render:h,renderCache:f,props:m,data:z,setupState:x,ctx:R,inheritAttrs:O}=e,B=Yn(e);let D,j;try{if(n.shapeFlag&4){const U=i||s,q=U;D=Ze(h.call(q,U,f,m,x,z,R)),j=r}else{const U=t;D=Ze(U.length>1?U(m,{attrs:r,slots:o,emit:g}):U(m,null)),j=t.props?r:Xa(r)}}catch(U){hn.length=0,Qn(U,e,1),D=et(Nt)}let Y=D;if(j&&O!==!1){const U=Object.keys(j),{shapeFlag:q}=Y;U.length&&q&7&&(l&&U.some(Ms)&&(j=Ga(j,l)),Y=Qt(Y,j,!1,!0))}return n.dirs&&(Y=Qt(Y,null,!1,!0),Y.dirs=Y.dirs?Y.dirs.concat(n.dirs):n.dirs),n.transition&&Xs(Y,n.transition),D=Y,Yn(B),D}const Xa=e=>{let t;for(const n in e)(n==="class"||n==="style"||Gn(n))&&((t||(t={}))[n]=e[n]);return t},Ga=(e,t)=>{const n={};for(const s in e)(!Ms(s)||!(s.slice(9)in t))&&(n[s]=e[s]);return n};function qa(e,t,n){const{props:s,children:i,component:l}=e,{props:o,children:r,patchFlag:g}=t,h=l.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&g>=0){if(g&1024)return!0;if(g&16)return s?pi(s,o,h):!!o;if(g&8){const f=t.dynamicProps;for(let m=0;m<f.length;m++){const z=f[m];if(o[z]!==s[z]&&!ts(h,z))return!0}}}else return(i||r)&&(!r||!r.$stable)?!0:s===o?!1:s?o?pi(s,o,h):!0:!!o;return!1}function pi(e,t,n){const s=Object.keys(t);if(s.length!==Object.keys(e).length)return!0;for(let i=0;i<s.length;i++){const l=s[i];if(t[l]!==e[l]&&!ts(n,l))return!0}return!1}function Ja({vnode:e,parent:t},n){for(;t;){const s=t.subTree;if(s.suspense&&s.suspense.activeBranch===e&&(s.el=e.el),s===e)(e=t.vnode).el=n,t=t.parent;else break}}const Rl=e=>e.__isSuspense;function Za(e,t){t&&t.pendingBranch?$(e)?t.effects.push(...e):t.effects.push(e):ra(e)}const ze=Symbol.for("v-fgt"),ns=Symbol.for("v-txt"),Nt=Symbol.for("v-cmt"),Vn=Symbol.for("v-stc"),hn=[];let De=null;function Z(e=!1){hn.push(De=e?null:[])}function Qa(){hn.pop(),De=hn[hn.length-1]||null}let bn=1;function hi(e){bn+=e,e<0&&De&&(De.hasOnce=!0)}function Il(e){return e.dynamicChildren=bn>0?De||Kt:null,Qa(),bn>0&&De&&De.push(e),e}function ne(e,t,n,s,i,l){return Il(u(e,t,n,s,i,l,!0))}function Ul(e,t,n,s,i){return Il(et(e,t,n,s,i,!0))}function e0(e){return e?e.__v_isVNode===!0:!1}function an(e,t){return e.type===t.type&&e.key===t.key}const Ol=({key:e})=>e??null,Hn=({ref:e,ref_key:t,ref_for:n})=>(typeof e=="number"&&(e=""+e),e!=null?de(e)||Se(e)||K(e)?{i:Ne,r:e,k:t,f:!!n}:e:null);function u(e,t=null,n=null,s=0,i=null,l=e===ze?0:1,o=!1,r=!1){const g={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Ol(t),ref:t&&Hn(t),scopeId:cl,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:l,patchFlag:s,dynamicProps:i,dynamicChildren:null,appContext:null,ctx:Ne};return r?(Js(g,n),l&128&&e.normalize(g)):n&&(g.shapeFlag|=de(n)?8:16),bn>0&&!o&&De&&(g.patchFlag>0||l&6)&&g.patchFlag!==32&&De.push(g),g}const et=t0;function t0(e,t=null,n=null,s=0,i=null,l=!1){if((!e||e===Sa)&&(e=Nt),e0(e)){const r=Qt(e,t,!0);return n&&Js(r,n),bn>0&&!l&&De&&(r.shapeFlag&6?De[De.indexOf(e)]=r:De.push(r)),r.patchFlag=-2,r}if(c0(e)&&(e=e.__vccOpts),t){t=n0(t);let{class:r,style:g}=t;r&&!de(r)&&(t.class=_e(r)),ae(g)&&(js(g)&&!$(g)&&(g=be({},g)),t.style=Gt(g))}const o=de(e)?1:Rl(e)?128:ca(e)?64:ae(e)?4:K(e)?2:0;return u(e,t,n,s,i,o,l,!0)}function n0(e){return e?js(e)||Cl(e)?be({},e):e:null}function Qt(e,t,n=!1,s=!1){const{props:i,ref:l,patchFlag:o,children:r,transition:g}=e,h=t?s0(i||{},t):i,f={__v_isVNode:!0,__v_skip:!0,type:e.type,props:h,key:h&&Ol(h),ref:t&&t.ref?n&&l?$(l)?l.concat(Hn(t)):[l,Hn(t)]:Hn(t):l,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:r,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==ze?o===-1?16:o|16:o,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:g,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Qt(e.ssContent),ssFallback:e.ssFallback&&Qt(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return g&&s&&Xs(f,g.clone(f)),f}function M(e=" ",t=0){return et(ns,null,e,t)}function Un(e,t){const n=et(Vn,null,e);return n.staticCount=t,n}function Be(e="",t=!1){return t?(Z(),Ul(Nt,null,e)):et(Nt,null,e)}function Ze(e){return e==null||typeof e=="boolean"?et(Nt):$(e)?et(ze,null,e.slice()):typeof e=="object"?Tt(e):et(ns,null,String(e))}function Tt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Qt(e)}function Js(e,t){let n=0;const{shapeFlag:s}=e;if(t==null)t=null;else if($(t))n=16;else if(typeof t=="object")if(s&65){const i=t.default;i&&(i._c&&(i._d=!1),Js(e,i()),i._c&&(i._d=!0));return}else{n=32;const i=t._;!i&&!Cl(t)?t._ctx=Ne:i===3&&Ne&&(Ne.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else K(t)?(t={default:t,_ctx:Ne},n=32):(t=String(t),s&64?(n=16,t=[M(t)]):n=8);e.children=t,e.shapeFlag|=n}function s0(...e){const t={};for(let n=0;n<e.length;n++){const s=e[n];for(const i in s)if(i==="class")t.class!==s.class&&(t.class=_e([t.class,s.class]));else if(i==="style")t.style=Gt([t.style,s.style]);else if(Gn(i)){const l=t[i],o=s[i];o&&l!==o&&!($(l)&&l.includes(o))&&(t[i]=l?[].concat(l,o):o)}else i!==""&&(t[i]=s[i])}return t}function Ge(e,t,n,s=null){nt(e,t,7,[n,s])}const i0=vl();let l0=0;function o0(e,t,n){const s=e.type,i=(t?t.appContext:e.appContext)||i0,l={uid:l0++,vnode:e,type:s,parent:t,appContext:i,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Eo(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(i.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Sl(s,i),emitsOptions:Ml(s,i),emit:null,emitted:null,propsDefaults:ie,inheritAttrs:s.inheritAttrs,ctx:ie,data:ie,props:ie,attrs:ie,slots:ie,refs:ie,setupState:ie,setupContext:null,suspense:n,suspenseId:n?n.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return l.ctx={_:l},l.root=t?t.root:l,l.emit=Ka.bind(null,l),e.ce&&e.ce(l),l}let Fe=null,Kn,Ls;{const e=Hi(),t=(n,s)=>{let i;return(i=e[n])||(i=e[n]=[]),i.push(s),l=>{i.length>1?i.forEach(o=>o(l)):i[0](l)}};Kn=t("__VUE_INSTANCE_SETTERS__",n=>Fe=n),Ls=t("__VUE_SSR_SETTERS__",n=>ss=n)}const Tn=e=>{const t=Fe;return Kn(e),e.scope.on(),()=>{e.scope.off(),Kn(t)}},mi=()=>{Fe&&Fe.scope.off(),Kn(null)};function Wl(e){return e.vnode.shapeFlag&4}let ss=!1;function a0(e,t=!1,n=!1){t&&Ls(t);const{props:s,children:i}=e.vnode,l=Wl(e);Ra(e,s,l,t),Wa(e,i,n);const o=l?r0(e,t):void 0;return t&&Ls(!1),o}function r0(e,t){const n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,xa);const{setup:s}=n;if(s){const i=e.setupContext=s.length>1?g0(e):null,l=Tn(e);_t();const o=xn(s,e,0,[e.props,i]);if(Lt(),l(),Ui(o)){if(fn(e)||dl(e),o.then(mi,mi),t)return o.then(r=>{zi(e,r,t)}).catch(r=>{Qn(r,e,0)});e.asyncDep=o}else zi(e,o,t)}else Dl(e,t)}function zi(e,t,n){K(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:ae(t)&&(e.setupState=ll(t)),Dl(e,n)}let vi;function Dl(e,t,n){const s=e.type;if(!e.render){if(!t&&vi&&!s.render){const i=s.template||Gs(e).template;if(i){const{isCustomElement:l,compilerOptions:o}=e.appContext.config,{delimiters:r,compilerOptions:g}=s,h=be(be({isCustomElement:l,delimiters:r},o),g);s.render=vi(i,h)}}e.render=s.render||Qe}{const i=Tn(e);_t();try{Ta(e)}finally{Lt(),i()}}}const u0={get(e,t){return xe(e,"get",""),e[t]}};function g0(e){const t=n=>{e.exposed=n||{}};return{attrs:new Proxy(e.attrs,u0),slots:e.slots,emit:e.emit,expose:t}}function is(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(ll(Jo(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in dn)return dn[n](e)},has(t,n){return n in t||n in dn}})):e.proxy}function c0(e){return K(e)&&"__vccOpts"in e}const f0=(e,t)=>sa(e,t,ss),d0="3.5.8";/**
* @vue/runtime-dom v3.5.8
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Fs;const yi=typeof window<"u"&&window.trustedTypes;if(yi)try{Fs=yi.createPolicy("vue",{createHTML:e=>e})}catch{}const Vl=Fs?e=>Fs.createHTML(e):e=>e,p0="http://www.w3.org/2000/svg",h0="http://www.w3.org/1998/Math/MathML",ht=typeof document<"u"?document:null,bi=ht&&ht.createElement("template"),m0={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,s)=>{const i=t==="svg"?ht.createElementNS(p0,e):t==="mathml"?ht.createElementNS(h0,e):n?ht.createElement(e,{is:n}):ht.createElement(e);return e==="select"&&s&&s.multiple!=null&&i.setAttribute("multiple",s.multiple),i},createText:e=>ht.createTextNode(e),createComment:e=>ht.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>ht.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,n,s,i,l){const o=n?n.previousSibling:t.lastChild;if(i&&(i===l||i.nextSibling))for(;t.insertBefore(i.cloneNode(!0),n),!(i===l||!(i=i.nextSibling)););else{bi.innerHTML=Vl(s==="svg"?`<svg>${e}</svg>`:s==="mathml"?`<math>${e}</math>`:e);const r=bi.content;if(s==="svg"||s==="mathml"){const g=r.firstChild;for(;g.firstChild;)r.appendChild(g.firstChild);r.removeChild(g)}t.insertBefore(r,n)}return[o?o.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},z0=Symbol("_vtc");function v0(e,t,n){const s=e[z0];s&&(t=(t?[t,...s]:[...s]).join(" ")),t==null?e.removeAttribute("class"):n?e.setAttribute("class",t):e.className=t}const Xn=Symbol("_vod"),Hl=Symbol("_vsh"),Ke={beforeMount(e,{value:t},{transition:n}){e[Xn]=e.style.display==="none"?"":e.style.display,n&&t?n.beforeEnter(e):rn(e,t)},mounted(e,{value:t},{transition:n}){n&&t&&n.enter(e)},updated(e,{value:t,oldValue:n},{transition:s}){!t!=!n&&(s?t?(s.beforeEnter(e),rn(e,!0),s.enter(e)):s.leave(e,()=>{rn(e,!1)}):rn(e,t))},beforeUnmount(e,{value:t}){rn(e,t)}};function rn(e,t){e.style.display=t?e[Xn]:"none",e[Hl]=!t}const y0=Symbol(""),b0=/(^|;)\s*display\s*:/;function C0(e,t,n){const s=e.style,i=de(n);let l=!1;if(n&&!i){if(t)if(de(t))for(const o of t.split(";")){const r=o.slice(0,o.indexOf(":")).trim();n[r]==null&&Nn(s,r,"")}else for(const o in t)n[o]==null&&Nn(s,o,"");for(const o in n)o==="display"&&(l=!0),Nn(s,o,n[o])}else if(i){if(t!==n){const o=s[y0];o&&(n+=";"+o),s.cssText=n,l=b0.test(n)}}else t&&e.removeAttribute("style");Xn in e&&(e[Xn]=l?s.display:"",e[Hl]&&(s.display="none"))}const Ci=/\s*!important$/;function Nn(e,t,n){if($(n))n.forEach(s=>Nn(e,t,s));else if(n==null&&(n=""),t.startsWith("--"))e.setProperty(t,n);else{const s=w0(e,t);Ci.test(n)?e.setProperty($t(s),n.replace(Ci,""),"important"):e[s]=n}}const wi=["Webkit","Moz","ms"],vs={};function w0(e,t){const n=vs[t];if(n)return n;let s=Vt(t);if(s!=="filter"&&s in e)return vs[t]=s;s=Di(s);for(let i=0;i<wi.length;i++){const l=wi[i]+s;if(l in e)return vs[t]=l}return t}const Si="http://www.w3.org/1999/xlink";function xi(e,t,n,s,i,l=To(t)){s&&t.startsWith("xlink:")?n==null?e.removeAttributeNS(Si,t.slice(6,t.length)):e.setAttributeNS(Si,t,n):n==null||l&&!Ni(n)?e.removeAttribute(t):e.setAttribute(t,l?"":tt(n)?String(n):n)}function S0(e,t,n,s){if(t==="innerHTML"||t==="textContent"){n!=null&&(e[t]=t==="innerHTML"?Vl(n):n);return}const i=e.tagName;if(t==="value"&&i!=="PROGRESS"&&!i.includes("-")){const o=i==="OPTION"?e.getAttribute("value")||"":e.value,r=n==null?e.type==="checkbox"?"on":"":String(n);(o!==r||!("_value"in e))&&(e.value=r),n==null&&e.removeAttribute(t),e._value=n;return}let l=!1;if(n===""||n==null){const o=typeof e[t];o==="boolean"?n=Ni(n):n==null&&o==="string"?(n="",l=!0):o==="number"&&(n=0,l=!0)}try{e[t]=n}catch{}l&&e.removeAttribute(t)}function Pt(e,t,n,s){e.addEventListener(t,n,s)}function x0(e,t,n,s){e.removeEventListener(t,n,s)}const Ti=Symbol("_vei");function T0(e,t,n,s,i=null){const l=e[Ti]||(e[Ti]={}),o=l[t];if(s&&o)o.value=s;else{const[r,g]=P0(t);if(s){const h=l[t]=L0(s,i);Pt(e,r,h,g)}else o&&(x0(e,r,o,g),l[t]=void 0)}}const Pi=/(?:Once|Passive|Capture)$/;function P0(e){let t;if(Pi.test(e)){t={};let s;for(;s=e.match(Pi);)e=e.slice(0,e.length-s[0].length),t[s[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):$t(e.slice(2)),t]}let ys=0;const E0=Promise.resolve(),_0=()=>ys||(E0.then(()=>ys=0),ys=Date.now());function L0(e,t){const n=s=>{if(!s._vts)s._vts=Date.now();else if(s._vts<=n.attached)return;nt(F0(s,n.value),t,5,[s])};return n.value=e,n.attached=_0(),n}function F0(e,t){if($(t)){const n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(s=>i=>!i._stopped&&s&&s(i))}else return t}const Ei=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,A0=(e,t,n,s,i,l)=>{const o=i==="svg";t==="class"?v0(e,s,o):t==="style"?C0(e,n,s):Gn(t)?Ms(t)||T0(e,t,n,s,l):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):M0(e,t,s,o))?(S0(e,t,s),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&xi(e,t,s,o,l,t!=="value")):(t==="true-value"?e._trueValue=s:t==="false-value"&&(e._falseValue=s),xi(e,t,s,o))};function M0(e,t,n,s){if(s)return!!(t==="innerHTML"||t==="textContent"||t in e&&Ei(t)&&K(n));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const i=e.tagName;if(i==="IMG"||i==="VIDEO"||i==="CANVAS"||i==="SOURCE")return!1}return Ei(t)&&de(n)?!1:!!(t in e||e._isVueCE&&(/[A-Z]/.test(t)||!de(n)))}const en=e=>{const t=e.props["onUpdate:modelValue"]||!1;return $(t)?n=>On(t,n):t};function R0(e){e.target.composing=!0}function _i(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const vt=Symbol("_assign"),k={created(e,{modifiers:{lazy:t,trim:n,number:s}},i){e[vt]=en(i);const l=s||i.props&&i.props.type==="number";Pt(e,t?"change":"input",o=>{if(o.target.composing)return;let r=e.value;n&&(r=r.trim()),l&&(r=$n(r)),e[vt](r)}),n&&Pt(e,"change",()=>{e.value=e.value.trim()}),t||(Pt(e,"compositionstart",R0),Pt(e,"compositionend",_i),Pt(e,"change",_i))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:n,modifiers:{lazy:s,trim:i,number:l}},o){if(e[vt]=en(o),e.composing)return;const r=(l||e.type==="number")&&!/^0\d/.test(e.value)?$n(e.value):e.value,g=t??"";r!==g&&(document.activeElement===e&&e.type!=="range"&&(s&&t===n||i&&e.value.trim()===g)||(e.value=g))}},qe={deep:!0,created(e,t,n){e[vt]=en(n),Pt(e,"change",()=>{const s=e._modelValue,i=Cn(e),l=e.checked,o=e[vt];if($(s)){const r=Us(s,i),g=r!==-1;if(l&&!g)o(s.concat(i));else if(!l&&g){const h=[...s];h.splice(r,1),o(h)}}else if(tn(s)){const r=new Set(s);l?r.add(i):r.delete(i),o(r)}else o(Nl(e,l))})},mounted:Li,beforeUpdate(e,t,n){e[vt]=en(n),Li(e,t,n)}};function Li(e,{value:t,oldValue:n},s){e._modelValue=t;let i;$(t)?i=Us(t,s.props.value)>-1:tn(t)?i=t.has(s.props.value):i=Sn(t,Nl(e,!0)),e.checked!==i&&(e.checked=i)}const St={deep:!0,created(e,{value:t,modifiers:{number:n}},s){const i=tn(t);Pt(e,"change",()=>{const l=Array.prototype.filter.call(e.options,o=>o.selected).map(o=>n?$n(Cn(o)):Cn(o));e[vt](e.multiple?i?new Set(l):l:l[0]),e._assigning=!0,al(()=>{e._assigning=!1})}),e[vt]=en(s)},mounted(e,{value:t,modifiers:{number:n}}){Fi(e,t)},beforeUpdate(e,t,n){e[vt]=en(n)},updated(e,{value:t,modifiers:{number:n}}){e._assigning||Fi(e,t)}};function Fi(e,t,n){const s=e.multiple,i=$(t);if(!(s&&!i&&!tn(t))){for(let l=0,o=e.options.length;l<o;l++){const r=e.options[l],g=Cn(r);if(s)if(i){const h=typeof g;h==="string"||h==="number"?r.selected=t.some(f=>String(f)===String(g)):r.selected=Us(t,g)>-1}else r.selected=t.has(g);else if(Sn(Cn(r),t)){e.selectedIndex!==l&&(e.selectedIndex=l);return}}!s&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function Cn(e){return"_value"in e?e._value:e.value}function Nl(e,t){const n=t?"_trueValue":"_falseValue";return n in e?e[n]:t}const I0=be({patchProp:A0},m0);let Ai;function U0(){return Ai||(Ai=Va(I0))}const O0=(...e)=>{const t=U0().createApp(...e),{mount:n}=t;return t.mount=s=>{const i=D0(s);if(!i)return;const l=t._component;!K(l)&&!l.render&&!l.template&&(l.template=i.innerHTML),i.nodeType===1&&(i.textContent="");const o=n(i,!1,W0(i));return i instanceof Element&&(i.removeAttribute("v-cloak"),i.setAttribute("data-v-app","")),o},t};function W0(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function D0(e){return de(e)?document.querySelector(e):e}function V0(e,t,n,s,i,l,o){e.beginPath(),e.ellipse(t,n,s,i,0,0,Math.PI*2),e.strokeStyle=o,e.lineWidth=l,e.stroke()}class H0{constructor(t){V(this,"mmToPixel",10);this.mmToPixel=t}drawCircleList(t,n,s,i,l){n.forEach(o=>{o.drawInnerCircle&&this.drawCircle(t,s,i,l,o)})}drawCircle(t,n,s,i,l){const o=(l.innerCircleLineRadiusX-l.innerCircleLineWidth)/2,r=(l.innerCircleLineRadiusY-l.innerCircleLineWidth)/2;this.drawEllipse(t,n,s,o*this.mmToPixel,r*this.mmToPixel,l.innerCircleLineWidth*this.mmToPixel,i)}drawEllipse(t,n,s,i,l,o,r){t.beginPath(),t.ellipse(n,s,i,l,0,0,Math.PI*2),t.strokeStyle=r,t.lineWidth=o,t.stroke()}}class N0{constructor(t){V(this,"mmToPixel",10);this.mmToPixel=t}drawCompanyList(t,n,s,i,l,o,r){n.forEach(g=>{this.drawCompanyName(t,g,s,i,l,o,g.color||r)})}drawCompanyName(t,n,s,i,l,o,r){const g=n.fontHeight*this.mmToPixel,h=n.fontWeight||"normal";t.save(),t.font=`${h} ${g}px ${n.fontFamily}`,t.fillStyle=r,t.textAlign="center",t.textBaseline="bottom";const f=n.companyName.split(""),m=f.length,z=n.borderOffset*this.mmToPixel,x=Math.PI*(.5+m/(n.textDistributionFactor*4)),R=x/m,O=n.rotateDirection==="clockwise"?-1:1,B=(n.startAngle?n.startAngle:0)+(n.rotateDirection==="clockwise"?Math.PI-x/2:Math.PI+(Math.PI-x)/2);if(n.adjustEllipseText){const D=(m+1)/2;f.forEach((j,Y)=>{const U=D-Y-1,pe=Math.pow(U/D,2)*R*n.adjustEllipseTextFactor,he=Y-D,ve=he/Math.abs(he);let Me=B+O*R*(Y+.5);Me+=pe*ve;const Re=s+Math.cos(Me)*(l-g-z),ke=i+Math.sin(Me)*(o-g-z);t.save(),t.translate(Re,ke),t.rotate(Me+(n.rotateDirection==="clockwise"?-Math.PI/2:Math.PI/2)),t.scale(n.compression,1),t.fillText(j,0,0),t.restore()})}else f.forEach((D,j)=>{const Y=B+O*R*(j+.5),U=s+Math.cos(Y)*(l-g-z),q=i+Math.sin(Y)*(o-g-z);t.save(),t.translate(U,q),t.rotate(Y+(n.rotateDirection==="clockwise"?-Math.PI/2:Math.PI/2)),t.scale(n.compression,1),t.fillText(D,0,0),t.restore()});t.restore()}}class $0{constructor(t){V(this,"mmToPixel",10);V(this,"drawPositionCrossLines",(t,n,s,i,l,o,r)=>{const g=t;if(!g)return;const h=g.getContext("2d");if(h&&(h.clearRect(0,0,g.width,g.height),h.beginPath(),h.strokeStyle=r,h.lineWidth=1,h.moveTo(s,o),h.lineTo(g.width,o),h.moveTo(l,i),h.lineTo(l,g.height),h.stroke(),n)){const f=n.getContext("2d");f&&f.drawImage(g,0,0)}});V(this,"drawCurrentPositionText",(t,n,s,i,l,o)=>{t.fillStyle="black",t.font="bold 12px Arial",t.textAlign="left",t.textBaseline="top";const r=n/i,g=s/i;t.fillText(`${r.toFixed(1)}mm, ${g.toFixed(1)}mm, scale: ${i.toFixed(2)}`,l+5,o+5)});this.mmToPixel=t}drawRuler(t,n,s,i,l,o){if(!n.showRuler)return;const r=1/this.mmToPixel;t.save(),t.fillStyle="lightgray",o?t.fillRect(0,0,i,l):t.fillRect(0,0,l,i),t.fillStyle="black",t.font="10px Arial",t.textAlign="center",t.textBaseline="top";const g=this.mmToPixel,h=Math.ceil((i-l)*r/s);for(let f=0;f<=h;f++){const m=f*g*s+l;f%5===0?(t.beginPath(),o?(t.moveTo(m,0),t.lineTo(m,l*.8)):(t.moveTo(0,m),t.lineTo(l*.8,m)),t.lineWidth=1,t.stroke(),t.save(),o?t.fillText(f.toString(),m,l*.8):(t.translate(l*.8,m),t.rotate(-Math.PI/2),t.fillText(f.toString(),0,0)),t.restore()):(t.beginPath(),o?(t.moveTo(m,0),t.lineTo(m,l*.6)):(t.moveTo(0,m),t.lineTo(l*.6,m)),t.lineWidth=.5,t.stroke())}t.restore()}showCrossDashLine(t,n,s,i,l,o,r){if(!n.showDashLine)return;t.save(),t.strokeStyle="#bbbbbb",t.lineWidth=1,t.setLineDash([5,5]);const g=this.mmToPixel*5;for(let h=i;h<o;h+=g*s)t.beginPath(),t.moveTo(h,l),t.lineTo(h,r),t.stroke();for(let h=l;h<r;h+=g*s)t.beginPath(),t.moveTo(i,h),t.lineTo(o,h),t.stroke();t.restore()}}class k0{constructor(t){V(this,"mmToPixel",10);this.mmToPixel=t}drawSecurityPattern(t,n,s,i,l,o,r){t.save(),t.strokeStyle="#FFFFFF",t.lineWidth=n.securityPatternWidth*this.mmToPixel,t.globalCompositeOperation="destination-out";const g=n.securityPatternAngleRange*Math.PI/180;if(r||n.securityPatternParams.length===0){n.securityPatternParams=[];for(let h=0;h<n.securityPatternCount;h++){const f=Math.random()*Math.PI*2,z=Math.atan2(o*Math.cos(f),l*Math.sin(f))+(Math.random()-.5)*g;n.securityPatternParams.push({angle:f,lineAngle:z})}}n.securityPatternParams.forEach(({angle:h,lineAngle:f})=>{const m=s+l*Math.cos(h),z=i+o*Math.sin(h),x=n.securityPatternLength*this.mmToPixel,R=m-x/2*Math.cos(f),O=z-x/2*Math.sin(f),B=m+x/2*Math.cos(f),D=z+x/2*Math.sin(f);t.beginPath(),t.moveTo(R,O),t.lineTo(B,D),t.stroke()}),t.restore()}}class Mi{constructor(t){V(this,"mmToPixel",10);this.mmToPixel=t}async drawSVGContent(t,n,s,i,l=1){try{const o=10*this.mmToPixel,r=document.createElement("div");r.innerHTML=n;const g=r.querySelector("svg");if(!g)throw new Error("Invalid SVG content");g.hasAttribute("width")||g.setAttribute("width","100"),g.hasAttribute("height")||g.setAttribute("height","100");const h=parseFloat(g.getAttribute("width")||"100"),f=parseFloat(g.getAttribute("height")||"100"),m=new XMLSerializer().serializeToString(g),x=`data:image/svg+xml;base64,${btoa(unescape(encodeURIComponent(m)))}`,R=new Image;R.src=x,await new Promise((O,B)=>{R.onload=O,R.onerror=B}),t.save(),t.translate(s,i),t.scale(1,1),t.strokeStyle="blue",t.lineWidth=1,t.strokeRect(10,10,o,o),t.restore(),console.log("draw svg base64Url")}catch(o){console.error("Error drawing SVG:",o)}}async loadAndDrawSVG(t,n,s,i,l=1){try{const o=10*this.mmToPixel;console.log("draw test svg content",n,s,i,l),t.save(),t.translate(s,i),t.scale(1,1),t.strokeStyle="blue",t.lineWidth=1,t.strokeRect(10,10,o,o),t.restore()}catch(o){console.error("Error loading SVG:",o)}}async drawStarShape(t,n,s,i,l){try{if(console.log("draw star svg content",n.svgPath),n.svgPath.startsWith("<svg"))await this.drawSVGContent(t,n.svgPath,s,i+n.starPositionY*this.mmToPixel,n.starDiameter*this.mmToPixel/40);else if(n.svgPath.endsWith(".svg"))await this.loadAndDrawSVG(t,n.svgPath,s,i+n.starPositionY*this.mmToPixel,n.starDiameter*this.mmToPixel/40);else{t.save(),t.translate(s,i+n.starPositionY*this.mmToPixel),t.scale(n.starDiameter*this.mmToPixel/2,n.starDiameter*this.mmToPixel/2);const o=new Path2D(n.svgPath);t.fillStyle=l,t.fill(o),t.restore()}}catch(o){console.error("Error in drawStarShape:",o)}}}class j0{constructor(){V(this,"primaryColor","#ff0000");V(this,"ruler",{showRuler:!0,showFullRuler:!0,showCrossLine:!0,showDashLine:!0,showSideRuler:!0,showCurrentPositionText:!0});V(this,"drawStar",{svgPath:"M 0 -1 L 0.588 0.809 L -0.951 -0.309 L 0.951 -0.309 L -0.588 0.809 Z",drawStar:!1,starDiameter:14,starPositionY:0,scaleToSmallStar:!1});V(this,"securityPattern",{openSecurityPattern:!0,securityPatternWidth:.15,securityPatternLength:3,securityPatternCount:5,securityPatternAngleRange:40,securityPatternParams:[]});V(this,"company",{companyName:"印章绘制有限责任公司",compression:1,borderOffset:1,textDistributionFactor:5,fontFamily:"SimSun",fontHeight:4.2,fontWeight:"normal",shape:"ellipse",adjustEllipseText:!1,adjustEllipseTextFactor:.5,startAngle:0,rotateDirection:"counterclockwise"});V(this,"taxNumber",{code:"000000000000000000",compression:.7,fontHeight:3.7,fontFamily:"Arial",fontWidth:1.3,letterSpacing:8,positionY:0,totalWidth:26,fontWeight:"normal"});V(this,"stampCode",{code:"1234567890",compression:1,fontHeight:1.2,fontFamily:"Arial",borderOffset:1,fontWidth:1.2,textDistributionFactor:50,fontWeight:"normal"});V(this,"stampType",{stampType:"印章类型",fontHeight:4.6,fontFamily:"Arial",fontWidth:3,compression:.75,letterSpacing:0,positionY:-3,fontWeight:"normal",lineSpacing:2});V(this,"agingEffect",{applyAging:!1,agingIntensity:50,agingEffectParams:[]});V(this,"innerCircle",{drawInnerCircle:!0,innerCircleLineWidth:.5,innerCircleLineRadiusX:16,innerCircleLineRadiusY:12});V(this,"outThinCircle",{drawInnerCircle:!0,innerCircleLineWidth:.2,innerCircleLineRadiusX:36,innerCircleLineRadiusY:27});V(this,"roughEdge",{drawRoughEdge:!0,roughEdgeWidth:.2,roughEdgeHeight:5,roughEdgeParams:[],roughEdgeProbability:.3,roughEdgeShift:8,roughEdgePoints:360});V(this,"stampTypeList",[{stampType:"印章类型",fontHeight:4.6,fontFamily:"Arial",fontWidth:3,compression:.75,letterSpacing:0,positionY:-3,fontWeight:"normal",lineSpacing:2}]);V(this,"companyList",[{companyName:"绘制印章有限责任公司",compression:1,borderOffset:1,textDistributionFactor:3,fontFamily:"SimSun",fontHeight:4.2,fontWeight:"normal",shape:"ellipse",adjustEllipseText:!0,adjustEllipseTextFactor:.5,startAngle:0,rotateDirection:"counterclockwise"}]);V(this,"innerCircleList",[]);V(this,"imageList",[]);V(this,"drawStampConfigs",{roughEdge:this.roughEdge,ruler:this.ruler,drawStar:this.drawStar,securityPattern:this.securityPattern,company:this.company,stampCode:this.stampCode,width:40,height:30,stampType:this.stampType,primaryColor:this.primaryColor,borderWidth:1,refreshSecurityPattern:!1,refreshOld:!1,taxNumber:this.taxNumber,agingEffect:this.agingEffect,innerCircle:this.innerCircle,outThinCircle:this.outThinCircle,openManualAging:!1,stampTypeList:this.stampTypeList,companyList:this.companyList,innerCircleList:this.innerCircleList,imageList:this.imageList,scale:1,offsetX:0,offsetY:0,mmToPixel:0})}initDrawStampConfigs(){return this.drawStampConfigs}}class Y0{constructor(t,n){V(this,"imageCanvas");V(this,"imageCtx");this.imageCanvas=document.createElement("canvas"),this.imageCanvas.width=t,this.imageCanvas.height=n;const s=this.imageCanvas.getContext("2d");if(!s)throw new Error("Failed to get image canvas context");this.imageCtx=s}async drawImage(t,n,s,i,l){this.imageCtx.clearRect(0,0,this.imageCanvas.width,this.imageCanvas.height);const o=document.createElement("div");o.innerHTML=t;const r=o.querySelector("svg");if(!r)throw new Error("Invalid SVG content");r.hasAttribute("width")||r.setAttribute("width",i.toString()),r.hasAttribute("height")||r.setAttribute("height",l.toString());const g=new XMLSerializer().serializeToString(r),f=`data:image/svg+xml;base64,${btoa(unescape(encodeURIComponent(g)))}`,m=new Image;return await new Promise((z,x)=>{m.onload=z,m.onerror=x,m.src=f}),this.imageCtx.drawImage(m,n,s,i,l),this.imageCanvas}getCanvas(){return this.imageCanvas}clear(){this.imageCtx.clearRect(0,0,this.imageCanvas.width,this.imageCanvas.height)}}const bs=80,Ot=80;class Ri{constructor(t,n){V(this,"scale",1);V(this,"offsetX",0);V(this,"offsetY",0);V(this,"mmToPixel");V(this,"canvasCtx");V(this,"offscreenCanvas");V(this,"canvas");V(this,"stampOffsetX",0);V(this,"stampOffsetY",0);V(this,"drawStampConfigs");V(this,"imageCache",new Map);V(this,"drawCircleUtils");V(this,"drawSvgUtils");V(this,"drawCompanyUtils");V(this,"drawRulerUtils");V(this,"drawSecurityPatternUtils");V(this,"initDrawStampConfigsUtils");V(this,"imageCanvas");V(this,"isDragging",!1);V(this,"dragStartX",0);V(this,"dragStartY",0);V(this,"onMouseUp",()=>{this.isDragging=!1,this.refreshStamp(!1,!1)});V(this,"onCanvasClick",t=>{this.canvas});V(this,"onMouseLeave",t=>{this.isDragging=!1,this.refreshStamp()});V(this,"onMouseDown",t=>{this.isDragging=!0,this.dragStartX=t.clientX-this.stampOffsetX*this.mmToPixel,this.dragStartY=t.clientY-this.stampOffsetY*this.mmToPixel});V(this,"onMouseMove",t=>{if(!this.drawStampConfigs.openManualAging)if(this.isDragging){const n=(t.clientX-this.dragStartX)/this.mmToPixel,s=(t.clientY-this.dragStartY)/this.mmToPixel;this.stampOffsetX=Math.round(n*10)/10,this.stampOffsetY=Math.round(s*10)/10,this.refreshStamp()}else{const n=this.canvas.getBoundingClientRect(),s=t.clientX-n.left,i=t.clientY-n.top,l=Math.round((s-bs)/this.mmToPixel*10)/10,o=Math.round((i-Ot)/this.mmToPixel*10)/10;this.refreshStamp(),this.drawStampConfigs.ruler.showCurrentPositionText&&this.drawRulerUtils.drawCurrentPositionText(this.canvasCtx,l,o,this.scale,bs,Ot),this.drawStampConfigs.ruler.showCrossLine&&this.drawRulerUtils.drawPositionCrossLines(this.offscreenCanvas,this.canvas,bs,Ot,s,i,this.drawStampConfigs.primaryColor)}});if(!t)throw new Error("Canvas is null");const s=t.getContext("2d");if(!s)throw new Error("Failed to get canvas context");this.initDrawStampConfigsUtils=new j0,this.drawStampConfigs=this.initDrawStampConfigsUtils.initDrawStampConfigs(),this.canvasCtx=s,this.mmToPixel=n,this.canvas=t,this.offscreenCanvas=document.createElement("canvas"),this.canvas&&this.offscreenCanvas&&(this.offscreenCanvas.width=t.width,this.offscreenCanvas.height=t.height),this.addCanvasListener(),this.initDrawUtils(),this.drawSvgUtils=new Mi(n),this.imageCanvas=new Y0(t.width,t.height)}initDrawUtils(){this.drawCircleUtils=new H0(this.mmToPixel),this.drawSvgUtils=new Mi(this.mmToPixel),this.drawCompanyUtils=new N0(this.mmToPixel),this.drawRulerUtils=new $0(this.mmToPixel),this.drawSecurityPatternUtils=new k0(this.mmToPixel)}getDrawConfigs(){return this.drawStampConfigs}addManualAgingEffect(t,n,s){console.log("手动做旧   1",t,n,this.drawStampConfigs.agingEffect.agingEffectParams);const i=1*this.mmToPixel,l=t-this.stampOffsetX*this.mmToPixel,o=n-this.stampOffsetY*this.mmToPixel;for(let r=0;r<10;r++)this.drawStampConfigs.agingEffect.agingEffectParams.push({x:l,y:o,noiseSize:Math.random()*3+1,noise:Math.random()*200*s,strongNoiseSize:Math.random()*5+2,strongNoise:Math.random()*250*s+5,fade:Math.random()*50*s,seed:Math.random()});this.refreshStamp(!1,!1),this.canvasCtx.save(),this.canvasCtx.globalCompositeOperation="destination-out",this.canvasCtx.beginPath(),this.canvasCtx.arc(t,n,i,0,Math.PI*2,!0),this.canvasCtx.fillStyle="rgba(255, 255, 255, 0.5)",this.canvasCtx.fill(),this.canvasCtx.restore()}setDrawConfigs(t){this.drawStampConfigs=t}addCanvasListener(){this.canvas.addEventListener("mousemove",t=>{if(this.drawStampConfigs.openManualAging&&t.buttons===1){const n=this.canvas.getBoundingClientRect(),s=t.clientX-n.left,i=t.clientY-n.top,l=this.drawStampConfigs.agingEffect.agingIntensity/100;this.addManualAgingEffect(s,i,l)}else this.onMouseMove(t)}),this.canvas.addEventListener("mouseleave",t=>{this.onMouseLeave(t)}),this.canvas.addEventListener("mousedown",t=>{if(this.onMouseDown(t),this.drawStampConfigs.openManualAging){const n=this.canvas.getBoundingClientRect(),s=t.clientX-n.left,i=t.clientY-n.top,l=this.drawStampConfigs.agingEffect.agingIntensity/100;this.addManualAgingEffect(s,i,l)}}),this.canvas.addEventListener("mouseup",t=>{this.onMouseUp()}),this.canvas.addEventListener("click",t=>{this.onCanvasClick(t)}),this.canvas.addEventListener("wheel",t=>{if(t.ctrlKey){t.preventDefault();const n=t.deltaY>0?.9:1.1;this.zoomCanvas(t.offsetX,t.offsetY,n)}})}zoomCanvas(t,n,s){const i=this.scale;this.scale*=s,this.scale=Math.max(.1,Math.min(5,this.scale)),this.offsetX=t-(t-this.offsetX)*(this.scale/i),this.offsetY=n-(n-this.offsetY)*(this.scale/i),this.refreshStamp()}async drawSvgImage(t,n,s,i){try{const r=await this.imageCanvas.drawImage(n.svgPath,s-100,i-100,200,200);t.drawImage(r,0,0)}catch(l){console.error("Error drawing SVG:",l)}}async drawImageList(t,n,s,i){for(const l of n)if(l.imageUrl){let o=this.imageCache.get(l.imageUrl);if(o)this.drawSingleImage(t,o,l,s,i);else try{const r=new Image;r.src=l.imageUrl,await new Promise((h,f)=>{r.onload=h,r.onerror=f});const g=await createImageBitmap(r);this.imageCache.set(l.imageUrl,g),this.drawSingleImage(t,g,l,s,i),requestAnimationFrame(()=>{this.refreshStamp()})}catch(r){console.error("Error loading or processing image:",r)}}}drawSingleImage(t,n,s,i,l){let o=s.imageWidth*this.mmToPixel,r=s.imageHeight*this.mmToPixel;if(s.keepAspectRatio){const f=Math.min(o/n.width,r/n.height);o=n.width*f,r=n.height*f}const g=i-o/2+s.positionX*this.mmToPixel,h=l-r/2+s.positionY*this.mmToPixel;t.save(),t.drawImage(n,g,h,o,r),t.restore()}async clearImageCache(){for(const t of this.imageCache.values())t.close();this.imageCache.clear()}drawStampType(t,n,s,i,l){const o=n.fontHeight*this.mmToPixel,r=n.letterSpacing,g=n.positionY,h=n.fontWeight||"normal",f=n.lineSpacing*this.mmToPixel;t.save(),t.font=`${h} ${o}px ${n.fontFamily}`,t.fillStyle=this.drawStampConfigs.primaryColor,t.textAlign="center",t.textBaseline="middle";const m=n.stampType.split(`
`),z=m.length;m.forEach((x,R)=>{const O=x.split(""),B=O.map(q=>t.measureText(q).width),D=B.reduce((q,pe)=>q+pe,0)+(O.length-1)*r*this.mmToPixel,j=(R-(z-1)/2)*(o+f),Y=i+l*.5+g*this.mmToPixel+j;t.save(),t.translate(s,Y);let U=-D/2;t.scale(n.compression,1),O.forEach((q,pe)=>{t.fillText(q,U+B[pe]/2,0),U+=B[pe]+r*this.mmToPixel}),t.restore()}),t.restore()}drawStampTypeList(t,n,s,i,l){n.forEach(o=>{this.drawStampType(t,o,s,i,l)}),t.restore()}drawEllipse(t,n,s,i,l,o,r){t.beginPath(),t.ellipse(n,s,i,l,0,0,Math.PI*2),t.strokeStyle=r,t.lineWidth=o,t.stroke()}drawCode(t,n,s,i,l,o){const r=n.fontHeight*this.mmToPixel,g=n.code,h=n.fontWeight||"normal";t.save(),t.font=`${h} ${r}px ${n.fontFamily}`,t.fillStyle=this.drawStampConfigs.primaryColor,t.textAlign="center",t.textBaseline="middle";const f=g.split(""),m=f.length;if(m===1){const z=s,x=i+o-r-n.borderOffset*this.mmToPixel;t.save(),t.translate(z,x),t.scale(n.compression,1),t.fillText(g,0,0),t.restore()}else{const z=Math.PI*((1+m)/n.textDistributionFactor),x=Math.PI/2+z/2,R=z/(m-1);f.forEach((O,B)=>{const D=x-R*B,j=s+Math.cos(D)*(l-r/2-n.borderOffset*this.mmToPixel),Y=i+Math.sin(D)*(o-r/2-n.borderOffset*this.mmToPixel);t.save(),t.translate(j,Y),t.rotate(D-Math.PI/2),t.scale(n.compression,1),t.fillText(O,0,0),t.restore()})}t.restore()}drawTaxNumber(t,n,s,i){const l=n.fontHeight*this.mmToPixel,o=n.totalWidth*this.mmToPixel,r=n.positionY*this.mmToPixel+.3,g=n.fontWeight||"normal";t.save(),t.font=`${g} ${l}px ${n.fontFamily}`,t.fillStyle=this.drawStampConfigs.primaryColor,t.textAlign="center",t.textBaseline="middle";const h=n.code.split(""),f=h.length,m=this.drawStampConfigs.taxNumber.letterSpacing*this.mmToPixel,x=(o*this.drawStampConfigs.taxNumber.compression-(f-1)*m)/f,R=f*x+(f-1)*m,O=s-R/2+x/2,B=i+r*this.mmToPixel;h.forEach((D,j)=>{const Y=O+j*(x+m);t.save(),t.translate(Y,B),t.scale(this.drawStampConfigs.taxNumber.compression,1.35),t.fillText(D,0,0),t.restore()}),t.restore()}addRoughEdge(t,n,s,i,l,o,r=!1){const g=o*this.drawStampConfigs.roughEdge.roughEdgeHeight*.01,h=this.drawStampConfigs.roughEdge.roughEdgePoints,f=this.drawStampConfigs.roughEdge.roughEdgeShift;if(t.save(),t.fillStyle="white",t.globalCompositeOperation="destination-out",r||this.drawStampConfigs.roughEdge.roughEdgeParams.length===0){this.drawStampConfigs.roughEdge.roughEdgeParams=[];for(let m=0;m<h;m++){const z=m/h*Math.PI*2,R=Math.random()>this.drawStampConfigs.roughEdge.roughEdgeProbability?Math.random()*g*Math.random()+this.drawStampConfigs.roughEdge.roughEdgeWidth:0;this.drawStampConfigs.roughEdge.roughEdgeParams.push({angle:z,size:R,offset:f,opacity:1})}}this.drawStampConfigs.roughEdge.roughEdgeParams.forEach(({angle:m,size:z})=>{const x=n+Math.cos(m)*(i+f),R=s+Math.sin(m)*(l+f);z>0&&(t.beginPath(),t.arc(x,R,z*this.mmToPixel,0,Math.PI*2),t.fill())}),t.restore()}addAgingEffect(t,n,s,i=!1){if(!this.drawStampConfigs.agingEffect.applyAging)return;const l=t.getImageData(0,0,n,s),o=l.data,r=n/(2*this.scale)+this.stampOffsetX*this.mmToPixel/this.scale,g=s/(2*this.scale)+this.stampOffsetY*this.mmToPixel/this.scale,h=Math.max(n,s)/2*this.mmToPixel/this.scale;if(i||this.drawStampConfigs.agingEffect.agingEffectParams.length===0){this.drawStampConfigs.agingEffect.agingEffectParams=[];for(let f=0;f<s;f++)for(let m=0;m<n;m++){const z=(f*n+m)*4;if(Math.sqrt(Math.pow(m-r,2)+Math.pow(f-g,2))<=h&&o[z]>200&&o[z+1]<50&&o[z+2]<50){const R=this.drawStampConfigs.agingEffect.agingIntensity/100,O=Math.random();this.drawStampConfigs.agingEffect.agingEffectParams.push({x:m-this.stampOffsetX*this.mmToPixel,y:f-this.stampOffsetY*this.mmToPixel,noiseSize:Math.random()*3+1,noise:Math.random()*200*R,strongNoiseSize:Math.random()*5+2,strongNoise:Math.random()*250*R+5,fade:Math.random()*50*R,seed:O})}}}this.drawStampConfigs.agingEffect.agingEffectParams.forEach(f=>{const{x:m,y:z,noiseSize:x,noise:R,strongNoiseSize:O,strongNoise:B,fade:D,seed:j}=f,Y=m+this.stampOffsetX*this.mmToPixel,U=z+this.stampOffsetY*this.mmToPixel,q=(Math.round(U)*n+Math.round(Y))*4;j<.4&&this.addCircularNoise(o,n,Y,U,x,R,!0),j<.05&&this.addCircularNoise(o,n,Y,U,O,B,!0),j<.2&&(o[q+3]=Math.max(0,o[q+3]-D))}),t.putImageData(l,0,0)}addCircularNoise(t,n,s,i,l,o,r=!1){const g=l*l/4;for(let h=-l/2;h<l/2;h++)for(let f=-l/2;f<l/2;f++)if(f*f+h*h<=g){const m=Math.round(s+f),x=(Math.round(i+h)*n+m)*4;x>=0&&x<t.length&&(r?t[x+3]=Math.max(0,t[x+3]-o):(t[x]=Math.min(255,t[x]+o),t[x+1]=Math.min(255,t[x+1]+o),t[x+2]=Math.min(255,t[x+2]+o)))}}saveStampAsPNG(t=512){this.drawStampConfigs.ruler.showCrossLine=!1,this.drawStampConfigs.ruler.showRuler=!1,this.drawStampConfigs.ruler.showDashLine=!1,this.drawStampConfigs.ruler.showSideRuler=!1,this.drawStampConfigs.ruler.showFullRuler=!1,this.drawStampConfigs.ruler.showCurrentPositionText=!1,this.refreshStamp(),setTimeout(()=>{const n=document.createElement("canvas");n.width=t,n.height=t;const s=n.getContext("2d");if(!s)return;s.clearRect(0,0,t,t);const i=(Math.max(this.drawStampConfigs.width,this.drawStampConfigs.height)+2)*this.mmToPixel,l=(this.canvas.width-i)/2+this.stampOffsetX*this.mmToPixel,o=(this.canvas.height-i)/2+this.stampOffsetY*this.mmToPixel,r=t*.01,g=t-2*r;s.drawImage(this.canvas,l,o,i,i,r,r,g,g),this.drawStampConfigs.agingEffect.applyAging&&this.addAgingEffect(s,t,t,!1);const h=n.toDataURL("image/png"),f=document.createElement("a");f.href=h,f.download="印章.png",document.body.appendChild(f),f.click(),document.body.removeChild(f),this.drawStampConfigs.ruler.showCrossLine=!0,this.drawStampConfigs.ruler.showRuler=!0,this.drawStampConfigs.ruler.showDashLine=!0,this.drawStampConfigs.ruler.showSideRuler=!0,this.drawStampConfigs.ruler.showFullRuler=!0,this.drawStampConfigs.ruler.showCurrentPositionText=!0,this.refreshStamp()},50)}refreshStamp(t=!1,n=!1,s=!1){this.canvasCtx.clearRect(0,0,this.canvas.width,this.canvas.height),this.canvasCtx.save(),this.canvasCtx.translate(this.offsetX,this.offsetY),this.canvasCtx.scale(this.scale,this.scale);const i=this.canvas.width/2/this.scale,l=this.canvas.height/2/this.scale,o=this.mmToPixel,r=(this.drawStampConfigs.width-this.drawStampConfigs.borderWidth)/2,g=(this.drawStampConfigs.height-this.drawStampConfigs.borderWidth)/2,h=this.stampOffsetX*this.mmToPixel,f=this.stampOffsetY*this.mmToPixel,m=i+h,z=l+f;this.drawStamp(this.canvasCtx,m,z,r*o,g*o,this.drawStampConfigs.borderWidth*o,this.drawStampConfigs.primaryColor,t,n,s),this.canvasCtx.restore(),this.drawStampConfigs.ruler.showRuler&&(this.drawStampConfigs.ruler.showSideRuler&&(this.drawRulerUtils.drawRuler(this.canvasCtx,this.drawStampConfigs.ruler,this.scale,this.canvas.width,Ot,!0),this.drawRulerUtils.drawRuler(this.canvasCtx,this.drawStampConfigs.ruler,this.scale,this.canvas.height,Ot,!1)),this.drawStampConfigs.ruler.showDashLine&&this.drawRulerUtils.showCrossDashLine(this.canvasCtx,this.drawStampConfigs.ruler,this.scale,Ot,Ot,this.canvas.width,this.canvas.height))}resetZoom(){this.scale=1,this.offsetX=0,this.offsetY=0,this.refreshStamp()}drawStamp(t,n,s,i,l,o,r,g=!1,h=!1,f=!1){t.clearRect(0,0,this.canvas.width,this.canvas.height);const m=this.offscreenCanvas;m.width=this.canvas.width,m.height=this.canvas.height;const z=m.getContext("2d");if(!z)return;const x=document.createElement("canvas");x.width=this.canvas.width,x.height=this.canvas.height,x.getContext("2d")&&(V0(z,n,s,i,l,o,r),z.save(),z.beginPath(),z.ellipse(n,s,i,l,0,0,Math.PI*2),z.clip(),this.drawStampConfigs.innerCircleList.length>0&&this.drawCircleUtils.drawCircleList(z,this.drawStampConfigs.innerCircleList,n,s,r),this.drawStampConfigs.drawStar.drawStar&&this.drawSvgUtils.drawStarShape(z,this.drawStampConfigs.drawStar,n,s,this.drawStampConfigs.primaryColor),this.drawStampConfigs.imageList&&this.drawStampConfigs.imageList.length>0&&this.drawImageList(z,this.drawStampConfigs.imageList,n,s),this.drawCompanyUtils.drawCompanyList(z,this.drawStampConfigs.companyList,n,s,i,l,this.drawStampConfigs.primaryColor),this.drawStampTypeList(z,this.drawStampConfigs.stampTypeList,n,s,i),this.drawCode(z,this.drawStampConfigs.stampCode,n,s,i,l),this.drawTaxNumber(z,this.drawStampConfigs.taxNumber,n,s),z.restore(),t.save(),this.drawStampConfigs.roughEdge.drawRoughEdge&&this.addRoughEdge(z,n,s,i,l,o,f),this.drawStampConfigs.securityPattern.openSecurityPattern&&this.drawSecurityPatternUtils.drawSecurityPattern(z,this.drawStampConfigs.securityPattern,n,s,i,l,g),t.globalCompositeOperation="source-over",t.drawImage(m,0,0),t.restore(),this.drawStampConfigs.agingEffect.applyAging&&this.addAgingEffect(t,this.canvas.width,this.canvas.height,h))}}async function B0(){try{if(window.queryLocalFonts){const e=await window.queryLocalFonts();return[...new Set(e.map(t=>t.family))]}else return["SimSun","SimHei","Microsoft YaHei","KaiTi","FangSong","STHeiti","STKaiti","STSong","STFangsong","LiSu","YouYuan","STZhongsong","STXihei","Arial","Times New Roman","Helvetica"]}catch(e){return console.error("获取系统字体失败:",e),["SimSun","SimHei","Microsoft YaHei","KaiTi"]}}const K0={drawRoughEdge:!0,roughEdgeWidth:.2,roughEdgeHeight:5,roughEdgeParams:[{angle:0,size:0},{angle:.017453292519943295,size:.38295203621045515},{angle:.03490658503988659,size:.5492613923360503},{angle:.05235987755982988,size:.27434474993518904},{angle:.06981317007977318,size:.22030697295451715},{angle:.08726646259971647,size:.3156841250988867},{angle:.10471975511965977,size:.40612969609125726},{angle:.12217304763960307,size:.2522901213757936},{angle:.13962634015954636,size:.246306211980516},{angle:.15707963267948966,size:.23786514123799182},{angle:.17453292519943295,size:0},{angle:.19198621771937624,size:.2015434095954711},{angle:.20943951023931953,size:.38369357988290775},{angle:.22689280275926282,size:0},{angle:.24434609527920614,size:.6487769458762811},{angle:.2617993877991494,size:0},{angle:.2792526803190927,size:0},{angle:.296705972839036,size:.2317359759490517},{angle:.3141592653589793,size:.2712568593134777},{angle:.33161255787892263,size:.2845195717645558},{angle:.3490658503988659,size:0},{angle:.3665191429188092,size:0},{angle:.3839724354387525,size:.6230142506529917},{angle:.40142572795869574,size:.23084210527369242},{angle:.41887902047863906,size:0},{angle:.4363323129985824,size:.29243244701418614},{angle:.45378560551852565,size:.5814076452082239},{angle:.47123889803846897,size:.38996798463997884},{angle:.4886921905584123,size:.5609326818193505},{angle:.5061454830783556,size:.4126978370429956},{angle:.5235987755982988,size:.46945256703319604},{angle:.5410520681182421,size:.23998992906190666},{angle:.5585053606381855,size:0},{angle:.5759586531581287,size:.33107627343004614},{angle:.593411945678072,size:.26114366022228325},{angle:.6108652381980153,size:.44170318442645573},{angle:.6283185307179586,size:.3407227219134016},{angle:.6457718232379019,size:.4622866936800872},{angle:.6632251157578453,size:.32531065236340706},{angle:.6806784082777886,size:.4483851005090098},{angle:.6981317007977318,size:.2448124389343519},{angle:.7155849933176751,size:.2541491123397663},{angle:.7330382858376184,size:.21847185146472708},{angle:.7504915783575618,size:0},{angle:.767944870877505,size:0},{angle:.7853981633974483,size:0},{angle:.8028514559173915,size:.3447841036309345},{angle:.8203047484373349,size:0},{angle:.8377580409572781,size:0},{angle:.8552113334772213,size:0},{angle:.8726646259971648,size:.20527614207837877},{angle:.890117918517108,size:0},{angle:.9075712110370513,size:0},{angle:.9250245035569946,size:.3856907029999833},{angle:.9424777960769379,size:.24568040616605208},{angle:.9599310885968813,size:.24387909310207762},{angle:.9773843811168246,size:.3873033740903039},{angle:.9948376736367678,size:.39382726829515113},{angle:1.0122909661567112,size:0},{angle:1.0297442586766543,size:.3100704229098932},{angle:1.0471975511965976,size:.22843467963639974},{angle:1.064650843716541,size:0},{angle:1.0821041362364843,size:0},{angle:1.0995574287564276,size:.22001569021712197},{angle:1.117010721276371,size:0},{angle:1.1344640137963142,size:0},{angle:1.1519173063162573,size:.2844161570717306},{angle:1.1693705988362009,size:.2501214761414058},{angle:1.186823891356144,size:.36924905509636724},{angle:1.2042771838760875,size:0},{angle:1.2217304763960306,size:0},{angle:1.239183768915974,size:.4020436243776079},{angle:1.2566370614359172,size:.24827252389605228},{angle:1.2740903539558606,size:.286073856283045},{angle:1.2915436464758039,size:.494627844741657},{angle:1.3089969389957472,size:.2454587617336245},{angle:1.3264502315156905,size:.3094515137722454},{angle:1.3439035240356336,size:.23637957236743304},{angle:1.3613568165555772,size:0},{angle:1.3788101090755203,size:0},{angle:1.3962634015954636,size:.20412286570199437},{angle:1.413716694115407,size:.24047090482284017},{angle:1.4311699866353502,size:.34666334659398645},{angle:1.4486232791552935,size:0},{angle:1.4660765716752369,size:.22726894266550954},{angle:1.48352986419518,size:.4820862785218389},{angle:1.5009831567151235,size:.4900794054445491},{angle:1.5184364492350666,size:0},{angle:1.53588974175501,size:.31077487338632476},{angle:1.5533430342749532,size:0},{angle:1.5707963267948966,size:.3096924472677579},{angle:1.5882496193148399,size:.2242517531481337},{angle:1.605702911834783,size:0},{angle:1.6231562043547265,size:.3573012946346664},{angle:1.6406094968746698,size:.3539673000303739},{angle:1.6580627893946132,size:.4493979184966387},{angle:1.6755160819145563,size:.2435163326350156},{angle:1.6929693744344996,size:.4700148636032513},{angle:1.7104226669544427,size:0},{angle:1.7278759594743864,size:0},{angle:1.7453292519943295,size:.545036528273444},{angle:1.7627825445142729,size:0},{angle:1.780235837034216,size:0},{angle:1.7976891295541593,size:.35832487488982373},{angle:1.8151424220741026,size:0},{angle:1.8325957145940461,size:.45358546019000345},{angle:1.8500490071139892,size:.37813184438366143},{angle:1.8675022996339325,size:.42066320855740963},{angle:1.8849555921538759,size:.22990722992079815},{angle:1.902408884673819,size:.34671651422545785},{angle:1.9198621771937625,size:.2559117507605169},{angle:1.9373154697137058,size:.3770724933685311},{angle:1.9547687622336491,size:.4467399517651608},{angle:1.9722220547535922,size:.2306109026042734},{angle:1.9896753472735356,size:.24412993697937255},{angle:2.007128639793479,size:.2731605071560616},{angle:2.0245819323134224,size:0},{angle:2.0420352248333655,size:0},{angle:2.0594885173533086,size:0},{angle:2.076941809873252,size:.298209325216828},{angle:2.0943951023931953,size:0},{angle:2.111848394913139,size:.2761919087766098},{angle:2.129301687433082,size:.2303367463257467},{angle:2.1467549799530254,size:0},{angle:2.1642082724729685,size:.21592332912084936},{angle:2.1816615649929116,size:.29689704754021284},{angle:2.199114857512855,size:.2736677833089964},{angle:2.2165681500327987,size:.44081382511230294},{angle:2.234021442552742,size:0},{angle:2.251474735072685,size:0},{angle:2.2689280275926285,size:0},{angle:2.2863813201125716,size:.2324275170023464},{angle:2.3038346126325147,size:.21488001083655994},{angle:2.321287905152458,size:.2000761478431045},{angle:2.3387411976724017,size:.32108071359823326},{angle:2.356194490192345,size:.2796037803575216},{angle:2.373647782712288,size:0},{angle:2.3911010752322315,size:0},{angle:2.408554367752175,size:.3071203309470264},{angle:2.426007660272118,size:0},{angle:2.443460952792061,size:0},{angle:2.4609142453120048,size:0},{angle:2.478367537831948,size:0},{angle:2.495820830351891,size:.2957402344868382},{angle:2.5132741228718345,size:0},{angle:2.530727415391778,size:0},{angle:2.548180707911721,size:.5218905725112977},{angle:2.5656340004316642,size:.2128333297070366},{angle:2.5830872929516078,size:.23762846686866643},{angle:2.600540585471551,size:0},{angle:2.6179938779914944,size:0},{angle:2.6354471705114375,size:0},{angle:2.652900463031381,size:.43067817432513267},{angle:2.670353755551324,size:.251100899774858},{angle:2.6878070480712672,size:.5719027771274653},{angle:2.705260340591211,size:0},{angle:2.7227136331111543,size:.37597645466750806},{angle:2.7401669256310974,size:.2516699750238845},{angle:2.7576202181510405,size:.2905083224535283},{angle:2.7750735106709836,size:0},{angle:2.792526803190927,size:.23403918990114664},{angle:2.8099800957108707,size:.28164860055171315},{angle:2.827433388230814,size:.33570892978211286},{angle:2.844886680750757,size:.23037735725477287},{angle:2.8623399732707004,size:.20234094608333014},{angle:2.8797932657906435,size:.48018978799750517},{angle:2.897246558310587,size:.3083444076717795},{angle:2.91469985083053,size:0},{angle:2.9321531433504737,size:.5202938598060132},{angle:2.949606435870417,size:.2452234594941649},{angle:2.96705972839036,size:.20621527311747193},{angle:2.9845130209103035,size:0},{angle:3.001966313430247,size:.2251741478067806},{angle:3.01941960595019,size:.292733813104287},{angle:3.036872898470133,size:.4747992853341091},{angle:3.0543261909900767,size:0},{angle:3.07177948351002,size:.21240209096627946},{angle:3.089232776029963,size:.23507559861062655},{angle:3.1066860685499065,size:0},{angle:3.12413936106985,size:0},{angle:3.141592653589793,size:.29815762522238404},{angle:3.159045946109736,size:0},{angle:3.1764992386296798,size:0},{angle:3.193952531149623,size:.5901429894718011},{angle:3.211405823669566,size:.5974209429375996},{angle:3.2288591161895095,size:.24416679351420006},{angle:3.246312408709453,size:0},{angle:3.2637657012293966,size:.506073722136908},{angle:3.2812189937493397,size:0},{angle:3.2986722862692828,size:.36597518132766504},{angle:3.3161255787892263,size:0},{angle:3.3335788713091694,size:0},{angle:3.3510321638291125,size:0},{angle:3.368485456349056,size:.6037208156598743},{angle:3.385938748868999,size:.279160859643192},{angle:3.4033920413889422,size:.37220400172995294},{angle:3.4208453339088853,size:.37748863606739747},{angle:3.4382986264288293,size:.2107989050214913},{angle:3.455751918948773,size:.4351201476445452},{angle:3.473205211468716,size:.36285258361795913},{angle:3.490658503988659,size:.29998347533739605},{angle:3.5081117965086026,size:.20092496858442133},{angle:3.5255650890285457,size:0},{angle:3.543018381548489,size:.37814528234776545},{angle:3.560471674068432,size:.2385265818864284},{angle:3.5779249665883754,size:.3059994265268066},{angle:3.5953782591083185,size:0},{angle:3.6128315516282616,size:.2577187712542432},{angle:3.630284844148205,size:0},{angle:3.647738136668149,size:.283266213677964},{angle:3.6651914291880923,size:.5419206869967785},{angle:3.6826447217080354,size:0},{angle:3.7000980142279785,size:.24188206877997467},{angle:3.717551306747922,size:.3718844622815858},{angle:3.735004599267865,size:.20395706268246955},{angle:3.752457891787808,size:0},{angle:3.7699111843077517,size:.269463613416793},{angle:3.787364476827695,size:.27677705210186654},{angle:3.804817769347638,size:.31205270076489416},{angle:3.8222710618675815,size:.24489501509740932},{angle:3.839724354387525,size:.3206064785801519},{angle:3.8571776469074686,size:.26973655528717033},{angle:3.8746309394274117,size:0},{angle:3.8920842319473548,size:.5329452694839945},{angle:3.9095375244672983,size:0},{angle:3.9269908169872414,size:0},{angle:3.9444441095071845,size:0},{angle:3.961897402027128,size:.31803959564006123},{angle:3.979350694547071,size:.27949367154160926},{angle:3.9968039870670142,size:.2779335935429797},{angle:4.014257279586958,size:.22651700120284607},{angle:4.031710572106902,size:0},{angle:4.049163864626845,size:.4147579556206455},{angle:4.066617157146788,size:.502607531171329},{angle:4.084070449666731,size:.4057269277969495},{angle:4.101523742186674,size:.2811797670905706},{angle:4.118977034706617,size:0},{angle:4.136430327226561,size:.43073095465385847},{angle:4.153883619746504,size:.5209935700764091},{angle:4.171336912266447,size:0},{angle:4.1887902047863905,size:.22205989879990534},{angle:4.206243497306334,size:0},{angle:4.223696789826278,size:0},{angle:4.241150082346221,size:0},{angle:4.258603374866164,size:.5590056872827196},{angle:4.276056667386108,size:.382771790208737},{angle:4.293509959906051,size:.308450138010432},{angle:4.310963252425994,size:0},{angle:4.328416544945937,size:.20987002240002106},{angle:4.34586983746588,size:0},{angle:4.363323129985823,size:.3331984790288821},{angle:4.380776422505767,size:.2922008337374681},{angle:4.39822971502571,size:.2053337353395718},{angle:4.4156830075456535,size:0},{angle:4.4331363000655974,size:.22551974764020258},{angle:4.4505895925855405,size:.2138619359580861},{angle:4.468042885105484,size:.28618941442769497},{angle:4.485496177625427,size:.3428933823612739},{angle:4.50294947014537,size:0},{angle:4.520402762665314,size:.5967059422685509},{angle:4.537856055185257,size:.3833493364544054},{angle:4.5553093477052,size:.60130230126192},{angle:4.572762640225143,size:.5738648444412305},{angle:4.590215932745086,size:.21850688919505262},{angle:4.607669225265029,size:.20566524689026358},{angle:4.625122517784973,size:.5137770748798419},{angle:4.642575810304916,size:.34946042723119625},{angle:4.66002910282486,size:.2287881159363336},{angle:4.6774823953448035,size:.2355438884134427},{angle:4.694935687864747,size:0},{angle:4.71238898038469,size:.24401366821517417},{angle:4.729842272904633,size:0},{angle:4.747295565424576,size:.4386979552875503},{angle:4.764748857944519,size:.35308646977788044},{angle:4.782202150464463,size:0},{angle:4.799655442984406,size:.5451606464538774},{angle:4.81710873550435,size:.3029721352349761},{angle:4.834562028024293,size:0},{angle:4.852015320544236,size:.37320609592907344},{angle:4.869468613064179,size:0},{angle:4.886921905584122,size:.44629176019424927},{angle:4.9043751981040655,size:0},{angle:4.9218284906240095,size:.21948420242671768},{angle:4.939281783143953,size:.21617085623851662},{angle:4.956735075663896,size:.21735079185803896},{angle:4.974188368183839,size:.5172828645149814},{angle:4.991641660703782,size:.20173808887642838},{angle:5.009094953223726,size:0},{angle:5.026548245743669,size:.39828611067878034},{angle:5.044001538263612,size:.2840677025368674},{angle:5.061454830783556,size:0},{angle:5.078908123303499,size:0},{angle:5.096361415823442,size:.2238510605444375},{angle:5.113814708343385,size:.4716984484529754},{angle:5.1312680008633285,size:.21535480242638494},{angle:5.148721293383272,size:.3341952328429191},{angle:5.1661745859032155,size:.21740549472404713},{angle:5.183627878423159,size:.47040575474782287},{angle:5.201081170943102,size:.3244807302556352},{angle:5.218534463463046,size:.2580754179212702},{angle:5.235987755982989,size:.4624154963046924},{angle:5.253441048502932,size:.201714413502068},{angle:5.270894341022875,size:.3340395897002632},{angle:5.288347633542818,size:.3623815856385897},{angle:5.305800926062762,size:.22470487048075383},{angle:5.323254218582705,size:.6317581429629764},{angle:5.340707511102648,size:0},{angle:5.358160803622591,size:0},{angle:5.3756140961425345,size:.26582844919746756},{angle:5.393067388662478,size:0},{angle:5.410520681182422,size:.24690021397667702},{angle:5.427973973702365,size:.299212133323042},{angle:5.445427266222309,size:.21335753776228694},{angle:5.462880558742252,size:.21744182424161645},{angle:5.480333851262195,size:0},{angle:5.497787143782138,size:.26127448233707784},{angle:5.515240436302081,size:0},{angle:5.532693728822024,size:.21010189500474288},{angle:5.550147021341967,size:0},{angle:5.567600313861911,size:.3028828766192924},{angle:5.585053606381854,size:.20314275883528143},{angle:5.602506898901798,size:0},{angle:5.619960191421741,size:.25454007257343775},{angle:5.6374134839416845,size:.20667793686377162},{angle:5.654866776461628,size:.45370551724727093},{angle:5.672320068981571,size:.31046294335586977},{angle:5.689773361501514,size:0},{angle:5.707226654021458,size:0},{angle:5.724679946541401,size:.2559910430906398},{angle:5.742133239061344,size:.3771699672911649},{angle:5.759586531581287,size:0},{angle:5.77703982410123,size:.22024367992318616},{angle:5.794493116621174,size:.22874708203163643},{angle:5.811946409141117,size:.34408056590047187},{angle:5.82939970166106,size:.23519983879227166},{angle:5.846852994181004,size:0},{angle:5.8643062867009474,size:0},{angle:5.8817595792208905,size:.43733534767515825},{angle:5.899212871740834,size:.4490881907656139},{angle:5.916666164260777,size:0},{angle:5.93411945678072,size:.20202185594320343},{angle:5.951572749300664,size:0},{angle:5.969026041820607,size:.4006389251697413},{angle:5.98647933434055,size:.22734323569469708},{angle:6.003932626860494,size:0},{angle:6.021385919380437,size:0},{angle:6.03883921190038,size:0},{angle:6.056292504420323,size:.29053305748258446},{angle:6.073745796940266,size:0},{angle:6.09119908946021,size:.22147539569893432},{angle:6.1086523819801535,size:.22459570403151013},{angle:6.126105674500097,size:.2030313802612825},{angle:6.14355896702004,size:.3947947454071749},{angle:6.161012259539983,size:.2696549738905104},{angle:6.178465552059926,size:0},{angle:6.19591884457987,size:0},{angle:6.213372137099813,size:0},{angle:6.230825429619757,size:.4276929124437861},{angle:6.2482787221397,size:.29361877489386645},{angle:6.265732014659643,size:0}],roughEdgeProbability:.3,roughEdgeShift:8,roughEdgePoints:360},X0={showRuler:!1,showFullRuler:!0},G0={svgPath:"M 0 -1 L 0.588 0.809 L -0.951 -0.309 L 0.951 -0.309 L -0.588 0.809 Z",drawStar:!1,starDiameter:14,starPositionY:0,scaleToSmallStar:!1,useImage:!1,imageUrl:"",imageWidth:10,imageHeight:10,keepAspectRatio:!0},q0={openSecurityPattern:!0,securityPatternWidth:.15,securityPatternLength:3,securityPatternCount:5,securityPatternAngleRange:40,securityPatternParams:[{angle:2.2133597445443898,lineAngle:-.4081252598817178},{angle:5.495176927405389,lineAngle:2.748263363444218},{angle:3.953214289859482,lineAngle:-2.5713077607148795},{angle:2.203849967220271,lineAngle:-.187991596425076},{angle:1.5481188637896033,lineAngle:-.22305068253272764}]},J0={companyName:"印章绘制有限责任公司",compression:1,borderOffset:1,textDistributionFactor:5,fontFamily:"Songti SC",fontHeight:4.2,fontWeight:400,shape:"ellipse",adjustEllipseText:!1,adjustEllipseTextFactor:.5,startAngle:0,rotateDirection:"clockwise"},Z0={code:"1234567890",compression:1,fontHeight:1.2,fontFamily:"Arial",borderOffset:1,fontWidth:1.2,textDistributionFactor:50,fontWeight:"normal"},Q0=40,er=30,tr={stampType:"印章类型",fontHeight:4.6,fontFamily:"Arial",fontWidth:3,compression:.75,letterSpacing:0,positionY:-3,fontWeight:"normal",lineSpacing:2},nr="#ff0000",sr=1,ir=!1,lr=!1,or={code:"000000000000000000",compression:.7,fontHeight:3.7,fontFamily:"Arial",fontWidth:1.3,letterSpacing:8,positionY:0,totalWidth:26,fontWeight:"normal"},ar={applyAging:!1,agingIntensity:50,agingEffectParams:[]},rr=!0,ur={drawInnerCircle:!0,innerCircleLineWidth:.5,innerCircleLineRadiusX:16,innerCircleLineRadiusY:12},gr={drawInnerCircle:!0,innerCircleLineWidth:.2,innerCircleLineRadiusX:36,innerCircleLineRadiusY:27},cr=!1,fr=[{stampType:"印章类型",fontHeight:4.6,fontFamily:"Arial",fontWidth:3,compression:.75,letterSpacing:0,positionY:-3,fontWeight:"normal",lineSpacing:2}],dr=[{companyName:"绘制印章有限责任公司",compression:1,borderOffset:1,textDistributionFactor:3,fontFamily:"SimSun",fontHeight:4.2,fontWeight:"normal",shape:"ellipse",adjustEllipseText:!0,adjustEllipseTextFactor:.5}],pr=[],hr={roughEdge:K0,ruler:X0,drawStar:G0,securityPattern:q0,company:J0,stampCode:Z0,width:Q0,height:er,stampType:tr,primaryColor:nr,borderWidth:sr,refreshSecurityPattern:ir,refreshOld:lr,taxNumber:or,agingEffect:ar,shouldDrawRuler:rr,innerCircle:ur,outThinCircle:gr,openManualAging:cr,stampTypeList:fr,companyList:dr,innerCircleList:pr},mr={drawRoughEdge:!1,roughEdgeWidth:.2,roughEdgeHeight:5,roughEdgeParams:[{angle:0,size:0},{angle:.017453292519943295,size:0},{angle:.03490658503988659,size:0},{angle:.05235987755982988,size:.31730287292758974},{angle:.06981317007977318,size:.3916444365989694},{angle:.08726646259971647,size:.208608754055621},{angle:.10471975511965977,size:.2201400602607466},{angle:.12217304763960307,size:0},{angle:.13962634015954636,size:.413218673378141},{angle:.15707963267948966,size:.2265863014473266},{angle:.17453292519943295,size:.24605542095631253},{angle:.19198621771937624,size:.23655514355059734},{angle:.20943951023931953,size:.253807375717143},{angle:.22689280275926282,size:0},{angle:.24434609527920614,size:.24330943667000401},{angle:.2617993877991494,size:.22953279555062572},{angle:.2792526803190927,size:.21392879792197558},{angle:.296705972839036,size:.2890854202057443},{angle:.3141592653589793,size:0},{angle:.33161255787892263,size:0},{angle:.3490658503988659,size:.20956234004730973},{angle:.3665191429188092,size:0},{angle:.3839724354387525,size:.4195584128674653},{angle:.40142572795869574,size:.28732832573605316},{angle:.41887902047863906,size:.47727796370752146},{angle:.4363323129985824,size:.4078196234676063},{angle:.45378560551852565,size:0},{angle:.47123889803846897,size:0},{angle:.4886921905584123,size:0},{angle:.5061454830783556,size:0},{angle:.5235987755982988,size:.23556530694644498},{angle:.5410520681182421,size:.2599537196239181},{angle:.5585053606381855,size:0},{angle:.5759586531581287,size:.22683369358645425},{angle:.593411945678072,size:.43941963242183213},{angle:.6108652381980153,size:.256981026413524},{angle:.6283185307179586,size:0},{angle:.6457718232379019,size:.4143289542013904},{angle:.6632251157578453,size:.2043975927300548},{angle:.6806784082777886,size:.2775738594178865},{angle:.6981317007977318,size:.45292165202456325},{angle:.7155849933176751,size:0},{angle:.7330382858376184,size:0},{angle:.7504915783575618,size:0},{angle:.767944870877505,size:.20332628954785245},{angle:.7853981633974483,size:.34115588214343917},{angle:.8028514559173915,size:0},{angle:.8203047484373349,size:0},{angle:.8377580409572781,size:.5593400040413774},{angle:.8552113334772213,size:.2033100660342137},{angle:.8726646259971648,size:0},{angle:.890117918517108,size:.5091817435737851},{angle:.9075712110370513,size:.279445568305137},{angle:.9250245035569946,size:.20767293459237582},{angle:.9424777960769379,size:.21286087516612376},{angle:.9599310885968813,size:.2725735644590326},{angle:.9773843811168246,size:.46298636488105377},{angle:.9948376736367678,size:.41238315415937515},{angle:1.0122909661567112,size:0},{angle:1.0297442586766543,size:0},{angle:1.0471975511965976,size:0},{angle:1.064650843716541,size:0},{angle:1.0821041362364843,size:0},{angle:1.0995574287564276,size:.20485472246876976},{angle:1.117010721276371,size:.3052714854016909},{angle:1.1344640137963142,size:.20607768685630748},{angle:1.1519173063162573,size:.3276447266845562},{angle:1.1693705988362009,size:.28664182476391264},{angle:1.186823891356144,size:0},{angle:1.2042771838760875,size:.20123843031687727},{angle:1.2217304763960306,size:.6548549401786197},{angle:1.239183768915974,size:.2296486641966379},{angle:1.2566370614359172,size:0},{angle:1.2740903539558606,size:0},{angle:1.2915436464758039,size:.3028221310953204},{angle:1.3089969389957472,size:.20635785523064312},{angle:1.3264502315156905,size:.3572536461573111},{angle:1.3439035240356336,size:.2598928499974735},{angle:1.3613568165555772,size:0},{angle:1.3788101090755203,size:.2094684164364703},{angle:1.3962634015954636,size:.3360749168526257},{angle:1.413716694115407,size:0},{angle:1.4311699866353502,size:.32475263917391867},{angle:1.4486232791552935,size:.5399755074264703},{angle:1.4660765716752369,size:.2644556748669187},{angle:1.48352986419518,size:.34449155662616326},{angle:1.5009831567151235,size:0},{angle:1.5184364492350666,size:.5685198037288627},{angle:1.53588974175501,size:0},{angle:1.5533430342749532,size:.20889344877506363},{angle:1.5707963267948966,size:.285347548051893},{angle:1.5882496193148399,size:0},{angle:1.605702911834783,size:.23292373033793978},{angle:1.6231562043547265,size:.25986031187936487},{angle:1.6406094968746698,size:.2559482264281531},{angle:1.6580627893946132,size:.3447768560720649},{angle:1.6755160819145563,size:.22484393149110152},{angle:1.6929693744344996,size:.22594026543664494},{angle:1.7104226669544427,size:.2073252363813488},{angle:1.7278759594743864,size:.5489029803336072},{angle:1.7453292519943295,size:0},{angle:1.7627825445142729,size:.280466955003097},{angle:1.780235837034216,size:.5137446035690201},{angle:1.7976891295541593,size:0},{angle:1.8151424220741026,size:.43845983704991326},{angle:1.8325957145940461,size:.20304403238592938},{angle:1.8500490071139892,size:.4073492348853144},{angle:1.8675022996339325,size:.219470131281173},{angle:1.8849555921538759,size:.45660256406005684},{angle:1.902408884673819,size:.3393399144640195},{angle:1.9198621771937625,size:.276550419773976},{angle:1.9373154697137058,size:.5320105900684261},{angle:1.9547687622336491,size:.6155269654771666},{angle:1.9722220547535922,size:.22419474338943196},{angle:1.9896753472735356,size:.23804382078153533},{angle:2.007128639793479,size:0},{angle:2.0245819323134224,size:.3633540999326592},{angle:2.0420352248333655,size:0},{angle:2.0594885173533086,size:.41460769244944395},{angle:2.076941809873252,size:.6286936990049774},{angle:2.0943951023931953,size:.200739905476094},{angle:2.111848394913139,size:.2193856614849951},{angle:2.129301687433082,size:.5174703835812356},{angle:2.1467549799530254,size:.43823170630040026},{angle:2.1642082724729685,size:.20016208053109044},{angle:2.1816615649929116,size:0},{angle:2.199114857512855,size:.2438752390230925},{angle:2.2165681500327987,size:.558839847177411},{angle:2.234021442552742,size:.5058161307652331},{angle:2.251474735072685,size:.4163416134868366},{angle:2.2689280275926285,size:.3927452602366039},{angle:2.2863813201125716,size:.22048433415322144},{angle:2.3038346126325147,size:0},{angle:2.321287905152458,size:.38325379506961654},{angle:2.3387411976724017,size:.26996156219160933},{angle:2.356194490192345,size:.24570140652714895},{angle:2.373647782712288,size:.4318682468411399},{angle:2.3911010752322315,size:0},{angle:2.408554367752175,size:.36303356973789147},{angle:2.426007660272118,size:0},{angle:2.443460952792061,size:0},{angle:2.4609142453120048,size:.2034206921689368},{angle:2.478367537831948,size:.3378753953823601},{angle:2.495820830351891,size:0},{angle:2.5132741228718345,size:.20946355100653166},{angle:2.530727415391778,size:0},{angle:2.548180707911721,size:.2010267555449919},{angle:2.5656340004316642,size:0},{angle:2.5830872929516078,size:.24585312769394696},{angle:2.600540585471551,size:.2678216009873836},{angle:2.6179938779914944,size:0},{angle:2.6354471705114375,size:.37886445683552594},{angle:2.652900463031381,size:0},{angle:2.670353755551324,size:.4207804712656875},{angle:2.6878070480712672,size:.3661765638604485},{angle:2.705260340591211,size:.23332090131822605},{angle:2.7227136331111543,size:.29260827281303475},{angle:2.7401669256310974,size:0},{angle:2.7576202181510405,size:0},{angle:2.7750735106709836,size:.2406869915993585},{angle:2.792526803190927,size:.4853579770327698},{angle:2.8099800957108707,size:0},{angle:2.827433388230814,size:0},{angle:2.844886680750757,size:.30911318272910954},{angle:2.8623399732707004,size:.24276346331431295},{angle:2.8797932657906435,size:.4074278930841708},{angle:2.897246558310587,size:0},{angle:2.91469985083053,size:0},{angle:2.9321531433504737,size:0},{angle:2.949606435870417,size:.5034771448783059},{angle:2.96705972839036,size:0},{angle:2.9845130209103035,size:.47671083061745534},{angle:3.001966313430247,size:.2954476419863419},{angle:3.01941960595019,size:.34721182293920017},{angle:3.036872898470133,size:0},{angle:3.0543261909900767,size:.33634281965249424},{angle:3.07177948351002,size:0},{angle:3.089232776029963,size:.3969744022758298},{angle:3.1066860685499065,size:.45035023963380594},{angle:3.12413936106985,size:0},{angle:3.141592653589793,size:.20123624256656963},{angle:3.159045946109736,size:.3409197476453387},{angle:3.1764992386296798,size:0},{angle:3.193952531149623,size:.2390378545693296},{angle:3.211405823669566,size:.4217427260554771},{angle:3.2288591161895095,size:.2321428378715548},{angle:3.246312408709453,size:.5108775192826629},{angle:3.2637657012293966,size:0},{angle:3.2812189937493397,size:.24830209118581767},{angle:3.2986722862692828,size:.4285043784690051},{angle:3.3161255787892263,size:0},{angle:3.3335788713091694,size:.41615345101647594},{angle:3.3510321638291125,size:.3443457167465501},{angle:3.368485456349056,size:.24278181910967297},{angle:3.385938748868999,size:.3170954028710607},{angle:3.4033920413889422,size:0},{angle:3.4208453339088853,size:.5762959264864541},{angle:3.4382986264288293,size:.4570336120259396},{angle:3.455751918948773,size:.24428631116492075},{angle:3.473205211468716,size:.21924143717137337},{angle:3.490658503988659,size:.25603848888915326},{angle:3.5081117965086026,size:.2719205421132959},{angle:3.5255650890285457,size:.3438426899001237},{angle:3.543018381548489,size:.21720854515049806},{angle:3.560471674068432,size:.33736501349723935},{angle:3.5779249665883754,size:.42428591127192306},{angle:3.5953782591083185,size:0},{angle:3.6128315516282616,size:.48794628528485784},{angle:3.630284844148205,size:0},{angle:3.647738136668149,size:0},{angle:3.6651914291880923,size:.5881284717282445},{angle:3.6826447217080354,size:0},{angle:3.7000980142279785,size:0},{angle:3.717551306747922,size:.4850921312265409},{angle:3.735004599267865,size:.25103285144303134},{angle:3.752457891787808,size:.28607749275975697},{angle:3.7699111843077517,size:.43242300153574686},{angle:3.787364476827695,size:.22208141764509584},{angle:3.804817769347638,size:.25299299895316874},{angle:3.8222710618675815,size:.2522146211017582},{angle:3.839724354387525,size:.5402465242948407},{angle:3.8571776469074686,size:.3571496842941953},{angle:3.8746309394274117,size:.47105064530199614},{angle:3.8920842319473548,size:.3510998272207881},{angle:3.9095375244672983,size:0},{angle:3.9269908169872414,size:.31137304702972385},{angle:3.9444441095071845,size:0},{angle:3.961897402027128,size:0},{angle:3.979350694547071,size:.5742732785644481},{angle:3.9968039870670142,size:.27293372321156956},{angle:4.014257279586958,size:.28094640267328636},{angle:4.031710572106902,size:.35734740177238444},{angle:4.049163864626845,size:0},{angle:4.066617157146788,size:.2037592858556903},{angle:4.084070449666731,size:.27965198692253335},{angle:4.101523742186674,size:0},{angle:4.118977034706617,size:.20244058497912834},{angle:4.136430327226561,size:0},{angle:4.153883619746504,size:0},{angle:4.171336912266447,size:.3550862726043248},{angle:4.1887902047863905,size:.27964599942794816},{angle:4.206243497306334,size:.26094683927319057},{angle:4.223696789826278,size:0},{angle:4.241150082346221,size:0},{angle:4.258603374866164,size:0},{angle:4.276056667386108,size:0},{angle:4.293509959906051,size:0},{angle:4.310963252425994,size:.21050224418624597},{angle:4.328416544945937,size:.20159105956083992},{angle:4.34586983746588,size:0},{angle:4.363323129985823,size:0},{angle:4.380776422505767,size:.2303200233080059},{angle:4.39822971502571,size:.28584907416913696},{angle:4.4156830075456535,size:.2702190630740686},{angle:4.4331363000655974,size:.2542534071769331},{angle:4.4505895925855405,size:.20590028943030392},{angle:4.468042885105484,size:.4935504132705553},{angle:4.485496177625427,size:.29690267122365893},{angle:4.50294947014537,size:.20580921471888527},{angle:4.520402762665314,size:.36441980896217085},{angle:4.537856055185257,size:0},{angle:4.5553093477052,size:0},{angle:4.572762640225143,size:.3899102794839669},{angle:4.590215932745086,size:.20125538999002457},{angle:4.607669225265029,size:.6263400863372552},{angle:4.625122517784973,size:0},{angle:4.642575810304916,size:0},{angle:4.66002910282486,size:0},{angle:4.6774823953448035,size:.20412933536875083},{angle:4.694935687864747,size:0},{angle:4.71238898038469,size:0},{angle:4.729842272904633,size:.21049088172464211},{angle:4.747295565424576,size:0},{angle:4.764748857944519,size:0},{angle:4.782202150464463,size:.43689909343537303},{angle:4.799655442984406,size:0},{angle:4.81710873550435,size:.3298382241770763},{angle:4.834562028024293,size:0},{angle:4.852015320544236,size:0},{angle:4.869468613064179,size:.286634468435369},{angle:4.886921905584122,size:.26747579680629896},{angle:4.9043751981040655,size:0},{angle:4.9218284906240095,size:.3521297980099919},{angle:4.939281783143953,size:.23127324951261863},{angle:4.956735075663896,size:.24965415339986494},{angle:4.974188368183839,size:.23172880439370955},{angle:4.991641660703782,size:0},{angle:5.009094953223726,size:.3679831345094222},{angle:5.026548245743669,size:.5470123408215705},{angle:5.044001538263612,size:.4530142685955189},{angle:5.061454830783556,size:0},{angle:5.078908123303499,size:.350315594587748},{angle:5.096361415823442,size:.20181768514230186},{angle:5.113814708343385,size:0},{angle:5.1312680008633285,size:.28487004445575653},{angle:5.148721293383272,size:.4102707998784525},{angle:5.1661745859032155,size:.3388659010142765},{angle:5.183627878423159,size:.33083986204933336},{angle:5.201081170943102,size:.3166212094297834},{angle:5.218534463463046,size:0},{angle:5.235987755982989,size:.20652480787107877},{angle:5.253441048502932,size:.3885527736251152},{angle:5.270894341022875,size:.20456614851743604},{angle:5.288347633542818,size:.4338995907399211},{angle:5.305800926062762,size:.30412699721363057},{angle:5.323254218582705,size:0},{angle:5.340707511102648,size:.419438754015243},{angle:5.358160803622591,size:0},{angle:5.3756140961425345,size:0},{angle:5.393067388662478,size:.42556206108576267},{angle:5.410520681182422,size:.25031055427765553},{angle:5.427973973702365,size:.35975108909375286},{angle:5.445427266222309,size:.22754830048197103},{angle:5.462880558742252,size:.2029808517797327},{angle:5.480333851262195,size:.32799674228923514},{angle:5.497787143782138,size:0},{angle:5.515240436302081,size:.3095980030407374},{angle:5.532693728822024,size:0},{angle:5.550147021341967,size:0},{angle:5.567600313861911,size:0},{angle:5.585053606381854,size:0},{angle:5.602506898901798,size:.22929725546430665},{angle:5.619960191421741,size:0},{angle:5.6374134839416845,size:0},{angle:5.654866776461628,size:.486297562894816},{angle:5.672320068981571,size:.3759204214445959},{angle:5.689773361501514,size:.20745644189726606},{angle:5.707226654021458,size:0},{angle:5.724679946541401,size:.26546917127540703},{angle:5.742133239061344,size:.23068229745925445},{angle:5.759586531581287,size:.20045367856022286},{angle:5.77703982410123,size:.3172715326653881},{angle:5.794493116621174,size:.5332522186868482},{angle:5.811946409141117,size:.3759996438500316},{angle:5.82939970166106,size:.2922710127994122},{angle:5.846852994181004,size:0},{angle:5.8643062867009474,size:0},{angle:5.8817595792208905,size:.28001225096002696},{angle:5.899212871740834,size:.23169987000640824},{angle:5.916666164260777,size:0},{angle:5.93411945678072,size:0},{angle:5.951572749300664,size:.2666549139221699},{angle:5.969026041820607,size:0},{angle:5.98647933434055,size:.2621717655706146},{angle:6.003932626860494,size:0},{angle:6.021385919380437,size:0},{angle:6.03883921190038,size:.20671180860826915},{angle:6.056292504420323,size:.30370381391874146},{angle:6.073745796940266,size:.41357741598512554},{angle:6.09119908946021,size:.420551183125432},{angle:6.1086523819801535,size:.3790235708916212},{angle:6.126105674500097,size:.24967080089513013},{angle:6.14355896702004,size:.5575536015801299},{angle:6.161012259539983,size:.430540594260699},{angle:6.178465552059926,size:.42226795702173153},{angle:6.19591884457987,size:.22644215957711042},{angle:6.213372137099813,size:.3890337434456625},{angle:6.230825429619757,size:.21837281600909064},{angle:6.2482787221397,size:.48856734220218423},{angle:6.265732014659643,size:0}],roughEdgeProbability:.3,roughEdgeShift:8,roughEdgePoints:360},zr={showRuler:!0,showFullRuler:!0,showCrossLine:!0,showDashLine:!0,showSideRuler:!0,showCurrentPositionText:!0},vr={svgPath:"M 0 -1 L 0.588 0.809 L -0.951 -0.309 L 0.951 -0.309 L -0.588 0.809 Z",drawStar:!0,starDiameter:14,starPositionY:0,scaleToSmallStar:!1,useImage:!1,imageUrl:"",imageWidth:10,imageHeight:10,keepAspectRatio:!0},yr={openSecurityPattern:!0,securityPatternWidth:.15,securityPatternLength:3,securityPatternCount:5,securityPatternAngleRange:40,securityPatternParams:[{angle:1.253382821001648,lineAngle:.5011688961751503},{angle:2.0261644738555464,lineAngle:-.4527134494869728},{angle:.8372465390781247,lineAngle:.8205784612614496},{angle:4.985211537991283,lineAngle:2.957635549111782},{angle:4.841439261858876,lineAngle:3.3253215871661475}]},br={companyName:"印章绘制有限责任公司",compression:1,borderOffset:1,textDistributionFactor:5,fontFamily:"Songti SC",fontHeight:4.2,fontWeight:400,shape:"ellipse",adjustEllipseText:!1,adjustEllipseTextFactor:.5,startAngle:0,rotateDirection:"clockwise"},Cr={code:"1234567890",compression:1,fontHeight:1.2,fontFamily:"Arial",borderOffset:1,fontWidth:1.2,textDistributionFactor:50,fontWeight:"normal"},wr=40,Sr=40,xr={stampType:"印章类型",fontHeight:4.6,fontFamily:"Arial",fontWidth:3,compression:.75,letterSpacing:0,positionY:-3,fontWeight:"normal",lineSpacing:2},Tr="#ff0000",Pr=1,Er=!1,_r=!1,Lr={code:"",compression:.7,fontHeight:3.7,fontFamily:"Arial",fontWidth:1.3,letterSpacing:8,positionY:0,totalWidth:26,fontWeight:"normal"},Fr={applyAging:!1,agingIntensity:50,agingEffectParams:[]},Ar={drawInnerCircle:!0,innerCircleLineWidth:.5,innerCircleLineRadiusX:16,innerCircleLineRadiusY:12},Mr={drawInnerCircle:!0,innerCircleLineWidth:.2,innerCircleLineRadiusX:36,innerCircleLineRadiusY:27},Rr=!1,Ir=[{stampType:"印章类型",fontHeight:4.6,fontFamily:"Arial",fontWidth:3,compression:.75,letterSpacing:1.55,positionY:.5,fontWeight:"normal",lineSpacing:2}],Ur=[{companyName:"绘制印章有限责任公司",compression:1,borderOffset:1,textDistributionFactor:3.7,fontFamily:"SimSun",fontHeight:4.2,fontWeight:"normal",shape:"ellipse",adjustEllipseText:!0,adjustEllipseTextFactor:.75}],Or=[],Wr={roughEdge:mr,ruler:zr,drawStar:vr,securityPattern:yr,company:br,stampCode:Cr,width:wr,height:Sr,stampType:xr,primaryColor:Tr,borderWidth:Pr,refreshSecurityPattern:Er,refreshOld:_r,taxNumber:Lr,agingEffect:Fr,innerCircle:Ar,outThinCircle:Mr,openManualAging:Rr,stampTypeList:Ir,companyList:Ur,innerCircleList:Or},Dr={drawRoughEdge:!1,roughEdgeWidth:.2,roughEdgeHeight:5,roughEdgeParams:[{angle:0,size:0},{angle:.017453292519943295,size:0},{angle:.03490658503988659,size:0},{angle:.05235987755982988,size:.31730287292758974},{angle:.06981317007977318,size:.3916444365989694},{angle:.08726646259971647,size:.208608754055621},{angle:.10471975511965977,size:.2201400602607466},{angle:.12217304763960307,size:0},{angle:.13962634015954636,size:.413218673378141},{angle:.15707963267948966,size:.2265863014473266},{angle:.17453292519943295,size:.24605542095631253},{angle:.19198621771937624,size:.23655514355059734},{angle:.20943951023931953,size:.253807375717143},{angle:.22689280275926282,size:0},{angle:.24434609527920614,size:.24330943667000401},{angle:.2617993877991494,size:.22953279555062572},{angle:.2792526803190927,size:.21392879792197558},{angle:.296705972839036,size:.2890854202057443},{angle:.3141592653589793,size:0},{angle:.33161255787892263,size:0},{angle:.3490658503988659,size:.20956234004730973},{angle:.3665191429188092,size:0},{angle:.3839724354387525,size:.4195584128674653},{angle:.40142572795869574,size:.28732832573605316},{angle:.41887902047863906,size:.47727796370752146},{angle:.4363323129985824,size:.4078196234676063},{angle:.45378560551852565,size:0},{angle:.47123889803846897,size:0},{angle:.4886921905584123,size:0},{angle:.5061454830783556,size:0},{angle:.5235987755982988,size:.23556530694644498},{angle:.5410520681182421,size:.2599537196239181},{angle:.5585053606381855,size:0},{angle:.5759586531581287,size:.22683369358645425},{angle:.593411945678072,size:.43941963242183213},{angle:.6108652381980153,size:.256981026413524},{angle:.6283185307179586,size:0},{angle:.6457718232379019,size:.4143289542013904},{angle:.6632251157578453,size:.2043975927300548},{angle:.6806784082777886,size:.2775738594178865},{angle:.6981317007977318,size:.45292165202456325},{angle:.7155849933176751,size:0},{angle:.7330382858376184,size:0},{angle:.7504915783575618,size:0},{angle:.767944870877505,size:.20332628954785245},{angle:.7853981633974483,size:.34115588214343917},{angle:.8028514559173915,size:0},{angle:.8203047484373349,size:0},{angle:.8377580409572781,size:.5593400040413774},{angle:.8552113334772213,size:.2033100660342137},{angle:.8726646259971648,size:0},{angle:.890117918517108,size:.5091817435737851},{angle:.9075712110370513,size:.279445568305137},{angle:.9250245035569946,size:.20767293459237582},{angle:.9424777960769379,size:.21286087516612376},{angle:.9599310885968813,size:.2725735644590326},{angle:.9773843811168246,size:.46298636488105377},{angle:.9948376736367678,size:.41238315415937515},{angle:1.0122909661567112,size:0},{angle:1.0297442586766543,size:0},{angle:1.0471975511965976,size:0},{angle:1.064650843716541,size:0},{angle:1.0821041362364843,size:0},{angle:1.0995574287564276,size:.20485472246876976},{angle:1.117010721276371,size:.3052714854016909},{angle:1.1344640137963142,size:.20607768685630748},{angle:1.1519173063162573,size:.3276447266845562},{angle:1.1693705988362009,size:.28664182476391264},{angle:1.186823891356144,size:0},{angle:1.2042771838760875,size:.20123843031687727},{angle:1.2217304763960306,size:.6548549401786197},{angle:1.239183768915974,size:.2296486641966379},{angle:1.2566370614359172,size:0},{angle:1.2740903539558606,size:0},{angle:1.2915436464758039,size:.3028221310953204},{angle:1.3089969389957472,size:.20635785523064312},{angle:1.3264502315156905,size:.3572536461573111},{angle:1.3439035240356336,size:.2598928499974735},{angle:1.3613568165555772,size:0},{angle:1.3788101090755203,size:.2094684164364703},{angle:1.3962634015954636,size:.3360749168526257},{angle:1.413716694115407,size:0},{angle:1.4311699866353502,size:.32475263917391867},{angle:1.4486232791552935,size:.5399755074264703},{angle:1.4660765716752369,size:.2644556748669187},{angle:1.48352986419518,size:.34449155662616326},{angle:1.5009831567151235,size:0},{angle:1.5184364492350666,size:.5685198037288627},{angle:1.53588974175501,size:0},{angle:1.5533430342749532,size:.20889344877506363},{angle:1.5707963267948966,size:.285347548051893},{angle:1.5882496193148399,size:0},{angle:1.605702911834783,size:.23292373033793978},{angle:1.6231562043547265,size:.25986031187936487},{angle:1.6406094968746698,size:.2559482264281531},{angle:1.6580627893946132,size:.3447768560720649},{angle:1.6755160819145563,size:.22484393149110152},{angle:1.6929693744344996,size:.22594026543664494},{angle:1.7104226669544427,size:.2073252363813488},{angle:1.7278759594743864,size:.5489029803336072},{angle:1.7453292519943295,size:0},{angle:1.7627825445142729,size:.280466955003097},{angle:1.780235837034216,size:.5137446035690201},{angle:1.7976891295541593,size:0},{angle:1.8151424220741026,size:.43845983704991326},{angle:1.8325957145940461,size:.20304403238592938},{angle:1.8500490071139892,size:.4073492348853144},{angle:1.8675022996339325,size:.219470131281173},{angle:1.8849555921538759,size:.45660256406005684},{angle:1.902408884673819,size:.3393399144640195},{angle:1.9198621771937625,size:.276550419773976},{angle:1.9373154697137058,size:.5320105900684261},{angle:1.9547687622336491,size:.6155269654771666},{angle:1.9722220547535922,size:.22419474338943196},{angle:1.9896753472735356,size:.23804382078153533},{angle:2.007128639793479,size:0},{angle:2.0245819323134224,size:.3633540999326592},{angle:2.0420352248333655,size:0},{angle:2.0594885173533086,size:.41460769244944395},{angle:2.076941809873252,size:.6286936990049774},{angle:2.0943951023931953,size:.200739905476094},{angle:2.111848394913139,size:.2193856614849951},{angle:2.129301687433082,size:.5174703835812356},{angle:2.1467549799530254,size:.43823170630040026},{angle:2.1642082724729685,size:.20016208053109044},{angle:2.1816615649929116,size:0},{angle:2.199114857512855,size:.2438752390230925},{angle:2.2165681500327987,size:.558839847177411},{angle:2.234021442552742,size:.5058161307652331},{angle:2.251474735072685,size:.4163416134868366},{angle:2.2689280275926285,size:.3927452602366039},{angle:2.2863813201125716,size:.22048433415322144},{angle:2.3038346126325147,size:0},{angle:2.321287905152458,size:.38325379506961654},{angle:2.3387411976724017,size:.26996156219160933},{angle:2.356194490192345,size:.24570140652714895},{angle:2.373647782712288,size:.4318682468411399},{angle:2.3911010752322315,size:0},{angle:2.408554367752175,size:.36303356973789147},{angle:2.426007660272118,size:0},{angle:2.443460952792061,size:0},{angle:2.4609142453120048,size:.2034206921689368},{angle:2.478367537831948,size:.3378753953823601},{angle:2.495820830351891,size:0},{angle:2.5132741228718345,size:.20946355100653166},{angle:2.530727415391778,size:0},{angle:2.548180707911721,size:.2010267555449919},{angle:2.5656340004316642,size:0},{angle:2.5830872929516078,size:.24585312769394696},{angle:2.600540585471551,size:.2678216009873836},{angle:2.6179938779914944,size:0},{angle:2.6354471705114375,size:.37886445683552594},{angle:2.652900463031381,size:0},{angle:2.670353755551324,size:.4207804712656875},{angle:2.6878070480712672,size:.3661765638604485},{angle:2.705260340591211,size:.23332090131822605},{angle:2.7227136331111543,size:.29260827281303475},{angle:2.7401669256310974,size:0},{angle:2.7576202181510405,size:0},{angle:2.7750735106709836,size:.2406869915993585},{angle:2.792526803190927,size:.4853579770327698},{angle:2.8099800957108707,size:0},{angle:2.827433388230814,size:0},{angle:2.844886680750757,size:.30911318272910954},{angle:2.8623399732707004,size:.24276346331431295},{angle:2.8797932657906435,size:.4074278930841708},{angle:2.897246558310587,size:0},{angle:2.91469985083053,size:0},{angle:2.9321531433504737,size:0},{angle:2.949606435870417,size:.5034771448783059},{angle:2.96705972839036,size:0},{angle:2.9845130209103035,size:.47671083061745534},{angle:3.001966313430247,size:.2954476419863419},{angle:3.01941960595019,size:.34721182293920017},{angle:3.036872898470133,size:0},{angle:3.0543261909900767,size:.33634281965249424},{angle:3.07177948351002,size:0},{angle:3.089232776029963,size:.3969744022758298},{angle:3.1066860685499065,size:.45035023963380594},{angle:3.12413936106985,size:0},{angle:3.141592653589793,size:.20123624256656963},{angle:3.159045946109736,size:.3409197476453387},{angle:3.1764992386296798,size:0},{angle:3.193952531149623,size:.2390378545693296},{angle:3.211405823669566,size:.4217427260554771},{angle:3.2288591161895095,size:.2321428378715548},{angle:3.246312408709453,size:.5108775192826629},{angle:3.2637657012293966,size:0},{angle:3.2812189937493397,size:.24830209118581767},{angle:3.2986722862692828,size:.4285043784690051},{angle:3.3161255787892263,size:0},{angle:3.3335788713091694,size:.41615345101647594},{angle:3.3510321638291125,size:.3443457167465501},{angle:3.368485456349056,size:.24278181910967297},{angle:3.385938748868999,size:.3170954028710607},{angle:3.4033920413889422,size:0},{angle:3.4208453339088853,size:.5762959264864541},{angle:3.4382986264288293,size:.4570336120259396},{angle:3.455751918948773,size:.24428631116492075},{angle:3.473205211468716,size:.21924143717137337},{angle:3.490658503988659,size:.25603848888915326},{angle:3.5081117965086026,size:.2719205421132959},{angle:3.5255650890285457,size:.3438426899001237},{angle:3.543018381548489,size:.21720854515049806},{angle:3.560471674068432,size:.33736501349723935},{angle:3.5779249665883754,size:.42428591127192306},{angle:3.5953782591083185,size:0},{angle:3.6128315516282616,size:.48794628528485784},{angle:3.630284844148205,size:0},{angle:3.647738136668149,size:0},{angle:3.6651914291880923,size:.5881284717282445},{angle:3.6826447217080354,size:0},{angle:3.7000980142279785,size:0},{angle:3.717551306747922,size:.4850921312265409},{angle:3.735004599267865,size:.25103285144303134},{angle:3.752457891787808,size:.28607749275975697},{angle:3.7699111843077517,size:.43242300153574686},{angle:3.787364476827695,size:.22208141764509584},{angle:3.804817769347638,size:.25299299895316874},{angle:3.8222710618675815,size:.2522146211017582},{angle:3.839724354387525,size:.5402465242948407},{angle:3.8571776469074686,size:.3571496842941953},{angle:3.8746309394274117,size:.47105064530199614},{angle:3.8920842319473548,size:.3510998272207881},{angle:3.9095375244672983,size:0},{angle:3.9269908169872414,size:.31137304702972385},{angle:3.9444441095071845,size:0},{angle:3.961897402027128,size:0},{angle:3.979350694547071,size:.5742732785644481},{angle:3.9968039870670142,size:.27293372321156956},{angle:4.014257279586958,size:.28094640267328636},{angle:4.031710572106902,size:.35734740177238444},{angle:4.049163864626845,size:0},{angle:4.066617157146788,size:.2037592858556903},{angle:4.084070449666731,size:.27965198692253335},{angle:4.101523742186674,size:0},{angle:4.118977034706617,size:.20244058497912834},{angle:4.136430327226561,size:0},{angle:4.153883619746504,size:0},{angle:4.171336912266447,size:.3550862726043248},{angle:4.1887902047863905,size:.27964599942794816},{angle:4.206243497306334,size:.26094683927319057},{angle:4.223696789826278,size:0},{angle:4.241150082346221,size:0},{angle:4.258603374866164,size:0},{angle:4.276056667386108,size:0},{angle:4.293509959906051,size:0},{angle:4.310963252425994,size:.21050224418624597},{angle:4.328416544945937,size:.20159105956083992},{angle:4.34586983746588,size:0},{angle:4.363323129985823,size:0},{angle:4.380776422505767,size:.2303200233080059},{angle:4.39822971502571,size:.28584907416913696},{angle:4.4156830075456535,size:.2702190630740686},{angle:4.4331363000655974,size:.2542534071769331},{angle:4.4505895925855405,size:.20590028943030392},{angle:4.468042885105484,size:.4935504132705553},{angle:4.485496177625427,size:.29690267122365893},{angle:4.50294947014537,size:.20580921471888527},{angle:4.520402762665314,size:.36441980896217085},{angle:4.537856055185257,size:0},{angle:4.5553093477052,size:0},{angle:4.572762640225143,size:.3899102794839669},{angle:4.590215932745086,size:.20125538999002457},{angle:4.607669225265029,size:.6263400863372552},{angle:4.625122517784973,size:0},{angle:4.642575810304916,size:0},{angle:4.66002910282486,size:0},{angle:4.6774823953448035,size:.20412933536875083},{angle:4.694935687864747,size:0},{angle:4.71238898038469,size:0},{angle:4.729842272904633,size:.21049088172464211},{angle:4.747295565424576,size:0},{angle:4.764748857944519,size:0},{angle:4.782202150464463,size:.43689909343537303},{angle:4.799655442984406,size:0},{angle:4.81710873550435,size:.3298382241770763},{angle:4.834562028024293,size:0},{angle:4.852015320544236,size:0},{angle:4.869468613064179,size:.286634468435369},{angle:4.886921905584122,size:.26747579680629896},{angle:4.9043751981040655,size:0},{angle:4.9218284906240095,size:.3521297980099919},{angle:4.939281783143953,size:.23127324951261863},{angle:4.956735075663896,size:.24965415339986494},{angle:4.974188368183839,size:.23172880439370955},{angle:4.991641660703782,size:0},{angle:5.009094953223726,size:.3679831345094222},{angle:5.026548245743669,size:.5470123408215705},{angle:5.044001538263612,size:.4530142685955189},{angle:5.061454830783556,size:0},{angle:5.078908123303499,size:.350315594587748},{angle:5.096361415823442,size:.20181768514230186},{angle:5.113814708343385,size:0},{angle:5.1312680008633285,size:.28487004445575653},{angle:5.148721293383272,size:.4102707998784525},{angle:5.1661745859032155,size:.3388659010142765},{angle:5.183627878423159,size:.33083986204933336},{angle:5.201081170943102,size:.3166212094297834},{angle:5.218534463463046,size:0},{angle:5.235987755982989,size:.20652480787107877},{angle:5.253441048502932,size:.3885527736251152},{angle:5.270894341022875,size:.20456614851743604},{angle:5.288347633542818,size:.4338995907399211},{angle:5.305800926062762,size:.30412699721363057},{angle:5.323254218582705,size:0},{angle:5.340707511102648,size:.419438754015243},{angle:5.358160803622591,size:0},{angle:5.3756140961425345,size:0},{angle:5.393067388662478,size:.42556206108576267},{angle:5.410520681182422,size:.25031055427765553},{angle:5.427973973702365,size:.35975108909375286},{angle:5.445427266222309,size:.22754830048197103},{angle:5.462880558742252,size:.2029808517797327},{angle:5.480333851262195,size:.32799674228923514},{angle:5.497787143782138,size:0},{angle:5.515240436302081,size:.3095980030407374},{angle:5.532693728822024,size:0},{angle:5.550147021341967,size:0},{angle:5.567600313861911,size:0},{angle:5.585053606381854,size:0},{angle:5.602506898901798,size:.22929725546430665},{angle:5.619960191421741,size:0},{angle:5.6374134839416845,size:0},{angle:5.654866776461628,size:.486297562894816},{angle:5.672320068981571,size:.3759204214445959},{angle:5.689773361501514,size:.20745644189726606},{angle:5.707226654021458,size:0},{angle:5.724679946541401,size:.26546917127540703},{angle:5.742133239061344,size:.23068229745925445},{angle:5.759586531581287,size:.20045367856022286},{angle:5.77703982410123,size:.3172715326653881},{angle:5.794493116621174,size:.5332522186868482},{angle:5.811946409141117,size:.3759996438500316},{angle:5.82939970166106,size:.2922710127994122},{angle:5.846852994181004,size:0},{angle:5.8643062867009474,size:0},{angle:5.8817595792208905,size:.28001225096002696},{angle:5.899212871740834,size:.23169987000640824},{angle:5.916666164260777,size:0},{angle:5.93411945678072,size:0},{angle:5.951572749300664,size:.2666549139221699},{angle:5.969026041820607,size:0},{angle:5.98647933434055,size:.2621717655706146},{angle:6.003932626860494,size:0},{angle:6.021385919380437,size:0},{angle:6.03883921190038,size:.20671180860826915},{angle:6.056292504420323,size:.30370381391874146},{angle:6.073745796940266,size:.41357741598512554},{angle:6.09119908946021,size:.420551183125432},{angle:6.1086523819801535,size:.3790235708916212},{angle:6.126105674500097,size:.24967080089513013},{angle:6.14355896702004,size:.5575536015801299},{angle:6.161012259539983,size:.430540594260699},{angle:6.178465552059926,size:.42226795702173153},{angle:6.19591884457987,size:.22644215957711042},{angle:6.213372137099813,size:.3890337434456625},{angle:6.230825429619757,size:.21837281600909064},{angle:6.2482787221397,size:.48856734220218423},{angle:6.265732014659643,size:0}],roughEdgeProbability:.3,roughEdgeShift:8,roughEdgePoints:360},Vr={showRuler:!0,showFullRuler:!0,showCrossLine:!0,showDashLine:!0,showSideRuler:!0,showCurrentPositionText:!0},Hr={svgPath:"M 0 -1 L 0.588 0.809 L -0.951 -0.309 L 0.951 -0.309 L -0.588 0.809 Z",drawStar:!1,starDiameter:14,starPositionY:0,scaleToSmallStar:!1,useImage:!1,imageUrl:"",imageWidth:10,imageHeight:10,keepAspectRatio:!0},Nr={openSecurityPattern:!0,securityPatternWidth:.15,securityPatternLength:3,securityPatternCount:5,securityPatternAngleRange:40,securityPatternParams:[{angle:1.253382821001648,lineAngle:.5011688961751503},{angle:2.0261644738555464,lineAngle:-.4527134494869728},{angle:.8372465390781247,lineAngle:.8205784612614496},{angle:4.985211537991283,lineAngle:2.957635549111782},{angle:4.841439261858876,lineAngle:3.3253215871661475}]},$r={companyName:"印章绘制有限责任公司",compression:1,borderOffset:1,textDistributionFactor:5,fontFamily:"Songti SC",fontHeight:4.2,fontWeight:400,shape:"ellipse",adjustEllipseText:!1,adjustEllipseTextFactor:.5,startAngle:0,rotateDirection:"clockwise"},kr={code:"",compression:1,fontHeight:1.2,fontFamily:"Arial",borderOffset:1,fontWidth:1.2,textDistributionFactor:50,fontWeight:"normal"},jr=40,Yr=40,Br={stampType:"印章类型",fontHeight:4.6,fontFamily:"Arial",fontWidth:3,compression:.75,letterSpacing:0,positionY:-3,fontWeight:"normal",lineSpacing:2},Kr="#ff0000",Xr=1,Gr=!1,qr=!1,Jr={code:"",compression:.7,fontHeight:3.7,fontFamily:"Arial",fontWidth:1.3,letterSpacing:8,positionY:0,totalWidth:26,fontWeight:"normal"},Zr={applyAging:!1,agingIntensity:50,agingEffectParams:[]},Qr={drawInnerCircle:!0,innerCircleLineWidth:.5,innerCircleLineRadiusX:16,innerCircleLineRadiusY:12},e2={drawInnerCircle:!0,innerCircleLineWidth:.2,innerCircleLineRadiusX:36,innerCircleLineRadiusY:27},t2=!1,n2=[{stampType:"MUMBAI",fontHeight:4.6,fontFamily:"Arial",fontWidth:3,compression:.75,letterSpacing:1.55,positionY:-9,fontWeight:"normal",lineSpacing:2}],s2=[{companyName:"MYSTAMP READYL AND SEAL GENERATOR",compression:1,borderOffset:1,textDistributionFactor:6.1,fontFamily:"SimSun",fontHeight:4.2,fontWeight:"normal",shape:"ellipse",adjustEllipseText:!1,adjustEllipseTextFactor:.75}],i2=[{drawInnerCircle:!0,innerCircleLineWidth:.5,innerCircleLineRadiusX:28,innerCircleLineRadiusY:28}],l2={roughEdge:Dr,ruler:Vr,drawStar:Hr,securityPattern:Nr,company:$r,stampCode:kr,width:jr,height:Yr,stampType:Br,primaryColor:Kr,borderWidth:Xr,refreshSecurityPattern:Gr,refreshOld:qr,taxNumber:Jr,agingEffect:Zr,innerCircle:Qr,outThinCircle:e2,openManualAging:t2,stampTypeList:n2,companyList:s2,innerCircleList:i2},o2={key:0,class:"legal-dialog-overlay"},a2={class:"container"},r2={class:"button-group",style:{position:"sticky",top:"0","z-index":"1000","background-color":"white",padding:"10px"}},u2={class:"control-group"},g2={class:"group-content"},c2={class:"checkbox-label"},f2={class:"control-group"},d2={class:"group-content"},p2={class:"company-header"},h2=["onClick"],m2=["onUpdate:modelValue"],z2={class:"font-input-group"},v2=["onUpdate:modelValue"],y2=["value"],b2=["onUpdate:modelValue"],C2=["onUpdate:modelValue"],w2=["onUpdate:modelValue"],S2=["onUpdate:modelValue"],x2=["onUpdate:modelValue"],T2=["onUpdate:modelValue"],P2={class:"checkbox-label"},E2=["onUpdate:modelValue"],_2={key:0},L2=["onUpdate:modelValue"],F2=["onUpdate:modelValue"],A2=["onUpdate:modelValue"],M2={class:"control-group"},R2={class:"group-content"},I2={class:"stamp-type-header"},U2=["onClick"],O2=["onUpdate:modelValue"],W2=["onUpdate:modelValue"],D2={class:"font-input-group"},V2=["onUpdate:modelValue"],H2={id:"stampTypeFontList"},N2=["value"],$2=["onUpdate:modelValue"],k2=["onUpdate:modelValue"],j2=["onUpdate:modelValue"],Y2=["onUpdate:modelValue"],B2={class:"control-group"},K2={class:"group-content"},X2={class:"font-input-group"},G2=["value"],q2={class:"control-group"},J2={class:"group-content"},Z2={class:"font-input-group"},Q2=["value"],e1={class:"control-group"},t1={class:"group-content"},n1={class:"image-list"},s1={class:"image-header"},i1=["onClick"],l1={key:0,class:"image-preview"},o1=["src"],a1=["onChange"],r1=["onUpdate:modelValue"],u1=["onUpdate:modelValue"],g1=["onUpdate:modelValue"],c1=["onUpdate:modelValue"],f1={class:"checkbox-label"},d1=["onUpdate:modelValue"],p1={class:"control-group"},h1={class:"group-content"},m1={class:"checkbox-label"},z1={key:0},v1={class:"control-group"},y1={class:"group-content"},b1={class:"control-group"},C1={class:"group-content"},w1={class:"checkbox-label"},S1={key:0},x1={key:1},T1={key:2},P1={key:3},E1={key:4},_1={class:"control-group"},L1={class:"group-content"},F1={class:"checkbox-label"},A1={class:"checkbox-label"},M1={key:0},R1={class:"control-group"},I1={class:"group-content"},U1={class:"inner-circle-header"},O1=["onClick"],W1=["onUpdate:modelValue"],D1=["onUpdate:modelValue"],V1=["onUpdate:modelValue"],H1={class:"canvas-container"},N1={style:{display:"flex","flex-direction":"row","margin-top":"12px",gap:"12px"}},$1={class:"control-group"},k1={class:"checkbox-label"},j1={class:"checkbox-label"},Y1={key:0},B1={class:"template-panel"},K1={class:"template-list"},X1={class:"template-category"},G1=["onClick"],q1={class:"template-preview"},J1=["src"],Z1={class:"template-info"},Q1={class:"template-name"},Ii=10,e5=fl({__name:"DrawStampUtilsDemo",setup(e){const t=_(null),n=_(null),s=_(!0),i=_("绘制印章有限责任公司"),l=_("1234567890123"),o=_("000000000000000000"),r=_("Songti SC"),g=_(4.2),h=_("SimSun"),f=_(1.2),m=_(1.2),z=_(20),x=_(1),R=_("#ff0000"),O=_(14),B=_(!1),D=_(!1),j=_(50),Y=_(3),U=_(!1),q=_(.5),pe=_(1),he=_(1),ve=_(20),Me=_("合同专用章"),Re=_("SimSun"),ke=_(4.6),Ft=_(3),st=_(0),it=_(0),kt=_(-5),me=_(1),le=_(400),Q=_(400),Ie=_(400),Ve=_("Songti SC"),Ue=_(400),Ae=_(1),lt=_(1),At=_(!0),ls=_(.5),je=_(.2),Mt=_("#FF0000"),ot=_(5),bt=_(2);_(!1);const He=_(!1),at=_(1),rt=_(.3),c=_(0),p=_(!0),v=_(.5),S=_(15),b=_(12),w=_(!0),L=_(.5),E=_(25),P=_(22);_(null);const C=_(!1),W=_(.2),A=_(5),I=_(.5),H=_(8),X=_(360),J=_(!1),G=_([{stampType:"印章类型",fontHeight:4.6,fontFamily:"SimSun",compression:.75,letterSpacing:0,positionY:-3,fontWeight:"normal",lineSpacing:2,fontWidth:3}]),ue=_([{companyName:"绘制印章有限责任公司",compression:1,borderOffset:1,textDistributionFactor:3,fontFamily:"SimSun",fontHeight:4.2,fontWeight:"normal",shape:"ellipse",adjustEllipseText:!1,adjustEllipseTextFactor:.5,startAngle:0,rotateDirection:"counterclockwise"}]),fe=_(!1),Te=_(10),ye=_(10),ut=_(!0),gt=_([{drawInnerCircle:!0,innerCircleLineWidth:.5,innerCircleLineRadiusX:36,innerCircleLineRadiusY:27},{drawInnerCircle:!0,innerCircleLineWidth:.5,innerCircleLineRadiusX:16,innerCircleLineRadiusY:12}]),Ce=_(null),ge=_([{imageUrl:"",imageWidth:10,imageHeight:10,positionX:0,positionY:0,keepAspectRatio:!0}]),Pn=()=>{console.log("add new image",ge.value),(ge.value===void 0||ge.value===null)&&(ge.value=[]),ge.value.length<10&&ge.value.push({imageUrl:"",imageWidth:10,imageHeight:10,positionX:0,positionY:0,keepAspectRatio:!0})},$l=y=>{ge.value.splice(y,1)},kl=()=>{const y=Ct.getDrawConfigs(),a=JSON.stringify(y,null,2),d=new Blob([a],{type:"application/json"}),N=URL.createObjectURL(d),T=document.createElement("a");T.href=N,T.download="印章模板.json",document.body.appendChild(T),T.click(),document.body.removeChild(T),URL.revokeObjectURL(N)},jl=()=>{var y;(y=Ce.value)==null||y.click()},Yl=y=>{const a=y.target;if(a.files&&a.files[0]){const d=a.files[0],N=new FileReader;N.onload=T=>{var ce;try{if((ce=T.target)!=null&&ce.result){const Pe=T.target.result,ft=JSON.parse(Pe);Ct.setDrawConfigs(ft),as(),ct()}}catch(Pe){console.error("加载模板失败:",Pe),alert("加载模板失败，请确保文件格式正确")}},N.readAsText(d)}a.value=""},Bl=(y,a)=>{const d=y.target;if(d.files&&d.files[0]){const N=d.files[0],T=new FileReader;T.onload=ce=>{var Pe;(Pe=ce.target)!=null&&Pe.result&&(ge.value[a].imageUrl=ce.target.result,ct())},T.readAsDataURL(N)}},Kl=()=>{let y=-3;if(G.value.length>0){const a=G.value[G.value.length-1];y=a.positionY+a.fontHeight}G.value.push({stampType:"新印章类型",fontHeight:4,fontFamily:"SimSun",compression:.75,letterSpacing:0,positionY:y,fontWeight:"normal",lineSpacing:2,fontWidth:3})},Xl=y=>{G.value.splice(y,1)},Gl=()=>{let y=1;if(ue.value.length>0){const a=ue.value[ue.value.length-1];y=a.borderOffset+a.fontHeight}ue.value.push({companyName:"新公司名称",compression:1,borderOffset:y,textDistributionFactor:3,fontFamily:"SimSun",fontHeight:4.2,fontWeight:"normal",shape:"ellipse",adjustEllipseText:!1,adjustEllipseTextFactor:.5,startAngle:0,rotateDirection:"counterclockwise"})},ql=y=>{ue.value.splice(y,1)},Jl=()=>{J.value=!0},nn=_(40),sn=_(30),os=_(1.2);let Ct;const Zl=()=>{Ct=new Ri(n.value,Ii)},ct=(y=!1,a=!1,d=!1)=>{Ct.refreshStamp(y,a,d)},Ql=()=>{gt.value.push({drawInnerCircle:!0,innerCircleLineWidth:.5,innerCircleLineRadiusX:16,innerCircleLineRadiusY:12})},eo=y=>{gt.value.splice(y,1)},to=()=>{const y=Ct.getDrawConfigs(),a=y.agingEffect;a.applyAging=B.value,a.agingIntensity=j.value,y.openManualAging=D.value;const d=y.securityPattern;d.openSecurityPattern=At.value,d.securityPatternCount=ot.value,d.securityPatternWidth=je.value,d.securityPatternLength=bt.value;const N=y.company;N.companyName=i.value,N.textDistributionFactor=Y.value,N.borderOffset=pe.value,N.fontHeight=g.value,N.fontFamily=r.value,N.compression=me.value,N.fontWeight=le.value,N.adjustEllipseText=U.value,N.adjustEllipseTextFactor=q.value;const T=y.taxNumber;T.code=o.value,T.compression=at.value,T.positionY=c.value,T.letterSpacing=rt.value,T.fontFamily=Ve.value,T.fontWeight=Ue.value;const ce=y.stampType;ce.stampType=Me.value,ce.fontFamily=Re.value,ce.fontHeight=ke.value,ce.fontWidth=Ft.value,ce.letterSpacing=st.value,ce.positionY=kt.value,ce.compression=Ae.value,ce.fontWeight=Q.value,ce.lineSpacing=os.value;const Pe=y.stampCode;Pe.code=l.value,Pe.compression=lt.value,Pe.fontFamily=h.value,Pe.fontHeight=f.value,Pe.fontWidth=m.value,Pe.borderOffset=he.value,Pe.textDistributionFactor=ve.value,Pe.fontWeight=Ie.value,y.primaryColor=R.value,y.borderWidth=x.value,y.width=nn.value,y.height=sn.value;const ft=y.drawStar;ft.drawStar=He.value,ft.useImage=fe.value,ft.imageWidth=Te.value,ft.imageHeight=ye.value,ft.keepAspectRatio=ut.value,ft.starDiameter=O.value,ft.starPositionY=it.value;const jt=y.roughEdge;jt.drawRoughEdge=C.value,jt.roughEdgeWidth=W.value,jt.roughEdgeHeight=A.value,jt.roughEdgeProbability=I.value,jt.roughEdgeShift=H.value,jt.roughEdgePoints=X.value;const En=y.innerCircle;En.drawInnerCircle=p.value,En.innerCircleLineWidth=v.value,En.innerCircleLineRadiusX=S.value,En.innerCircleLineRadiusY=b.value;const _n=y.outThinCircle;_n.drawInnerCircle=w.value,_n.innerCircleLineWidth=L.value,_n.innerCircleLineRadiusX=E.value,_n.innerCircleLineRadiusY=P.value,y.stampTypeList=G.value,y.companyList=ue.value,y.innerCircleList=gt.value,y.imageList=ge.value,ct()},no=()=>{J.value=!1},so=()=>{J.value=!1,Ct.saveStampAsPNG(512)},as=()=>{const y=Ct.getDrawConfigs();B.value=y.agingEffect.applyAging,j.value=y.agingEffect.agingIntensity,D.value=y.openManualAging,At.value=y.securityPattern.openSecurityPattern,ot.value=y.securityPattern.securityPatternCount,je.value=y.securityPattern.securityPatternWidth,bt.value=y.securityPattern.securityPatternLength,C.value=y.roughEdge.drawRoughEdge,W.value=y.roughEdge.roughEdgeWidth,A.value=y.roughEdge.roughEdgeHeight,I.value=y.roughEdge.roughEdgeProbability,H.value=y.roughEdge.roughEdgeShift,X.value=y.roughEdge.roughEdgePoints,nn.value=y.width,sn.value=y.height,x.value=y.borderWidth,R.value=y.primaryColor,i.value=y.company.companyName,g.value=y.company.fontHeight,me.value=y.company.compression,Y.value=y.company.textDistributionFactor,pe.value=y.company.borderOffset,ue.value=y.companyList;const a=y.stampCode;l.value=a.code,f.value=a.fontHeight,m.value=a.fontWidth,ve.value=a.textDistributionFactor,he.value=a.borderOffset,h.value=a.fontFamily,Ie.value=a.fontWeight,lt.value=a.compression;const d=y.taxNumber;o.value=d.code,at.value=d.compression,rt.value=d.letterSpacing,c.value=d.positionY,Ve.value=d.fontFamily,Ue.value=d.fontWeight;const N=y.stampType;Me.value=N.stampType,ke.value=N.fontHeight,Ft.value=N.fontWidth,st.value=N.letterSpacing,kt.value=N.positionY,Re.value=N.fontFamily,Q.value=N.fontWeight,Ae.value=N.compression,os.value=N.lineSpacing,G.value=y.stampTypeList,He.value=y.drawStar.drawStar,fe.value=y.drawStar.useImage,Te.value=y.drawStar.imageWidth,ye.value=y.drawStar.imageHeight,ut.value=y.drawStar.keepAspectRatio,O.value=y.drawStar.starDiameter,it.value=y.drawStar.starPositionY,p.value=y.innerCircle.drawInnerCircle,v.value=y.innerCircle.innerCircleLineWidth,S.value=y.innerCircle.innerCircleLineRadiusX,b.value=y.innerCircle.innerCircleLineRadiusY,gt.value=y.innerCircleList,w.value=y.outThinCircle.drawInnerCircle,L.value=y.outThinCircle.innerCircleLineWidth,E.value=y.outThinCircle.innerCircleLineRadiusX,P.value=y.outThinCircle.innerCircleLineRadiusY,ge.value=y.imageList||[]},ln=_([]),io=async()=>{ln.value=await B0()};Wn(async()=>{await io(),Zl(),as(),ct(),document.querySelectorAll(".font-select, .font-input").forEach(y=>{y instanceof HTMLElement&&Rt({target:y})})}),pn([i,r,l,g,f,z,x,R,O,ve,Y,pe,he,j,Me,Re,ke,st,kt,o,B,j,me,Ae,lt,st,Mt,ls,Mt,At,ot,bt,je,nn,sn,He,it,at,Ve,rt,c,O,p,v,S,b,L,E,P,w,D,C,W,A,I,H,X,le,Q,Ie,h,Ue,U,q,os,G,ue,fe,Te,ye,ut,gt,ge],()=>{to()},{deep:!0});const rs=_([{id:"contract",name:"合同专用章",text:"合同专用章",fontSize:4.6,letterSpacing:0,lineSpacing:1.2,positionY:-5,compression:1},{id:"invoice",name:"印章类型",text:`发票专章
增值税专用`,fontSize:4.2,letterSpacing:0,lineSpacing:1.5,positionY:-4,compression:.9},{id:"finance",name:"财务专用章",text:`财务专用章
仅限报销使用`,fontSize:4,letterSpacing:0,lineSpacing:1.8,positionY:-3,compression:.85}]),lo=()=>{localStorage.setItem("stampTypePresets",JSON.stringify(rs.value))},oo=()=>{const y=localStorage.getItem("stampTypePresets");y&&(rs.value=JSON.parse(y))};Wn(()=>{oo()}),pn(rs,()=>{lo()},{deep:!0});const ao=()=>{window.open("https://xxss0903.github.io/extractstamp/","_blank")},Rt=y=>{var N,T;const a=y.target,d=(a.tagName==="SELECT",a.value);if(a.style.setProperty("--current-font",d),a.tagName==="SELECT"){const ce=(N=a.parentElement)==null?void 0:N.querySelector(".font-input");ce&&(ce.value=d,ce.style.setProperty("--current-font",d))}if(a.tagName==="INPUT"){const ce=(T=a.parentElement)==null?void 0:T.querySelector(".font-select");ce&&(ce.value=d,ce.style.setProperty("--current-font",d))}},re=_({basic:!1,company:!1,stampType:!1,code:!1,taxNumber:!1,star:!1,security:!1,roughEdge:!1,aging:!1,innerCircle:!1,images:!1}),Ye=y=>{re.value[y]=!re.value[y]},Zs=_(-1),ro=async()=>{prompt("请输入模板名称")&&go()},uo=y=>{try{const a=JSON.parse(JSON.stringify(y.config));a.ruler.showRuler=!0,a.ruler.showFullRuler=!0,a.ruler.showSideRuler=!0,a.ruler.showCrossLine=!0,a.ruler.showCurrentPositionText=!0,a.ruler.showDashLine=!0,a.company.startAngle=y.config.company.startAngle,a.company.rotateDirection=y.config.company.rotateDirection,console.log("load template",y,a),Ct.setDrawConfigs(a),as(),ct(),Zs.value=-1-us.findIndex(d=>d===y)}catch(a){console.error("加载默认模板失败:",a),alert("加载默认模板失败")}},go=()=>{localStorage.setItem("stampTemplates",JSON.stringify(templateList.value))},co=()=>{us.forEach(async y=>{const a=document.createElement("canvas");a.width=400,a.height=400;const d=new Ri(a,Ii);y.config.ruler.showRuler=!1,d.setDrawConfigs(y.config),d.refreshStamp(),y.preview=a.toDataURL("image/png")})};Wn(()=>{co()});const us=[{name:"合同印章",preview:"",config:hr},{name:"公司印章1",preview:"",config:Wr},{name:"公司印章2",preview:"",config:l2}];return(y,a)=>(Z(),ne(ze,null,[J.value?(Z(),ne("div",o2,[u("div",{class:"legal-dialog"},[a[54]||(a[54]=u("h3",null,"⚠️ 法律提示",-1)),a[55]||(a[55]=u("div",{class:"legal-content"},[u("p",null,[u("strong",null,"请确认您已知悉并同意以下内容：")]),u("ol",null,[u("li",null,"本工具仅供学习和技术研究使用"),u("li",null,"使用本工具生成的任何图片请勿用于任何非法用途"),u("li",null,"因违法使用本工具造成的任何法律责任和损失，需自行承担"),u("li",null,"如果使用本工具请遵守相关法律法规")])],-1)),u("div",{class:"dialog-buttons"},[u("button",{onClick:no,class:"cancel-button"},"取消"),u("button",{onClick:so,class:"confirm-button"},"我已知悉并同意")])])])):Be("",!0),u("div",a2,[a[136]||(a[136]=u("div",{class:"legal-disclaimer"},[u("div",{class:"disclaimer-content"},[u("div",{class:"warning-icon"},"⚠️"),u("div",{class:"warning-text"},[u("h3",null,"安全警告"),u("p",null,[u("strong",null,"本项目仅供学习和参考！严禁用于任何非法用途！")]),u("p",null,[M(" 1. 本项目开源代码仅用于技术学习和交流。"),u("br"),M(" 2. 使用本项目生成的任何图片请勿用于任何非法用途。"),u("br"),M(" 3. 因违法使用本项目造成的任何法律责任和损失，需自行承担，与本项目无关。"),u("br"),M(" 4. 如果使用本项目请遵守相关法律法规。 ")])])])],-1)),u("div",{class:"editor-controls",ref_key:"editorControls",ref:t},[u("div",r2,[u("button",{onClick:Jl},"保存印章"),u("button",{onClick:kl},"保存模板"),u("input",{type:"file",ref_key:"templateFileInput",ref:Ce,style:{display:"none"},accept:".json",onChange:Yl},null,544),u("button",{onClick:jl},"加载模板")]),u("div",u2,[u("div",{class:"group-header",onClick:a[0]||(a[0]=d=>Ye("basic"))},[a[56]||(a[56]=u("h3",null,"印章基本设置",-1)),u("span",{class:_e(["expand-icon",{expanded:re.value.basic}])},"▼",2)]),F(u("div",g2,[u("label",c2,[F(u("input",{type:"checkbox","onUpdate:modelValue":a[1]||(a[1]=d=>s.value=d)},null,512),[[qe,s.value]]),a[57]||(a[57]=M(" 提取圆形印章 "))]),u("label",null,[a[58]||(a[58]=M("印章宽度 (mm): ")),F(u("input",{type:"number","onUpdate:modelValue":a[2]||(a[2]=d=>nn.value=d),min:"1",max:"50",step:"1"},null,512),[[k,nn.value,void 0,{number:!0}]])]),u("label",null,[a[59]||(a[59]=M("印章高度 (mm): ")),F(u("input",{type:"number","onUpdate:modelValue":a[3]||(a[3]=d=>sn.value=d),min:"1",max:"50",step:"1"},null,512),[[k,sn.value,void 0,{number:!0}]])]),u("label",null,[a[60]||(a[60]=M("圆形边框宽度 (mm): ")),F(u("input",{type:"number",step:"0.1","onUpdate:modelValue":a[4]||(a[4]=d=>x.value=d)},null,512),[[k,x.value,void 0,{number:!0}]])]),u("label",null,[a[61]||(a[61]=M("印章颜色: ")),F(u("input",{type:"color","onUpdate:modelValue":a[5]||(a[5]=d=>R.value=d)},null,512),[[k,R.value]])])],512),[[Ke,re.value.basic]])]),u("div",f2,[u("div",{class:"group-header",onClick:a[6]||(a[6]=d=>Ye("company"))},[a[62]||(a[62]=u("h3",null,"公司名称列表设置",-1)),u("span",{class:_e(["expand-icon",{expanded:re.value.company}])},"▼",2)]),F(u("div",d2,[(Z(!0),ne(ze,null,pt(ue.value,(d,N)=>(Z(),ne("div",{key:N,class:"company-item"},[u("div",p2,[u("span",null,"第 "+oe(N+1)+" 行",1),u("button",{class:"small-button delete-button",onClick:T=>ql(N)},"删除",8,h2)]),u("label",null,[a[63]||(a[63]=M(" 公司名称: ")),F(u("input",{type:"text","onUpdate:modelValue":T=>d.companyName=T},null,8,m2),[[k,d.companyName]])]),u("label",null,[a[64]||(a[64]=M(" 字体: ")),u("div",z2,[F(u("select",{"onUpdate:modelValue":T=>d.fontFamily=T,class:"font-select",onChange:Rt},[(Z(!0),ne(ze,null,pt(ln.value,T=>(Z(),ne("option",{key:T,value:T,style:Gt({fontFamily:T})},oe(T),13,y2))),128))],40,v2),[[St,d.fontFamily]]),F(u("input",{type:"text","onUpdate:modelValue":T=>d.fontFamily=T,class:"font-input",onInput:Rt,placeholder:"输入字体名称"},null,40,b2),[[k,d.fontFamily]])])]),u("label",null,[a[65]||(a[65]=M(" 字体大小 (mm): ")),F(u("input",{type:"number","onUpdate:modelValue":T=>d.fontHeight=T,min:"1",max:"10",step:"0.1"},null,8,C2),[[k,d.fontHeight,void 0,{number:!0}]])]),u("label",null,[a[67]||(a[67]=M(" 字体粗细: ")),F(u("select",{"onUpdate:modelValue":T=>d.fontWeight=T},a[66]||(a[66]=[Un('<option value="normal">正常</option><option value="bold">粗体</option><option value="100">100</option><option value="200">200</option><option value="300">300</option><option value="400">400</option><option value="500">500</option><option value="600">600</option><option value="700">700</option><option value="800">800</option><option value="900">900</option>',11)]),8,w2),[[St,d.fontWeight]])]),u("label",null,[a[68]||(a[68]=M(" 压缩比例: ")),F(u("input",{type:"range","onUpdate:modelValue":T=>d.compression=T,min:"0.5",max:"1.5",step:"0.05"},null,8,S2),[[k,d.compression,void 0,{number:!0}]]),u("span",null,oe(d.compression.toFixed(2)),1)]),u("label",null,[a[69]||(a[69]=M(" 分布因子: ")),F(u("input",{type:"range","onUpdate:modelValue":T=>d.textDistributionFactor=T,min:"0",max:"50",step:"0.1"},null,8,x2),[[k,d.textDistributionFactor,void 0,{number:!0}]]),u("span",null,oe(d.textDistributionFactor.toFixed(2)),1)]),u("label",null,[a[70]||(a[70]=M(" 边距 (mm): ")),F(u("input",{type:"number","onUpdate:modelValue":T=>d.borderOffset=T,min:"-10",max:"10",step:"0.05"},null,8,T2),[[k,d.borderOffset,void 0,{number:!0}]])]),u("label",P2,[F(u("input",{type:"checkbox","onUpdate:modelValue":T=>d.adjustEllipseText=T},null,8,E2),[[qe,d.adjustEllipseText]]),a[71]||(a[71]=M(" 调整椭圆文字 "))]),d.adjustEllipseText?(Z(),ne("label",_2,[a[72]||(a[72]=M(" 椭圆文字调整: ")),F(u("input",{type:"range","onUpdate:modelValue":T=>d.adjustEllipseTextFactor=T,min:"0",max:"2",step:"0.01"},null,8,L2),[[k,d.adjustEllipseTextFactor,void 0,{number:!0}]]),u("span",null,oe(d.adjustEllipseTextFactor.toFixed(2)),1)])):Be("",!0),u("label",null,[a[73]||(a[73]=M(" 开始角度: ")),F(u("input",{type:"range","onUpdate:modelValue":T=>d.startAngle=T,min:"-6.5",max:"6.5",step:"0.01"},null,8,F2),[[k,d.startAngle,void 0,{number:!0}]]),u("span",null,oe(d.startAngle?(d.startAngle*180/Math.PI).toFixed(0):0)+"°",1)]),u("label",null,[a[75]||(a[75]=M(" 旋转方向: ")),F(u("select",{"onUpdate:modelValue":T=>d.rotateDirection=T},a[74]||(a[74]=[u("option",{value:"clockwise"},"顺时针",-1),u("option",{value:"counterclockwise"},"逆时针",-1)]),8,A2),[[St,d.rotateDirection]])])]))),128)),u("button",{class:"add-button",onClick:Gl},"添加新行")],512),[[Ke,re.value.company]])]),u("div",M2,[u("div",{class:"group-header",onClick:a[7]||(a[7]=d=>Ye("stampType"))},[a[76]||(a[76]=u("h3",null,"印章类型列表设置",-1)),u("span",{class:_e(["expand-icon",{expanded:re.value.stampType}])},"▼",2)]),F(u("div",R2,[(Z(!0),ne(ze,null,pt(G.value,(d,N)=>(Z(),ne("div",{key:N,class:"stamp-type-item"},[u("div",I2,[u("span",null,"第 "+oe(N+1)+" 行",1),u("button",{class:"small-button delete-button",onClick:T=>Xl(N)},"删除",8,U2)]),u("label",null,[a[77]||(a[77]=M(" 文字内容: ")),F(u("input",{type:"text","onUpdate:modelValue":T=>d.stampType=T},null,8,O2),[[k,d.stampType]])]),u("label",null,[a[78]||(a[78]=M(" 字体大小 (mm): ")),F(u("input",{type:"number","onUpdate:modelValue":T=>d.fontHeight=T,min:"1",max:"10",step:"0.1"},null,8,W2),[[k,d.fontHeight,void 0,{number:!0}]])]),u("label",null,[a[79]||(a[79]=M(" 字体: ")),u("div",D2,[F(u("input",{type:"text","onUpdate:modelValue":T=>d.fontFamily=T,list:"stampTypeFontList",class:"font-input"},null,8,V2),[[k,d.fontFamily]]),u("datalist",H2,[(Z(!0),ne(ze,null,pt(ln.value,T=>(Z(),ne("option",{key:T,value:T},oe(T),9,N2))),128))])])]),u("label",null,[a[81]||(a[81]=M(" 字体粗细: ")),F(u("select",{"onUpdate:modelValue":T=>d.fontWeight=T},a[80]||(a[80]=[Un('<option value="normal">正常</option><option value="bold">粗体</option><option value="100">100</option><option value="200">200</option><option value="300">300</option><option value="400">400</option><option value="500">500</option><option value="600">600</option><option value="700">700</option><option value="800">800</option><option value="900">900</option>',11)]),8,$2),[[St,d.fontWeight]])]),u("label",null,[a[82]||(a[82]=M(" 压缩比例: ")),F(u("input",{type:"range","onUpdate:modelValue":T=>d.compression=T,min:"0.1",max:"1.5",step:"0.05"},null,8,k2),[[k,d.compression,void 0,{number:!0}]]),u("span",null,oe(d.compression.toFixed(2)),1)]),u("label",null,[a[83]||(a[83]=M(" 字符间距 (mm): ")),F(u("input",{type:"range","onUpdate:modelValue":T=>d.letterSpacing=T,min:"-1",max:"10",step:"0.05"},null,8,j2),[[k,d.letterSpacing,void 0,{number:!0}]]),u("span",null,oe(d.letterSpacing.toFixed(2)),1)]),u("label",null,[a[84]||(a[84]=M(" 垂直位置 (mm): ")),F(u("input",{type:"number","onUpdate:modelValue":T=>d.positionY=T,min:"-20",max:"20",step:"0.5"},null,8,Y2),[[k,d.positionY,void 0,{number:!0}]])])]))),128)),u("button",{class:"add-button",onClick:Kl},"添加新行")],512),[[Ke,re.value.stampType]])]),u("div",B2,[u("div",{class:"group-header",onClick:a[8]||(a[8]=d=>Ye("code"))},[a[85]||(a[85]=u("h3",null,"印章编码设置",-1)),u("span",{class:_e(["expand-icon",{expanded:re.value.code}])},"▼",2)]),F(u("div",K2,[u("label",null,[a[86]||(a[86]=M("印章编码: ")),F(u("input",{"onUpdate:modelValue":a[9]||(a[9]=d=>l.value=d)},null,512),[[k,l.value]])]),u("label",null,[a[87]||(a[87]=M(" 字体: ")),u("div",X2,[F(u("select",{"onUpdate:modelValue":a[10]||(a[10]=d=>h.value=d),class:"font-select",onChange:Rt},[(Z(!0),ne(ze,null,pt(ln.value,d=>(Z(),ne("option",{key:d,value:d,style:Gt({fontFamily:d})},oe(d),13,G2))),128))],544),[[St,h.value]]),F(u("input",{type:"text","onUpdate:modelValue":a[11]||(a[11]=d=>h.value=d),class:"font-input",onInput:Rt,placeholder:"输入字体名称"},null,544),[[k,h.value]])])]),u("label",null,[a[88]||(a[88]=M("字体大小 (mm): ")),F(u("input",{type:"number","onUpdate:modelValue":a[12]||(a[12]=d=>f.value=d),step:"0.1"},null,512),[[k,f.value,void 0,{number:!0}]])]),u("label",null,[a[90]||(a[90]=M(" 字体粗细: ")),F(u("select",{"onUpdate:modelValue":a[13]||(a[13]=d=>Ie.value=d)},a[89]||(a[89]=[Un('<option value="normal">正常</option><option value="bold">粗体</option><option value="100">100</option><option value="200">200</option><option value="300">300</option><option value="400">400</option><option value="500">500</option><option value="600">600</option><option value="700">700</option><option value="800">800</option><option value="900">900</option>',11)]),512),[[St,Ie.value]])]),u("label",null,[u("span",null,"压缩比例："+oe(lt.value.toFixed(2)),1),F(u("input",{type:"range","onUpdate:modelValue":a[14]||(a[14]=d=>lt.value=d),min:"0.0",max:"3",step:"0.01"},null,512),[[k,lt.value,void 0,{number:!0}]])]),u("label",null,[u("span",null,"分布因子: "+oe(ve.value.toFixed(1)),1),F(u("input",{type:"range","onUpdate:modelValue":a[15]||(a[15]=d=>ve.value=d),min:"0",max:"100",step:"0.5"},null,512),[[k,ve.value,void 0,{number:!0}]])]),u("label",null,[a[91]||(a[91]=M(" 边距 (mm): ")),F(u("input",{type:"number","onUpdate:modelValue":a[16]||(a[16]=d=>he.value=d),min:"-10",max:"20",step:"0.05"},null,512),[[k,he.value,void 0,{number:!0}]])])],512),[[Ke,re.value.code]])]),u("div",q2,[u("div",{class:"group-header",onClick:a[17]||(a[17]=d=>Ye("taxNumber"))},[a[92]||(a[92]=u("h3",null,"中间数字设置",-1)),u("span",{class:_e(["expand-icon",{expanded:re.value.taxNumber}])},"▼",2)]),F(u("div",J2,[u("label",null,[a[93]||(a[93]=M("税号: ")),F(u("input",{"onUpdate:modelValue":a[18]||(a[18]=d=>o.value=d)},null,512),[[k,o.value]])]),u("label",null,[a[94]||(a[94]=M(" 字体: ")),u("div",Z2,[F(u("select",{"onUpdate:modelValue":a[19]||(a[19]=d=>Ve.value=d),class:"font-select",onChange:Rt},[(Z(!0),ne(ze,null,pt(ln.value,d=>(Z(),ne("option",{key:d,value:d,style:Gt({fontFamily:d})},oe(d),13,Q2))),128))],544),[[St,Ve.value]]),F(u("input",{type:"text","onUpdate:modelValue":a[20]||(a[20]=d=>Ve.value=d),class:"font-input",onInput:Rt,placeholder:"输入字体名称"},null,544),[[k,Ve.value]])])]),u("label",null,[a[96]||(a[96]=M(" 字体粗细: ")),F(u("select",{"onUpdate:modelValue":a[21]||(a[21]=d=>Ue.value=d)},a[95]||(a[95]=[Un('<option value="normal">正常</option><option value="bold">粗体</option><option value="100">100</option><option value="200">200</option><option value="300">300</option><option value="400">400</option><option value="500">500</option><option value="600">600</option><option value="700">700</option><option value="800">800</option><option value="900">900</option>',11)]),512),[[St,Ue.value]])]),u("label",null,[u("span",null,"压缩比例："+oe(at.value.toFixed(2)),1),F(u("input",{type:"range","onUpdate:modelValue":a[22]||(a[22]=d=>at.value=d),min:"0.0",max:"3",step:"0.01"},null,512),[[k,at.value,void 0,{number:!0}]])]),u("label",null,[u("span",null,"字符间距 (mm)："+oe(rt.value.toFixed(2)),1),F(u("input",{type:"range","onUpdate:modelValue":a[23]||(a[23]=d=>rt.value=d),min:"-1",max:"20",step:"0.05"},null,512),[[k,rt.value,void 0,{number:!0}]])]),u("label",null,[u("span",null,"垂直位置调整 (mm)："+oe(c.value.toFixed(1)),1),F(u("input",{type:"range","onUpdate:modelValue":a[24]||(a[24]=d=>c.value=d),min:"-10",max:"10",step:"0.1"},null,512),[[k,c.value,void 0,{number:!0}]])])],512),[[Ke,re.value.taxNumber]])]),u("div",e1,[u("div",{class:"group-header",onClick:a[25]||(a[25]=d=>Ye("images"))},[a[97]||(a[97]=u("h3",null,"图片列表设置",-1)),u("span",{class:_e(["expand-icon",{expanded:re.value.images}])},"▼",2)]),F(u("div",t1,[u("div",n1,[(Z(!0),ne(ze,null,pt(ge.value,(d,N)=>(Z(),ne("div",{key:N,class:"image-item"},[u("div",s1,[u("span",null,"图片 "+oe(N+1),1),u("button",{class:"small-button delete-button",onClick:T=>$l(N)},"删除",8,i1)]),d.imageUrl?(Z(),ne("div",l1,[u("img",{src:d.imageUrl,alt:"预览"},null,8,o1)])):Be("",!0),u("label",null,[a[98]||(a[98]=M(" 选择图片: ")),u("input",{type:"file",onChange:T=>Bl(T,N),accept:"image/*"},null,40,a1)]),u("label",null,[a[99]||(a[99]=M(" 图片宽度 (mm): ")),F(u("input",{type:"number","onUpdate:modelValue":T=>d.imageWidth=T,min:"1",max:"100",step:"0.5"},null,8,r1),[[k,d.imageWidth,void 0,{number:!0}]])]),u("label",null,[a[100]||(a[100]=M(" 图片高度 (mm): ")),F(u("input",{type:"number","onUpdate:modelValue":T=>d.imageHeight=T,min:"1",max:"100",step:"0.5"},null,8,u1),[[k,d.imageHeight,void 0,{number:!0}]])]),u("label",null,[a[101]||(a[101]=M(" 水平位置 (mm): ")),F(u("input",{type:"number","onUpdate:modelValue":T=>d.positionX=T,min:"-20",max:"20",step:"0.5"},null,8,g1),[[k,d.positionX,void 0,{number:!0}]])]),u("label",null,[a[102]||(a[102]=M(" 垂直位置 (mm): ")),F(u("input",{type:"number","onUpdate:modelValue":T=>d.positionY=T,min:"-20",max:"20",step:"0.5"},null,8,c1),[[k,d.positionY,void 0,{number:!0}]])]),u("label",f1,[F(u("input",{type:"checkbox","onUpdate:modelValue":T=>d.keepAspectRatio=T},null,8,d1),[[qe,d.keepAspectRatio]]),a[103]||(a[103]=M(" 保持宽高比 "))])]))),128))]),u("button",{class:"add-button",onClick:Pn},"添加新图片")],512),[[Ke,re.value.images]])]),u("div",p1,[u("div",{class:"group-header",onClick:a[26]||(a[26]=d=>Ye("star"))},[a[104]||(a[104]=u("h3",null,"五角星设置",-1)),u("span",{class:_e(["expand-icon",{expanded:re.value.star}])},"▼",2)]),F(u("div",h1,[u("label",m1,[F(u("input",{type:"checkbox","onUpdate:modelValue":a[27]||(a[27]=d=>He.value=d)},null,512),[[qe,He.value]]),a[105]||(a[105]=M(" 绘制五角星 "))]),He.value?(Z(),ne("div",z1,[u("label",null,[a[106]||(a[106]=M(" 五角星直径 (mm): ")),F(u("input",{type:"number","onUpdate:modelValue":a[28]||(a[28]=d=>O.value=d),step:"0.1"},null,512),[[k,O.value,void 0,{number:!0}]])]),u("label",null,[a[107]||(a[107]=M(" 垂直位置 (mm): ")),F(u("input",{type:"number","onUpdate:modelValue":a[29]||(a[29]=d=>it.value=d),min:"-10",max:"10",step:"0.1"},null,512),[[k,it.value,void 0,{number:!0}]])])])):Be("",!0)],512),[[Ke,re.value.star]])]),u("div",v1,[u("div",{class:"group-header",onClick:a[30]||(a[30]=d=>Ye("security"))},[a[108]||(a[108]=u("h3",null,"防伪纹路设置",-1)),u("span",{class:_e(["expand-icon",{expanded:re.value.security}])},"▼",2)]),F(u("div",y1,[u("label",null,[a[109]||(a[109]=M(" 启用防伪纹路: ")),F(u("input",{type:"checkbox","onUpdate:modelValue":a[31]||(a[31]=d=>At.value=d)},null,512),[[qe,At.value]])]),u("button",{onClick:a[32]||(a[32]=d=>ct(!0,!1))},"刷新纹路"),u("label",null,[a[110]||(a[110]=M("纹路数量: ")),F(u("input",{type:"range","onUpdate:modelValue":a[33]||(a[33]=d=>ot.value=d),min:"1",max:"100",step:"1"},null,512),[[k,ot.value,void 0,{number:!0}]])]),u("label",null,[a[111]||(a[111]=M("纹路长度 (mm): ")),F(u("input",{type:"range","onUpdate:modelValue":a[34]||(a[34]=d=>bt.value=d),min:"0.1",max:"100",step:"0.1"},null,512),[[k,bt.value,void 0,{number:!0}]])]),u("label",null,[a[112]||(a[112]=M("纹路宽度 (mm): ")),F(u("input",{type:"range","onUpdate:modelValue":a[35]||(a[35]=d=>je.value=d),min:"0.05",max:"0.5",step:"0.05"},null,512),[[k,je.value,void 0,{number:!0}]])])],512),[[Ke,re.value.security]])]),u("div",b1,[u("div",{class:"group-header",onClick:a[36]||(a[36]=d=>Ye("roughEdge"))},[a[113]||(a[113]=u("h3",null,"毛边效果设置",-1)),u("span",{class:_e(["expand-icon",{expanded:re.value.roughEdge}])},"▼",2)]),F(u("div",C1,[u("label",w1,[F(u("input",{type:"checkbox","onUpdate:modelValue":a[37]||(a[37]=d=>C.value=d)},null,512),[[qe,C.value]]),a[114]||(a[114]=M(" 启用毛边效果 "))]),C.value?(Z(),ne("label",S1,[a[115]||(a[115]=M(" 毛边宽度 (mm): ")),F(u("input",{type:"range","onUpdate:modelValue":a[38]||(a[38]=d=>W.value=d),min:"0.05",max:"0.5",step:"0.05"},null,512),[[k,W.value,void 0,{number:!0}]]),u("span",null,oe(W.value.toFixed(2)),1)])):Be("",!0),C.value?(Z(),ne("label",x1,[a[116]||(a[116]=M(" 毛边高度 (mm): ")),F(u("input",{type:"range","onUpdate:modelValue":a[39]||(a[39]=d=>A.value=d),min:"0.1",max:"5",step:"0.1"},null,512),[[k,A.value,void 0,{number:!0}]]),u("span",null,oe(A.value.toFixed(1)),1)])):Be("",!0),C.value?(Z(),ne("label",T1,[a[117]||(a[117]=M(" 毛边概率: ")),F(u("input",{type:"range","onUpdate:modelValue":a[40]||(a[40]=d=>I.value=d),min:"0",max:"1",step:"0.01"},null,512),[[k,I.value,void 0,{number:!0}]]),u("span",null,oe(I.value.toFixed(2)),1)])):Be("",!0),C.value?(Z(),ne("label",P1,[a[118]||(a[118]=M(" 毛边偏移 (mm): ")),F(u("input",{type:"range","onUpdate:modelValue":a[41]||(a[41]=d=>H.value=d),min:"-10",max:"10",step:"0.01"},null,512),[[k,H.value,void 0,{number:!0}]]),u("span",null,oe(H.value.toFixed(2)),1)])):Be("",!0),C.value?(Z(),ne("label",E1,[a[119]||(a[119]=M(" 毛边点数: ")),F(u("input",{type:"range","onUpdate:modelValue":a[42]||(a[42]=d=>X.value=d),min:"100",max:"1000",step:"10"},null,512),[[k,X.value,void 0,{number:!0}]]),u("span",null,oe(X.value),1)])):Be("",!0),u("button",{onClick:a[43]||(a[43]=d=>ct(!1,!1,!0))},"刷新毛边")],512),[[Ke,re.value.roughEdge]])]),u("div",_1,[u("div",{class:"group-header",onClick:a[44]||(a[44]=d=>Ye("aging"))},[a[120]||(a[120]=u("h3",null,"做旧效果",-1)),u("span",{class:_e(["expand-icon",{expanded:re.value.aging}])},"▼",2)]),F(u("div",L1,[u("label",F1,[F(u("input",{type:"checkbox","onUpdate:modelValue":a[45]||(a[45]=d=>B.value=d)},null,512),[[qe,B.value]]),a[121]||(a[121]=M(" 启用做旧效果 "))]),u("label",A1,[F(u("input",{type:"checkbox","onUpdate:modelValue":a[46]||(a[46]=d=>D.value=d)},null,512),[[qe,D.value]]),a[122]||(a[122]=M(" 手动做旧 "))]),B.value?(Z(),ne("label",M1,[a[123]||(a[123]=M(" 做旧强度: ")),F(u("input",{type:"range","onUpdate:modelValue":a[47]||(a[47]=d=>j.value=d),min:"0",max:"100",step:"1"},null,512),[[k,j.value,void 0,{number:!0}]])])):Be("",!0),u("button",{onClick:a[48]||(a[48]=d=>ct(!1,!0))},"刷新做旧")],512),[[Ke,re.value.aging]])]),u("div",R1,[u("div",{class:"group-header",onClick:a[49]||(a[49]=d=>Ye("innerCircle"))},[a[124]||(a[124]=u("h3",null,"内圈圆形设置",-1)),u("span",{class:_e(["expand-icon",{expanded:re.value.innerCircle}])},"▼",2)]),F(u("div",I1,[u("button",{onClick:Ql},"添加新行"),(Z(!0),ne(ze,null,pt(gt.value,(d,N)=>(Z(),ne("div",{key:N,class:"inner-circle-item"},[u("div",U1,[u("span",null,"第 "+oe(N+1)+" 行",1),u("button",{class:"small-button delete-button",onClick:T=>eo(N)},"删除",8,O1)]),u("label",null,[a[125]||(a[125]=M(" 内圈圆线宽 (mm): ")),F(u("input",{type:"number","onUpdate:modelValue":T=>d.innerCircleLineWidth=T,min:"0.05",max:"0.5",step:"0.05"},null,8,W1),[[k,d.innerCircleLineWidth,void 0,{number:!0}]])]),u("label",null,[a[126]||(a[126]=M(" 内圈圆半径X (mm): ")),F(u("input",{type:"number","onUpdate:modelValue":T=>d.innerCircleLineRadiusX=T,min:"1",max:"50",step:"0.1"},null,8,D1),[[k,d.innerCircleLineRadiusX,void 0,{number:!0}]])]),u("label",null,[a[127]||(a[127]=M(" 内圈圆半径Y (mm): ")),F(u("input",{type:"number","onUpdate:modelValue":T=>d.innerCircleLineRadiusY=T,min:"1",max:"50",step:"0.1"},null,8,V1),[[k,d.innerCircleLineRadiusY,void 0,{number:!0}]])])]))),128))],512),[[Ke,re.value.innerCircle]])])],512),u("div",H1,[u("div",N1,[u("div",$1,[a[131]||(a[131]=u("h3",null,"做旧效果",-1)),u("label",k1,[F(u("input",{type:"checkbox","onUpdate:modelValue":a[50]||(a[50]=d=>B.value=d)},null,512),[[qe,B.value]]),a[128]||(a[128]=M(" 启用做旧效果 "))]),u("label",j1,[F(u("input",{type:"checkbox","onUpdate:modelValue":a[51]||(a[51]=d=>D.value=d)},null,512),[[qe,D.value]]),a[129]||(a[129]=M(" 手动做旧 "))]),B.value?(Z(),ne("label",Y1,[a[130]||(a[130]=M(" 做旧强度: ")),F(u("input",{type:"range","onUpdate:modelValue":a[52]||(a[52]=d=>j.value=d),min:"0",max:"100",step:"1"},null,512),[[k,j.value,void 0,{number:!0}]])])):Be("",!0),u("button",{onClick:a[53]||(a[53]=d=>ct(!1,!0))},"刷新做旧")]),u("div",{class:"control-group"},[a[132]||(a[132]=u("h3",null,"提取印章",-1)),u("button",{onClick:ao},"提取印章工具")])]),u("canvas",{ref_key:"stampCanvas",ref:n,width:"600",height:"600"},null,512)]),u("div",B1,[u("div",{class:"template-header"},[a[134]||(a[134]=u("h3",null,"常用模板",-1)),u("button",{class:"add-template",onClick:ro},a[133]||(a[133]=[u("span",null,"+",-1),M(" 保存当前为模板 ")]))]),u("div",K1,[u("div",X1,[a[135]||(a[135]=u("h4",null,"默认模板",-1)),(Z(),ne(ze,null,pt(us,(d,N)=>u("div",{key:"default-"+N,class:_e(["template-item",{active:Zs.value===-1-N}]),onClick:T=>uo(d)},[u("div",q1,[u("img",{src:d.preview,alt:"模板预览"},null,8,J1)]),u("div",Z1,[u("span",Q1,oe(d.name),1)])],10,G1)),64))])])])])],64))}}),t5=fl({__name:"App",setup(e){return(t,n)=>(Z(),Ul(e5))}}),n5=(e,t)=>{const n=e.__vccOpts||e;for(const[s,i]of t)n[s]=i;return n},s5=n5(t5,[["__scopeId","data-v-16a1c733"]]);O0(s5).mount("#app");
