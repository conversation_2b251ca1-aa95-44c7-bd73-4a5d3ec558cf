var Lr=Object.defineProperty;var Pr=(e,t,s)=>t in e?Lr(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s;var J=(e,t,s)=>Pr(e,typeof t!="symbol"?t+"":t,s);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))n(i);new MutationObserver(i=>{for(const o of i)if(o.type==="childList")for(const a of o.addedNodes)a.tagName==="LINK"&&a.rel==="modulepreload"&&n(a)}).observe(document,{childList:!0,subtree:!0});function s(i){const o={};return i.integrity&&(o.integrity=i.integrity),i.referrerPolicy&&(o.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?o.credentials="include":i.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function n(i){if(i.ep)return;i.ep=!0;const o=s(i);fetch(i.href,o)}})();/**
* @vue/shared v3.5.8
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Wi(e){const t=Object.create(null);for(const s of e.split(","))t[s]=1;return s=>s in t}const Ce={},Es=[],Mt=()=>{},Mr=()=>!1,jn=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Vi=e=>e.startsWith("onUpdate:"),We=Object.assign,$i=(e,t)=>{const s=e.indexOf(t);s>-1&&e.splice(s,1)},Ar=Object.prototype.hasOwnProperty,ge=(e,t)=>Ar.call(e,t),q=Array.isArray,zs=e=>pn(e)==="[object Map]",Rs=e=>pn(e)==="[object Set]",Co=e=>pn(e)==="[object Date]",te=e=>typeof e=="function",Oe=e=>typeof e=="string",At=e=>typeof e=="symbol",Se=e=>e!==null&&typeof e=="object",Da=e=>(Se(e)||te(e))&&te(e.then)&&te(e.catch),Oa=Object.prototype.toString,pn=e=>Oa.call(e),xr=e=>pn(e).slice(8,-1),Ra=e=>pn(e)==="[object Object]",Hi=e=>Oe(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Gs=Wi(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Yn=e=>{const t=Object.create(null);return s=>t[s]||(t[s]=e(s))},Nr=/-(\w)/g,gs=Yn(e=>e.replace(Nr,(t,s)=>s?s.toUpperCase():"")),Dr=/\B([A-Z])/g,hs=Yn(e=>e.replace(Dr,"-$1").toLowerCase()),Fa=Yn(e=>e.charAt(0).toUpperCase()+e.slice(1)),ai=Yn(e=>e?`on${Fa(e)}`:""),es=(e,t)=>!Object.is(e,t),An=(e,...t)=>{for(let s=0;s<e.length;s++)e[s](...t)},ka=(e,t,s,n=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:n,value:s})},Rn=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let So;const Ua=()=>So||(So=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Is(e){if(q(e)){const t={};for(let s=0;s<e.length;s++){const n=e[s],i=Oe(n)?kr(n):Is(n);if(i)for(const o in i)t[o]=i[o]}return t}else if(Oe(e)||Se(e))return e}const Or=/;(?![^(]*\))/g,Rr=/:([^]+)/,Fr=/\/\*[^]*?\*\//g;function kr(e){const t={};return e.replace(Fr,"").split(Or).forEach(s=>{if(s){const n=s.split(Rr);n.length>1&&(t[n[0].trim()]=n[1].trim())}}),t}function Re(e){let t="";if(Oe(e))t=e;else if(q(e))for(let s=0;s<e.length;s++){const n=Re(e[s]);n&&(t+=n+" ")}else if(Se(e))for(const s in e)e[s]&&(t+=s+" ");return t.trim()}const Ur="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",Wr=Wi(Ur);function Wa(e){return!!e||e===""}function Vr(e,t){if(e.length!==t.length)return!1;let s=!0;for(let n=0;s&&n<e.length;n++)s=gn(e[n],t[n]);return s}function gn(e,t){if(e===t)return!0;let s=Co(e),n=Co(t);if(s||n)return s&&n?e.getTime()===t.getTime():!1;if(s=At(e),n=At(t),s||n)return e===t;if(s=q(e),n=q(t),s||n)return s&&n?Vr(e,t):!1;if(s=Se(e),n=Se(t),s||n){if(!s||!n)return!1;const i=Object.keys(e).length,o=Object.keys(t).length;if(i!==o)return!1;for(const a in e){const l=e.hasOwnProperty(a),r=t.hasOwnProperty(a);if(l&&!r||!l&&r||!gn(e[a],t[a]))return!1}}return String(e)===String(t)}function ji(e,t){return e.findIndex(s=>gn(s,t))}const Va=e=>!!(e&&e.__v_isRef===!0),M=e=>Oe(e)?e:e==null?"":q(e)||Se(e)&&(e.toString===Oa||!te(e.toString))?Va(e)?M(e.value):JSON.stringify(e,$a,2):String(e),$a=(e,t)=>Va(t)?$a(e,t.value):zs(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((s,[n,i],o)=>(s[li(n,o)+" =>"]=i,s),{})}:Rs(t)?{[`Set(${t.size})`]:[...t.values()].map(s=>li(s))}:At(t)?li(t):Se(t)&&!q(t)&&!Ra(t)?String(t):t,li=(e,t="")=>{var s;return At(e)?`Symbol(${(s=e.description)!=null?s:t})`:e};/**
* @vue/reactivity v3.5.8
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let at;class Ha{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=at,!t&&at&&(this.index=(at.scopes||(at.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].pause();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].resume();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].resume()}}run(t){if(this._active){const s=at;try{return at=this,t()}finally{at=s}}}on(){at=this}off(){at=this.parent}stop(t){if(this._active){let s,n;for(s=0,n=this.effects.length;s<n;s++)this.effects[s].stop();for(s=0,n=this.cleanups.length;s<n;s++)this.cleanups[s]();if(this.scopes)for(s=0,n=this.scopes.length;s<n;s++)this.scopes[s].stop(!0);if(!this.detached&&this.parent&&!t){const i=this.parent.scopes.pop();i&&i!==this&&(this.parent.scopes[this.index]=i,i.index=this.index)}this.parent=void 0,this._active=!1}}}function $r(e){return new Ha(e)}function Hr(){return at}let be;const ri=new WeakSet;class ja{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,at&&at.active&&at.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,ri.has(this)&&(ri.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Ba(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,To(this),Ga(this);const t=be,s=St;be=this,St=!0;try{return this.fn()}finally{Xa(this),be=t,St=s,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Gi(t);this.deps=this.depsTail=void 0,To(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?ri.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){vi(this)&&this.run()}get dirty(){return vi(this)}}let Ya=0,Xs;function Ba(e){e.flags|=8,e.next=Xs,Xs=e}function Yi(){Ya++}function Bi(){if(--Ya>0)return;let e;for(;Xs;){let t=Xs;for(Xs=void 0;t;){const s=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(n){e||(e=n)}t=s}}if(e)throw e}function Ga(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Xa(e,t=!1){let s,n=e.depsTail,i=n;for(;i;){const o=i.prevDep;i.version===-1?(i===n&&(n=o),Gi(i,t),jr(i)):s=i,i.dep.activeLink=i.prevActiveLink,i.prevActiveLink=void 0,i=o}e.deps=s,e.depsTail=n}function vi(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Ka(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Ka(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===en))return;e.globalVersion=en;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!vi(e)){e.flags&=-3;return}const s=be,n=St;be=e,St=!0;try{Ga(e);const i=e.fn(e._value);(t.version===0||es(i,e._value))&&(e._value=i,t.version++)}catch(i){throw t.version++,i}finally{be=s,St=n,Xa(e,!0),e.flags&=-3}}function Gi(e,t=!1){const{dep:s,prevSub:n,nextSub:i}=e;if(n&&(n.nextSub=i,e.prevSub=void 0),i&&(i.prevSub=n,e.nextSub=void 0),s.subs===e&&(s.subs=n),!s.subs)if(s.computed){s.computed.flags&=-5;for(let o=s.computed.deps;o;o=o.nextDep)Gi(o,!0)}else s.map&&!t&&(s.map.delete(s.key),s.map.size||Fn.delete(s.target))}function jr(e){const{prevDep:t,nextDep:s}=e;t&&(t.nextDep=s,e.prevDep=void 0),s&&(s.prevDep=t,e.nextDep=void 0)}let St=!0;const Ja=[];function ts(){Ja.push(St),St=!1}function ss(){const e=Ja.pop();St=e===void 0?!0:e}function To(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const s=be;be=void 0;try{t()}finally{be=s}}}let en=0;class Yr{constructor(t,s){this.sub=t,this.dep=s,this.version=s.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Xi{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.target=void 0,this.map=void 0,this.key=void 0}track(t){if(!be||!St||be===this.computed)return;let s=this.activeLink;if(s===void 0||s.sub!==be)s=this.activeLink=new Yr(be,this),be.deps?(s.prevDep=be.depsTail,be.depsTail.nextDep=s,be.depsTail=s):be.deps=be.depsTail=s,be.flags&4&&Qa(s);else if(s.version===-1&&(s.version=this.version,s.nextDep)){const n=s.nextDep;n.prevDep=s.prevDep,s.prevDep&&(s.prevDep.nextDep=n),s.prevDep=be.depsTail,s.nextDep=void 0,be.depsTail.nextDep=s,be.depsTail=s,be.deps===s&&(be.deps=n)}return s}trigger(t){this.version++,en++,this.notify(t)}notify(t){Yi();try{for(let s=this.subs;s;s=s.prevSub)s.sub.notify()&&s.sub.dep.notify()}finally{Bi()}}}function Qa(e){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let n=t.deps;n;n=n.nextDep)Qa(n)}const s=e.dep.subs;s!==e&&(e.prevSub=s,s&&(s.nextSub=e)),e.dep.subs=e}const Fn=new WeakMap,ps=Symbol(""),bi=Symbol(""),tn=Symbol("");function Ye(e,t,s){if(St&&be){let n=Fn.get(e);n||Fn.set(e,n=new Map);let i=n.get(s);i||(n.set(s,i=new Xi),i.target=e,i.map=n,i.key=s),i.track()}}function jt(e,t,s,n,i,o){const a=Fn.get(e);if(!a){en++;return}const l=r=>{r&&r.trigger()};if(Yi(),t==="clear")a.forEach(l);else{const r=q(e),u=r&&Hi(s);if(r&&s==="length"){const f=Number(n);a.forEach((d,v)=>{(v==="length"||v===tn||!At(v)&&v>=f)&&l(d)})}else switch(s!==void 0&&l(a.get(s)),u&&l(a.get(tn)),t){case"add":r?u&&l(a.get("length")):(l(a.get(ps)),zs(e)&&l(a.get(bi)));break;case"delete":r||(l(a.get(ps)),zs(e)&&l(a.get(bi)));break;case"set":zs(e)&&l(a.get(ps));break}}Bi()}function bs(e){const t=de(e);return t===e?t:(Ye(t,"iterate",tn),ht(e)?t:t.map($e))}function Bn(e){return Ye(e=de(e),"iterate",tn),e}const Br={__proto__:null,[Symbol.iterator](){return ci(this,Symbol.iterator,$e)},concat(...e){return bs(this).concat(...e.map(t=>q(t)?bs(t):t))},entries(){return ci(this,"entries",e=>(e[1]=$e(e[1]),e))},every(e,t){return Rt(this,"every",e,t,void 0,arguments)},filter(e,t){return Rt(this,"filter",e,t,s=>s.map($e),arguments)},find(e,t){return Rt(this,"find",e,t,$e,arguments)},findIndex(e,t){return Rt(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return Rt(this,"findLast",e,t,$e,arguments)},findLastIndex(e,t){return Rt(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return Rt(this,"forEach",e,t,void 0,arguments)},includes(...e){return fi(this,"includes",e)},indexOf(...e){return fi(this,"indexOf",e)},join(e){return bs(this).join(e)},lastIndexOf(...e){return fi(this,"lastIndexOf",e)},map(e,t){return Rt(this,"map",e,t,void 0,arguments)},pop(){return $s(this,"pop")},push(...e){return $s(this,"push",e)},reduce(e,...t){return wo(this,"reduce",e,t)},reduceRight(e,...t){return wo(this,"reduceRight",e,t)},shift(){return $s(this,"shift")},some(e,t){return Rt(this,"some",e,t,void 0,arguments)},splice(...e){return $s(this,"splice",e)},toReversed(){return bs(this).toReversed()},toSorted(e){return bs(this).toSorted(e)},toSpliced(...e){return bs(this).toSpliced(...e)},unshift(...e){return $s(this,"unshift",e)},values(){return ci(this,"values",$e)}};function ci(e,t,s){const n=Bn(e),i=n[t]();return n!==e&&!ht(e)&&(i._next=i.next,i.next=()=>{const o=i._next();return o.value&&(o.value=s(o.value)),o}),i}const Gr=Array.prototype;function Rt(e,t,s,n,i,o){const a=Bn(e),l=a!==e&&!ht(e),r=a[t];if(r!==Gr[t]){const d=r.apply(e,o);return l?$e(d):d}let u=s;a!==e&&(l?u=function(d,v){return s.call(this,$e(d),v,e)}:s.length>2&&(u=function(d,v){return s.call(this,d,v,e)}));const f=r.call(a,u,n);return l&&i?i(f):f}function wo(e,t,s,n){const i=Bn(e);let o=s;return i!==e&&(ht(e)?s.length>3&&(o=function(a,l,r){return s.call(this,a,l,r,e)}):o=function(a,l,r){return s.call(this,a,$e(l),r,e)}),i[t](o,...n)}function fi(e,t,s){const n=de(e);Ye(n,"iterate",tn);const i=n[t](...s);return(i===-1||i===!1)&&Zi(s[0])?(s[0]=de(s[0]),n[t](...s)):i}function $s(e,t,s=[]){ts(),Yi();const n=de(e)[t].apply(e,s);return Bi(),ss(),n}const Xr=Wi("__proto__,__v_isRef,__isVue"),qa=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(At));function Kr(e){At(e)||(e=String(e));const t=de(this);return Ye(t,"has",e),t.hasOwnProperty(e)}class Za{constructor(t=!1,s=!1){this._isReadonly=t,this._isShallow=s}get(t,s,n){const i=this._isReadonly,o=this._isShallow;if(s==="__v_isReactive")return!i;if(s==="__v_isReadonly")return i;if(s==="__v_isShallow")return o;if(s==="__v_raw")return n===(i?o?rc:nl:o?sl:tl).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(n)?t:void 0;const a=q(t);if(!i){let r;if(a&&(r=Br[s]))return r;if(s==="hasOwnProperty")return Kr}const l=Reflect.get(t,s,Ue(t)?t:n);return(At(s)?qa.has(s):Xr(s))||(i||Ye(t,"get",s),o)?l:Ue(l)?a&&Hi(s)?l:l.value:Se(l)?i?il(l):Qi(l):l}}class el extends Za{constructor(t=!1){super(!1,t)}set(t,s,n,i){let o=t[s];if(!this._isShallow){const r=ds(o);if(!ht(n)&&!ds(n)&&(o=de(o),n=de(n)),!q(t)&&Ue(o)&&!Ue(n))return r?!1:(o.value=n,!0)}const a=q(t)&&Hi(s)?Number(s)<t.length:ge(t,s),l=Reflect.set(t,s,n,Ue(t)?t:i);return t===de(i)&&(a?es(n,o)&&jt(t,"set",s,n):jt(t,"add",s,n)),l}deleteProperty(t,s){const n=ge(t,s);t[s];const i=Reflect.deleteProperty(t,s);return i&&n&&jt(t,"delete",s,void 0),i}has(t,s){const n=Reflect.has(t,s);return(!At(s)||!qa.has(s))&&Ye(t,"has",s),n}ownKeys(t){return Ye(t,"iterate",q(t)?"length":ps),Reflect.ownKeys(t)}}class Jr extends Za{constructor(t=!1){super(!0,t)}set(t,s){return!0}deleteProperty(t,s){return!0}}const Qr=new el,qr=new Jr,Zr=new el(!0);const Ki=e=>e,Gn=e=>Reflect.getPrototypeOf(e);function Cn(e,t,s=!1,n=!1){e=e.__v_raw;const i=de(e),o=de(t);s||(es(t,o)&&Ye(i,"get",t),Ye(i,"get",o));const{has:a}=Gn(i),l=n?Ki:s?eo:$e;if(a.call(i,t))return l(e.get(t));if(a.call(i,o))return l(e.get(o));e!==i&&e.get(t)}function Sn(e,t=!1){const s=this.__v_raw,n=de(s),i=de(e);return t||(es(e,i)&&Ye(n,"has",e),Ye(n,"has",i)),e===i?s.has(e):s.has(e)||s.has(i)}function Tn(e,t=!1){return e=e.__v_raw,!t&&Ye(de(e),"iterate",ps),Reflect.get(e,"size",e)}function Eo(e,t=!1){!t&&!ht(e)&&!ds(e)&&(e=de(e));const s=de(this);return Gn(s).has.call(s,e)||(s.add(e),jt(s,"add",e,e)),this}function zo(e,t,s=!1){!s&&!ht(t)&&!ds(t)&&(t=de(t));const n=de(this),{has:i,get:o}=Gn(n);let a=i.call(n,e);a||(e=de(e),a=i.call(n,e));const l=o.call(n,e);return n.set(e,t),a?es(t,l)&&jt(n,"set",e,t):jt(n,"add",e,t),this}function Io(e){const t=de(this),{has:s,get:n}=Gn(t);let i=s.call(t,e);i||(e=de(e),i=s.call(t,e)),n&&n.call(t,e);const o=t.delete(e);return i&&jt(t,"delete",e,void 0),o}function Lo(){const e=de(this),t=e.size!==0,s=e.clear();return t&&jt(e,"clear",void 0,void 0),s}function wn(e,t){return function(n,i){const o=this,a=o.__v_raw,l=de(a),r=t?Ki:e?eo:$e;return!e&&Ye(l,"iterate",ps),a.forEach((u,f)=>n.call(i,r(u),r(f),o))}}function En(e,t,s){return function(...n){const i=this.__v_raw,o=de(i),a=zs(o),l=e==="entries"||e===Symbol.iterator&&a,r=e==="keys"&&a,u=i[e](...n),f=s?Ki:t?eo:$e;return!t&&Ye(o,"iterate",r?bi:ps),{next(){const{value:d,done:v}=u.next();return v?{value:d,done:v}:{value:l?[f(d[0]),f(d[1])]:f(d),done:v}},[Symbol.iterator](){return this}}}}function Kt(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function ec(){const e={get(o){return Cn(this,o)},get size(){return Tn(this)},has:Sn,add:Eo,set:zo,delete:Io,clear:Lo,forEach:wn(!1,!1)},t={get(o){return Cn(this,o,!1,!0)},get size(){return Tn(this)},has:Sn,add(o){return Eo.call(this,o,!0)},set(o,a){return zo.call(this,o,a,!0)},delete:Io,clear:Lo,forEach:wn(!1,!0)},s={get(o){return Cn(this,o,!0)},get size(){return Tn(this,!0)},has(o){return Sn.call(this,o,!0)},add:Kt("add"),set:Kt("set"),delete:Kt("delete"),clear:Kt("clear"),forEach:wn(!0,!1)},n={get(o){return Cn(this,o,!0,!0)},get size(){return Tn(this,!0)},has(o){return Sn.call(this,o,!0)},add:Kt("add"),set:Kt("set"),delete:Kt("delete"),clear:Kt("clear"),forEach:wn(!0,!0)};return["keys","values","entries",Symbol.iterator].forEach(o=>{e[o]=En(o,!1,!1),s[o]=En(o,!0,!1),t[o]=En(o,!1,!0),n[o]=En(o,!0,!0)}),[e,s,t,n]}const[tc,sc,nc,ic]=ec();function Ji(e,t){const s=t?e?ic:nc:e?sc:tc;return(n,i,o)=>i==="__v_isReactive"?!e:i==="__v_isReadonly"?e:i==="__v_raw"?n:Reflect.get(ge(s,i)&&i in n?s:n,i,o)}const oc={get:Ji(!1,!1)},ac={get:Ji(!1,!0)},lc={get:Ji(!0,!1)};const tl=new WeakMap,sl=new WeakMap,nl=new WeakMap,rc=new WeakMap;function cc(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function fc(e){return e.__v_skip||!Object.isExtensible(e)?0:cc(xr(e))}function Qi(e){return ds(e)?e:qi(e,!1,Qr,oc,tl)}function uc(e){return qi(e,!1,Zr,ac,sl)}function il(e){return qi(e,!0,qr,lc,nl)}function qi(e,t,s,n,i){if(!Se(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const o=i.get(e);if(o)return o;const a=fc(e);if(a===0)return e;const l=new Proxy(e,a===2?n:s);return i.set(e,l),l}function Ls(e){return ds(e)?Ls(e.__v_raw):!!(e&&e.__v_isReactive)}function ds(e){return!!(e&&e.__v_isReadonly)}function ht(e){return!!(e&&e.__v_isShallow)}function Zi(e){return e?!!e.__v_raw:!1}function de(e){const t=e&&e.__v_raw;return t?de(t):e}function pc(e){return!ge(e,"__v_skip")&&Object.isExtensible(e)&&ka(e,"__v_skip",!0),e}const $e=e=>Se(e)?Qi(e):e,eo=e=>Se(e)?il(e):e;function Ue(e){return e?e.__v_isRef===!0:!1}function $(e){return ol(e,!1)}function gc(e){return ol(e,!0)}function ol(e,t){return Ue(e)?e:new dc(e,t)}class dc{constructor(t,s){this.dep=new Xi,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=s?t:de(t),this._value=s?t:$e(t),this.__v_isShallow=s}get value(){return this.dep.track(),this._value}set value(t){const s=this._rawValue,n=this.__v_isShallow||ht(t)||ds(t);t=n?t:de(t),es(t,s)&&(this._rawValue=t,this._value=n?t:$e(t),this.dep.trigger())}}function N(e){return Ue(e)?e.value:e}const mc={get:(e,t,s)=>t==="__v_raw"?e:N(Reflect.get(e,t,s)),set:(e,t,s,n)=>{const i=e[t];return Ue(i)&&!Ue(s)?(i.value=s,!0):Reflect.set(e,t,s,n)}};function al(e){return Ls(e)?e:new Proxy(e,mc)}class hc{constructor(t,s,n){this.fn=t,this.setter=s,this._value=void 0,this.dep=new Xi(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=en-1,this.effect=this,this.__v_isReadonly=!s,this.isSSR=n}notify(){if(this.flags|=16,!(this.flags&8)&&be!==this)return Ba(this),!0}get value(){const t=this.dep.track();return Ka(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function yc(e,t,s=!1){let n,i;return te(e)?n=e:(n=e.get,i=e.set),new hc(n,i,s)}const zn={},kn=new WeakMap;let fs;function _c(e,t=!1,s=fs){if(s){let n=kn.get(s);n||kn.set(s,n=[]),n.push(e)}}function vc(e,t,s=Ce){const{immediate:n,deep:i,once:o,scheduler:a,augmentJob:l,call:r}=s,u=T=>i?T:ht(T)||i===!1||i===0?Vt(T,1):Vt(T);let f,d,v,E,D=!1,O=!1;if(Ue(e)?(d=()=>e.value,D=ht(e)):Ls(e)?(d=()=>u(e),D=!0):q(e)?(O=!0,D=e.some(T=>Ls(T)||ht(T)),d=()=>e.map(T=>{if(Ue(T))return T.value;if(Ls(T))return u(T);if(te(T))return r?r(T,2):T()})):te(e)?t?d=r?()=>r(e,2):e:d=()=>{if(v){ts();try{v()}finally{ss()}}const T=fs;fs=f;try{return r?r(e,3,[E]):e(E)}finally{fs=T}}:d=Mt,t&&i){const T=d,F=i===!0?1/0:i;d=()=>Vt(T(),F)}const W=Hr(),b=()=>{f.stop(),W&&$i(W.effects,f)};if(o&&t){const T=t;t=(...F)=>{T(...F),b()}}let z=O?new Array(e.length).fill(zn):zn;const P=T=>{if(!(!(f.flags&1)||!f.dirty&&!T))if(t){const F=f.run();if(i||D||(O?F.some((H,k)=>es(H,z[k])):es(F,z))){v&&v();const H=fs;fs=f;try{const k=[F,z===zn?void 0:O&&z[0]===zn?[]:z,E];r?r(t,3,k):t(...k),z=F}finally{fs=H}}}else f.run()};return l&&l(P),f=new ja(d),f.scheduler=a?()=>a(P,!1):P,E=T=>_c(T,!1,f),v=f.onStop=()=>{const T=kn.get(f);if(T){if(r)r(T,4);else for(const F of T)F();kn.delete(f)}},t?n?P(!0):z=f.run():a?a(P.bind(null,!0),!0):f.run(),b.pause=f.pause.bind(f),b.resume=f.resume.bind(f),b.stop=b,b}function Vt(e,t=1/0,s){if(t<=0||!Se(e)||e.__v_skip||(s=s||new Set,s.has(e)))return e;if(s.add(e),t--,Ue(e))Vt(e.value,t,s);else if(q(e))for(let n=0;n<e.length;n++)Vt(e[n],t,s);else if(Rs(e)||zs(e))e.forEach(n=>{Vt(n,t,s)});else if(Ra(e)){for(const n in e)Vt(e[n],t,s);for(const n of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,n)&&Vt(e[n],t,s)}return e}/**
* @vue/runtime-core v3.5.8
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function dn(e,t,s,n){try{return n?e(...n):e()}catch(i){Xn(i,t,s)}}function xt(e,t,s,n){if(te(e)){const i=dn(e,t,s,n);return i&&Da(i)&&i.catch(o=>{Xn(o,t,s)}),i}if(q(e)){const i=[];for(let o=0;o<e.length;o++)i.push(xt(e[o],t,s,n));return i}}function Xn(e,t,s,n=!0){const i=t?t.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:a}=t&&t.appContext.config||Ce;if(t){let l=t.parent;const r=t.proxy,u=`https://vuejs.org/error-reference/#runtime-${s}`;for(;l;){const f=l.ec;if(f){for(let d=0;d<f.length;d++)if(f[d](e,r,u)===!1)return}l=l.parent}if(o){ts(),dn(o,null,10,[e,r,u]),ss();return}}bc(e,s,i,n,a)}function bc(e,t,s,n=!0,i=!1){if(i)throw e;console.error(e)}let sn=!1,Ci=!1;const qe=[];let Lt=0;const Ps=[];let Qt=null,Ss=0;const ll=Promise.resolve();let to=null;function rl(e){const t=to||ll;return e?t.then(this?e.bind(this):e):t}function Cc(e){let t=sn?Lt+1:0,s=qe.length;for(;t<s;){const n=t+s>>>1,i=qe[n],o=nn(i);o<e||o===e&&i.flags&2?t=n+1:s=n}return t}function so(e){if(!(e.flags&1)){const t=nn(e),s=qe[qe.length-1];!s||!(e.flags&2)&&t>=nn(s)?qe.push(e):qe.splice(Cc(t),0,e),e.flags|=1,cl()}}function cl(){!sn&&!Ci&&(Ci=!0,to=ll.then(ul))}function Sc(e){q(e)?Ps.push(...e):Qt&&e.id===-1?Qt.splice(Ss+1,0,e):e.flags&1||(Ps.push(e),e.flags|=1),cl()}function Po(e,t,s=sn?Lt+1:0){for(;s<qe.length;s++){const n=qe[s];if(n&&n.flags&2){if(e&&n.id!==e.uid)continue;qe.splice(s,1),s--,n.flags&4&&(n.flags&=-2),n(),n.flags&4||(n.flags&=-2)}}}function fl(e){if(Ps.length){const t=[...new Set(Ps)].sort((s,n)=>nn(s)-nn(n));if(Ps.length=0,Qt){Qt.push(...t);return}for(Qt=t,Ss=0;Ss<Qt.length;Ss++){const s=Qt[Ss];s.flags&4&&(s.flags&=-2),s.flags&8||s(),s.flags&=-2}Qt=null,Ss=0}}const nn=e=>e.id==null?e.flags&2?-1:1/0:e.id;function ul(e){Ci=!1,sn=!0;try{for(Lt=0;Lt<qe.length;Lt++){const t=qe[Lt];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),dn(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Lt<qe.length;Lt++){const t=qe[Lt];t&&(t.flags&=-2)}Lt=0,qe.length=0,fl(),sn=!1,to=null,(qe.length||Ps.length)&&ul()}}let lt=null,pl=null;function Un(e){const t=lt;return lt=e,pl=e&&e.type.__scopeId||null,t}function Tc(e,t=lt,s){if(!t||e._n)return e;const n=(...i)=>{n._d&&Fo(-1);const o=Un(t);let a;try{a=e(...i)}finally{Un(o),n._d&&Fo(1)}return a};return n._n=!0,n._c=!0,n._d=!0,n}function B(e,t){if(lt===null)return e;const s=qn(lt),n=e.dirs||(e.dirs=[]);for(let i=0;i<t.length;i++){let[o,a,l,r=Ce]=t[i];o&&(te(o)&&(o={mounted:o,updated:o}),o.deep&&Vt(a),n.push({dir:o,instance:s,value:a,oldValue:void 0,arg:l,modifiers:r}))}return e}function ls(e,t,s,n){const i=e.dirs,o=t&&t.dirs;for(let a=0;a<i.length;a++){const l=i[a];o&&(l.oldValue=o[a].value);let r=l.dir[n];r&&(ts(),xt(r,s,8,[e.el,l,e,t]),ss())}}const wc=Symbol("_vte"),Ec=e=>e.__isTeleport;function no(e,t){e.shapeFlag&6&&e.component?(e.transition=t,no(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function mn(e,t){return te(e)?We({name:e.name},t,{setup:e}):e}function gl(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Si(e,t,s,n,i=!1){if(q(e)){e.forEach((D,O)=>Si(D,t&&(q(t)?t[O]:t),s,n,i));return}if(Ks(n)&&!i)return;const o=n.shapeFlag&4?qn(n.component):n.el,a=i?null:o,{i:l,r}=e,u=t&&t.r,f=l.refs===Ce?l.refs={}:l.refs,d=l.setupState,v=de(d),E=d===Ce?()=>!1:D=>ge(v,D);if(u!=null&&u!==r&&(Oe(u)?(f[u]=null,E(u)&&(d[u]=null)):Ue(u)&&(u.value=null)),te(r))dn(r,l,12,[a,f]);else{const D=Oe(r),O=Ue(r);if(D||O){const W=()=>{if(e.f){const b=D?E(r)?d[r]:f[r]:r.value;i?q(b)&&$i(b,o):q(b)?b.includes(o)||b.push(o):D?(f[r]=[o],E(r)&&(d[r]=f[r])):(r.value=[o],e.k&&(f[e.k]=r.value))}else D?(f[r]=a,E(r)&&(d[r]=a)):O&&(r.value=a,e.k&&(f[e.k]=a))};a?(W.id=-1,ot(W,s)):W()}}}const Ks=e=>!!e.type.__asyncLoader,dl=e=>e.type.__isKeepAlive;function zc(e,t){ml(e,"a",t)}function Ic(e,t){ml(e,"da",t)}function ml(e,t,s=He){const n=e.__wdc||(e.__wdc=()=>{let i=s;for(;i;){if(i.isDeactivated)return;i=i.parent}return e()});if(Kn(t,n,s),s){let i=s.parent;for(;i&&i.parent;)dl(i.parent.vnode)&&Lc(n,t,s,i),i=i.parent}}function Lc(e,t,s,n){const i=Kn(t,e,n,!0);io(()=>{$i(n[t],i)},s)}function Kn(e,t,s=He,n=!1){if(s){const i=s[e]||(s[e]=[]),o=t.__weh||(t.__weh=(...a)=>{ts();const l=yn(s),r=xt(t,s,e,a);return l(),ss(),r});return n?i.unshift(o):i.push(o),o}}const Gt=e=>(t,s=He)=>{(!Qn||e==="sp")&&Kn(e,(...n)=>t(...n),s)},Pc=Gt("bm"),Js=Gt("m"),Mc=Gt("bu"),Ac=Gt("u"),xc=Gt("bum"),io=Gt("um"),Nc=Gt("sp"),Dc=Gt("rtg"),Oc=Gt("rtc");function Rc(e,t=He){Kn("ec",e,t)}const Fc=Symbol.for("v-ndc");function Ft(e,t,s,n){let i;const o=s,a=q(e);if(a||Oe(e)){const l=a&&Ls(e);let r=!1;l&&(r=!ht(e),e=Bn(e)),i=new Array(e.length);for(let u=0,f=e.length;u<f;u++)i[u]=t(r?$e(e[u]):e[u],u,void 0,o)}else if(typeof e=="number"){i=new Array(e);for(let l=0;l<e;l++)i[l]=t(l+1,l,void 0,o)}else if(Se(e))if(e[Symbol.iterator])i=Array.from(e,(l,r)=>t(l,r,void 0,o));else{const l=Object.keys(e);i=new Array(l.length);for(let r=0,u=l.length;r<u;r++){const f=l[r];i[r]=t(e[f],f,r,o)}}else i=[];return i}const Ti=e=>e?Ol(e)?qn(e):Ti(e.parent):null,Qs=We(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Ti(e.parent),$root:e=>Ti(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>oo(e),$forceUpdate:e=>e.f||(e.f=()=>{so(e.update)}),$nextTick:e=>e.n||(e.n=rl.bind(e.proxy)),$watch:e=>af.bind(e)}),ui=(e,t)=>e!==Ce&&!e.__isScriptSetup&&ge(e,t),kc={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:s,setupState:n,data:i,props:o,accessCache:a,type:l,appContext:r}=e;let u;if(t[0]!=="$"){const E=a[t];if(E!==void 0)switch(E){case 1:return n[t];case 2:return i[t];case 4:return s[t];case 3:return o[t]}else{if(ui(n,t))return a[t]=1,n[t];if(i!==Ce&&ge(i,t))return a[t]=2,i[t];if((u=e.propsOptions[0])&&ge(u,t))return a[t]=3,o[t];if(s!==Ce&&ge(s,t))return a[t]=4,s[t];wi&&(a[t]=0)}}const f=Qs[t];let d,v;if(f)return t==="$attrs"&&Ye(e.attrs,"get",""),f(e);if((d=l.__cssModules)&&(d=d[t]))return d;if(s!==Ce&&ge(s,t))return a[t]=4,s[t];if(v=r.config.globalProperties,ge(v,t))return v[t]},set({_:e},t,s){const{data:n,setupState:i,ctx:o}=e;return ui(i,t)?(i[t]=s,!0):n!==Ce&&ge(n,t)?(n[t]=s,!0):ge(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(o[t]=s,!0)},has({_:{data:e,setupState:t,accessCache:s,ctx:n,appContext:i,propsOptions:o}},a){let l;return!!s[a]||e!==Ce&&ge(e,a)||ui(t,a)||(l=o[0])&&ge(l,a)||ge(n,a)||ge(Qs,a)||ge(i.config.globalProperties,a)},defineProperty(e,t,s){return s.get!=null?e._.accessCache[t]=0:ge(s,"value")&&this.set(e,t,s.value,null),Reflect.defineProperty(e,t,s)}};function Mo(e){return q(e)?e.reduce((t,s)=>(t[s]=null,t),{}):e}let wi=!0;function Uc(e){const t=oo(e),s=e.proxy,n=e.ctx;wi=!1,t.beforeCreate&&Ao(t.beforeCreate,e,"bc");const{data:i,computed:o,methods:a,watch:l,provide:r,inject:u,created:f,beforeMount:d,mounted:v,beforeUpdate:E,updated:D,activated:O,deactivated:W,beforeDestroy:b,beforeUnmount:z,destroyed:P,unmounted:T,render:F,renderTracked:H,renderTriggered:k,errorCaptured:ee,serverPrefetch:ne,expose:_e,inheritAttrs:Ee,components:ve,directives:Ze,filters:ft}=t;if(u&&Wc(u,n,null),a)for(const ce in a){const ae=a[ce];te(ae)&&(n[ce]=ae.bind(s))}if(i){const ce=i.call(s,s);Se(ce)&&(e.data=Qi(ce))}if(wi=!0,o)for(const ce in o){const ae=o[ce],Pe=te(ae)?ae.bind(s,s):te(ae.get)?ae.get.bind(s,s):Mt,Be=!te(ae)&&te(ae.set)?ae.set.bind(s):Mt,Me=Ts({get:Pe,set:Be});Object.defineProperty(n,ce,{enumerable:!0,configurable:!0,get:()=>Me.value,set:Ae=>Me.value=Ae})}if(l)for(const ce in l)hl(l[ce],n,s,ce);if(r){const ce=te(r)?r.call(s):r;Reflect.ownKeys(ce).forEach(ae=>{Bc(ae,ce[ae])})}f&&Ao(f,e,"c");function ze(ce,ae){q(ae)?ae.forEach(Pe=>ce(Pe.bind(s))):ae&&ce(ae.bind(s))}if(ze(Pc,d),ze(Js,v),ze(Mc,E),ze(Ac,D),ze(zc,O),ze(Ic,W),ze(Rc,ee),ze(Oc,H),ze(Dc,k),ze(xc,z),ze(io,T),ze(Nc,ne),q(_e))if(_e.length){const ce=e.exposed||(e.exposed={});_e.forEach(ae=>{Object.defineProperty(ce,ae,{get:()=>s[ae],set:Pe=>s[ae]=Pe})})}else e.exposed||(e.exposed={});F&&e.render===Mt&&(e.render=F),Ee!=null&&(e.inheritAttrs=Ee),ve&&(e.components=ve),Ze&&(e.directives=Ze),ne&&gl(e)}function Wc(e,t,s=Mt){q(e)&&(e=Ei(e));for(const n in e){const i=e[n];let o;Se(i)?"default"in i?o=qs(i.from||n,i.default,!0):o=qs(i.from||n):o=qs(i),Ue(o)?Object.defineProperty(t,n,{enumerable:!0,configurable:!0,get:()=>o.value,set:a=>o.value=a}):t[n]=o}}function Ao(e,t,s){xt(q(e)?e.map(n=>n.bind(t.proxy)):e.bind(t.proxy),t,s)}function hl(e,t,s,n){let i=n.includes(".")?Ml(s,n):()=>s[n];if(Oe(e)){const o=t[e];te(o)&&Yt(i,o)}else if(te(e))Yt(i,e.bind(s));else if(Se(e))if(q(e))e.forEach(o=>hl(o,t,s,n));else{const o=te(e.handler)?e.handler.bind(s):t[e.handler];te(o)&&Yt(i,o,e)}}function oo(e){const t=e.type,{mixins:s,extends:n}=t,{mixins:i,optionsCache:o,config:{optionMergeStrategies:a}}=e.appContext,l=o.get(t);let r;return l?r=l:!i.length&&!s&&!n?r=t:(r={},i.length&&i.forEach(u=>Wn(r,u,a,!0)),Wn(r,t,a)),Se(t)&&o.set(t,r),r}function Wn(e,t,s,n=!1){const{mixins:i,extends:o}=t;o&&Wn(e,o,s,!0),i&&i.forEach(a=>Wn(e,a,s,!0));for(const a in t)if(!(n&&a==="expose")){const l=Vc[a]||s&&s[a];e[a]=l?l(e[a],t[a]):t[a]}return e}const Vc={data:xo,props:No,emits:No,methods:Bs,computed:Bs,beforeCreate:Je,created:Je,beforeMount:Je,mounted:Je,beforeUpdate:Je,updated:Je,beforeDestroy:Je,beforeUnmount:Je,destroyed:Je,unmounted:Je,activated:Je,deactivated:Je,errorCaptured:Je,serverPrefetch:Je,components:Bs,directives:Bs,watch:Hc,provide:xo,inject:$c};function xo(e,t){return t?e?function(){return We(te(e)?e.call(this,this):e,te(t)?t.call(this,this):t)}:t:e}function $c(e,t){return Bs(Ei(e),Ei(t))}function Ei(e){if(q(e)){const t={};for(let s=0;s<e.length;s++)t[e[s]]=e[s];return t}return e}function Je(e,t){return e?[...new Set([].concat(e,t))]:t}function Bs(e,t){return e?We(Object.create(null),e,t):t}function No(e,t){return e?q(e)&&q(t)?[...new Set([...e,...t])]:We(Object.create(null),Mo(e),Mo(t??{})):t}function Hc(e,t){if(!e)return t;if(!t)return e;const s=We(Object.create(null),e);for(const n in t)s[n]=Je(e[n],t[n]);return s}function yl(){return{app:null,config:{isNativeTag:Mr,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let jc=0;function Yc(e,t){return function(n,i=null){te(n)||(n=We({},n)),i!=null&&!Se(i)&&(i=null);const o=yl(),a=new WeakSet,l=[];let r=!1;const u=o.app={_uid:jc++,_component:n,_props:i,_container:null,_context:o,_instance:null,version:If,get config(){return o.config},set config(f){},use(f,...d){return a.has(f)||(f&&te(f.install)?(a.add(f),f.install(u,...d)):te(f)&&(a.add(f),f(u,...d))),u},mixin(f){return o.mixins.includes(f)||o.mixins.push(f),u},component(f,d){return d?(o.components[f]=d,u):o.components[f]},directive(f,d){return d?(o.directives[f]=d,u):o.directives[f]},mount(f,d,v){if(!r){const E=u._ceVNode||je(n,i);return E.appContext=o,v===!0?v="svg":v===!1&&(v=void 0),d&&t?t(E,f):e(E,f,v),r=!0,u._container=f,f.__vue_app__=u,qn(E.component)}},onUnmount(f){l.push(f)},unmount(){r&&(xt(l,u._instance,16),e(null,u._container),delete u._container.__vue_app__)},provide(f,d){return o.provides[f]=d,u},runWithContext(f){const d=Ms;Ms=u;try{return f()}finally{Ms=d}}};return u}}let Ms=null;function Bc(e,t){if(He){let s=He.provides;const n=He.parent&&He.parent.provides;n===s&&(s=He.provides=Object.create(n)),s[e]=t}}function qs(e,t,s=!1){const n=He||lt;if(n||Ms){const i=Ms?Ms._context.provides:n?n.parent==null?n.vnode.appContext&&n.vnode.appContext.provides:n.parent.provides:void 0;if(i&&e in i)return i[e];if(arguments.length>1)return s&&te(t)?t.call(n&&n.proxy):t}}const _l={},vl=()=>Object.create(_l),bl=e=>Object.getPrototypeOf(e)===_l;function Gc(e,t,s,n=!1){const i={},o=vl();e.propsDefaults=Object.create(null),Cl(e,t,i,o);for(const a in e.propsOptions[0])a in i||(i[a]=void 0);s?e.props=n?i:uc(i):e.type.props?e.props=i:e.props=o,e.attrs=o}function Xc(e,t,s,n){const{props:i,attrs:o,vnode:{patchFlag:a}}=e,l=de(i),[r]=e.propsOptions;let u=!1;if((n||a>0)&&!(a&16)){if(a&8){const f=e.vnode.dynamicProps;for(let d=0;d<f.length;d++){let v=f[d];if(Jn(e.emitsOptions,v))continue;const E=t[v];if(r)if(ge(o,v))E!==o[v]&&(o[v]=E,u=!0);else{const D=gs(v);i[D]=zi(r,l,D,E,e,!1)}else E!==o[v]&&(o[v]=E,u=!0)}}}else{Cl(e,t,i,o)&&(u=!0);let f;for(const d in l)(!t||!ge(t,d)&&((f=hs(d))===d||!ge(t,f)))&&(r?s&&(s[d]!==void 0||s[f]!==void 0)&&(i[d]=zi(r,l,d,void 0,e,!0)):delete i[d]);if(o!==l)for(const d in o)(!t||!ge(t,d))&&(delete o[d],u=!0)}u&&jt(e.attrs,"set","")}function Cl(e,t,s,n){const[i,o]=e.propsOptions;let a=!1,l;if(t)for(let r in t){if(Gs(r))continue;const u=t[r];let f;i&&ge(i,f=gs(r))?!o||!o.includes(f)?s[f]=u:(l||(l={}))[f]=u:Jn(e.emitsOptions,r)||(!(r in n)||u!==n[r])&&(n[r]=u,a=!0)}if(o){const r=de(s),u=l||Ce;for(let f=0;f<o.length;f++){const d=o[f];s[d]=zi(i,r,d,u[d],e,!ge(u,d))}}return a}function zi(e,t,s,n,i,o){const a=e[s];if(a!=null){const l=ge(a,"default");if(l&&n===void 0){const r=a.default;if(a.type!==Function&&!a.skipFactory&&te(r)){const{propsDefaults:u}=i;if(s in u)n=u[s];else{const f=yn(i);n=u[s]=r.call(null,t),f()}}else n=r;i.ce&&i.ce._setProp(s,n)}a[0]&&(o&&!l?n=!1:a[1]&&(n===""||n===hs(s))&&(n=!0))}return n}const Kc=new WeakMap;function Sl(e,t,s=!1){const n=s?Kc:t.propsCache,i=n.get(e);if(i)return i;const o=e.props,a={},l=[];let r=!1;if(!te(e)){const f=d=>{r=!0;const[v,E]=Sl(d,t,!0);We(a,v),E&&l.push(...E)};!s&&t.mixins.length&&t.mixins.forEach(f),e.extends&&f(e.extends),e.mixins&&e.mixins.forEach(f)}if(!o&&!r)return Se(e)&&n.set(e,Es),Es;if(q(o))for(let f=0;f<o.length;f++){const d=gs(o[f]);Do(d)&&(a[d]=Ce)}else if(o)for(const f in o){const d=gs(f);if(Do(d)){const v=o[f],E=a[d]=q(v)||te(v)?{type:v}:We({},v),D=E.type;let O=!1,W=!0;if(q(D))for(let b=0;b<D.length;++b){const z=D[b],P=te(z)&&z.name;if(P==="Boolean"){O=!0;break}else P==="String"&&(W=!1)}else O=te(D)&&D.name==="Boolean";E[0]=O,E[1]=W,(O||ge(E,"default"))&&l.push(d)}}const u=[a,l];return Se(e)&&n.set(e,u),u}function Do(e){return e[0]!=="$"&&!Gs(e)}const Tl=e=>e[0]==="_"||e==="$stable",ao=e=>q(e)?e.map(Pt):[Pt(e)],Jc=(e,t,s)=>{if(t._n)return t;const n=Tc((...i)=>ao(t(...i)),s);return n._c=!1,n},wl=(e,t,s)=>{const n=e._ctx;for(const i in e){if(Tl(i))continue;const o=e[i];if(te(o))t[i]=Jc(i,o,n);else if(o!=null){const a=ao(o);t[i]=()=>a}}},El=(e,t)=>{const s=ao(t);e.slots.default=()=>s},zl=(e,t,s)=>{for(const n in t)(s||n!=="_")&&(e[n]=t[n])},Qc=(e,t,s)=>{const n=e.slots=vl();if(e.vnode.shapeFlag&32){const i=t._;i?(zl(n,t,s),s&&ka(n,"_",i,!0)):wl(t,n)}else t&&El(e,t)},qc=(e,t,s)=>{const{vnode:n,slots:i}=e;let o=!0,a=Ce;if(n.shapeFlag&32){const l=t._;l?s&&l===1?o=!1:zl(i,t,s):(o=!t.$stable,wl(t,i)),a=t}else t&&(El(e,t),a={default:1});if(o)for(const l in i)!Tl(l)&&a[l]==null&&delete i[l]},ot=gf;function Zc(e){return ef(e)}function ef(e,t){const s=Ua();s.__VUE__=!0;const{insert:n,remove:i,patchProp:o,createElement:a,createText:l,createComment:r,setText:u,setElementText:f,parentNode:d,nextSibling:v,setScopeId:E=Mt,insertStaticContent:D}=e,O=(m,h,L,U=null,R=null,c=null,g=void 0,S=null,I=!!h.dynamicChildren)=>{if(m===h)return;m&&!Hs(m,h)&&(U=et(m),Ae(m,R,c,!0),m=null),h.patchFlag===-2&&(I=!1,h.dynamicChildren=null);const{type:A,ref:V,shapeFlag:y}=h;switch(A){case hn:W(m,h,L,U);break;case ms:b(m,h,L,U);break;case xn:m==null&&z(h,L,U,g);break;case Le:ve(m,h,L,U,R,c,g,S,I);break;default:y&1?F(m,h,L,U,R,c,g,S,I):y&6?Ze(m,h,L,U,R,c,g,S,I):(y&64||y&128)&&A.process(m,h,L,U,R,c,g,S,I,Ge)}V!=null&&R&&Si(V,m&&m.ref,c,h||m,!h)},W=(m,h,L,U)=>{if(m==null)n(h.el=l(h.children),L,U);else{const R=h.el=m.el;h.children!==m.children&&u(R,h.children)}},b=(m,h,L,U)=>{m==null?n(h.el=r(h.children||""),L,U):h.el=m.el},z=(m,h,L,U)=>{[m.el,m.anchor]=D(m.children,h,L,U,m.el,m.anchor)},P=({el:m,anchor:h},L,U)=>{let R;for(;m&&m!==h;)R=v(m),n(m,L,U),m=R;n(h,L,U)},T=({el:m,anchor:h})=>{let L;for(;m&&m!==h;)L=v(m),i(m),m=L;i(h)},F=(m,h,L,U,R,c,g,S,I)=>{h.type==="svg"?g="svg":h.type==="math"&&(g="mathml"),m==null?H(h,L,U,R,c,g,S,I):ne(m,h,R,c,g,S,I)},H=(m,h,L,U,R,c,g,S)=>{let I,A;const{props:V,shapeFlag:y,transition:w,dirs:Y}=m;if(I=m.el=a(m.type,c,V&&V.is,V),y&8?f(I,m.children):y&16&&ee(m.children,I,null,U,R,pi(m,c),g,S),Y&&ls(m,null,U,"created"),k(I,m,m.scopeId,g,U),V){for(const se in V)se!=="value"&&!Gs(se)&&o(I,se,null,V[se],c,U);"value"in V&&o(I,"value",null,V.value,c),(A=V.onVnodeBeforeMount)&&Et(A,U,m)}Y&&ls(m,null,U,"beforeMount");const X=tf(R,w);X&&w.beforeEnter(I),n(I,h,L),((A=V&&V.onVnodeMounted)||X||Y)&&ot(()=>{A&&Et(A,U,m),X&&w.enter(I),Y&&ls(m,null,U,"mounted")},R)},k=(m,h,L,U,R)=>{if(L&&E(m,L),U)for(let c=0;c<U.length;c++)E(m,U[c]);if(R){let c=R.subTree;if(h===c||xl(c.type)&&(c.ssContent===h||c.ssFallback===h)){const g=R.vnode;k(m,g,g.scopeId,g.slotScopeIds,R.parent)}}},ee=(m,h,L,U,R,c,g,S,I=0)=>{for(let A=I;A<m.length;A++){const V=m[A]=S?qt(m[A]):Pt(m[A]);O(null,V,h,L,U,R,c,g,S)}},ne=(m,h,L,U,R,c,g)=>{const S=h.el=m.el;let{patchFlag:I,dynamicChildren:A,dirs:V}=h;I|=m.patchFlag&16;const y=m.props||Ce,w=h.props||Ce;let Y;if(L&&rs(L,!1),(Y=w.onVnodeBeforeUpdate)&&Et(Y,L,h,m),V&&ls(h,m,L,"beforeUpdate"),L&&rs(L,!0),(y.innerHTML&&w.innerHTML==null||y.textContent&&w.textContent==null)&&f(S,""),A?_e(m.dynamicChildren,A,S,L,U,pi(h,R),c):g||ae(m,h,S,null,L,U,pi(h,R),c,!1),I>0){if(I&16)Ee(S,y,w,L,R);else if(I&2&&y.class!==w.class&&o(S,"class",null,w.class,R),I&4&&o(S,"style",y.style,w.style,R),I&8){const X=h.dynamicProps;for(let se=0;se<X.length;se++){const ie=X[se],pe=y[ie],he=w[ie];(he!==pe||ie==="value")&&o(S,ie,pe,he,R,L)}}I&1&&m.children!==h.children&&f(S,h.children)}else!g&&A==null&&Ee(S,y,w,L,R);((Y=w.onVnodeUpdated)||V)&&ot(()=>{Y&&Et(Y,L,h,m),V&&ls(h,m,L,"updated")},U)},_e=(m,h,L,U,R,c,g)=>{for(let S=0;S<h.length;S++){const I=m[S],A=h[S],V=I.el&&(I.type===Le||!Hs(I,A)||I.shapeFlag&70)?d(I.el):L;O(I,A,V,null,U,R,c,g,!0)}},Ee=(m,h,L,U,R)=>{if(h!==L){if(h!==Ce)for(const c in h)!Gs(c)&&!(c in L)&&o(m,c,h[c],null,R,U);for(const c in L){if(Gs(c))continue;const g=L[c],S=h[c];g!==S&&c!=="value"&&o(m,c,S,g,R,U)}"value"in L&&o(m,"value",h.value,L.value,R)}},ve=(m,h,L,U,R,c,g,S,I)=>{const A=h.el=m?m.el:l(""),V=h.anchor=m?m.anchor:l("");let{patchFlag:y,dynamicChildren:w,slotScopeIds:Y}=h;Y&&(S=S?S.concat(Y):Y),m==null?(n(A,L,U),n(V,L,U),ee(h.children||[],L,V,R,c,g,S,I)):y>0&&y&64&&w&&m.dynamicChildren?(_e(m.dynamicChildren,w,L,R,c,g,S),(h.key!=null||R&&h===R.subTree)&&Il(m,h,!0)):ae(m,h,L,V,R,c,g,S,I)},Ze=(m,h,L,U,R,c,g,S,I)=>{h.slotScopeIds=S,m==null?h.shapeFlag&512?R.ctx.activate(h,L,U,g,I):ft(h,L,U,R,c,g,I):ut(m,h,I)},ft=(m,h,L,U,R,c,g)=>{const S=m.component=Cf(m,U,R);if(dl(m)&&(S.ctx.renderer=Ge),Sf(S,!1,g),S.asyncDep){if(R&&R.registerDep(S,ze,g),!m.el){const I=S.subTree=je(ms);b(null,I,h,L)}}else ze(S,m,h,L,R,c,g)},ut=(m,h,L)=>{const U=h.component=m.component;if(uf(m,h,L))if(U.asyncDep&&!U.asyncResolved){ce(U,h,L);return}else U.next=h,U.update();else h.el=m.el,U.vnode=h},ze=(m,h,L,U,R,c,g)=>{const S=()=>{if(m.isMounted){let{next:y,bu:w,u:Y,parent:X,vnode:se}=m;{const ke=Ll(m);if(ke){y&&(y.el=se.el,ce(m,y,g)),ke.asyncDep.then(()=>{m.isUnmounted||S()});return}}let ie=y,pe;rs(m,!1),y?(y.el=se.el,ce(m,y,g)):y=se,w&&An(w),(pe=y.props&&y.props.onVnodeBeforeUpdate)&&Et(pe,X,y,se),rs(m,!0);const he=gi(m),Ve=m.subTree;m.subTree=he,O(Ve,he,d(Ve.el),et(Ve),m,R,c),y.el=he.el,ie===null&&pf(m,he.el),Y&&ot(Y,R),(pe=y.props&&y.props.onVnodeUpdated)&&ot(()=>Et(pe,X,y,se),R)}else{let y;const{el:w,props:Y}=h,{bm:X,m:se,parent:ie,root:pe,type:he}=m,Ve=Ks(h);if(rs(m,!1),X&&An(X),!Ve&&(y=Y&&Y.onVnodeBeforeMount)&&Et(y,ie,h),rs(m,!0),w&&nt){const ke=()=>{m.subTree=gi(m),nt(w,m.subTree,m,R,null)};Ve&&he.__asyncHydrate?he.__asyncHydrate(w,m,ke):ke()}else{pe.ce&&pe.ce._injectChildStyle(he);const ke=m.subTree=gi(m);O(null,ke,L,U,m,R,c),h.el=ke.el}if(se&&ot(se,R),!Ve&&(y=Y&&Y.onVnodeMounted)){const ke=h;ot(()=>Et(y,ie,ke),R)}(h.shapeFlag&256||ie&&Ks(ie.vnode)&&ie.vnode.shapeFlag&256)&&m.a&&ot(m.a,R),m.isMounted=!0,h=L=U=null}};m.scope.on();const I=m.effect=new ja(S);m.scope.off();const A=m.update=I.run.bind(I),V=m.job=I.runIfDirty.bind(I);V.i=m,V.id=m.uid,I.scheduler=()=>so(V),rs(m,!0),A()},ce=(m,h,L)=>{h.component=m;const U=m.vnode.props;m.vnode=h,m.next=null,Xc(m,h.props,U,L),qc(m,h.children,L),ts(),Po(m),ss()},ae=(m,h,L,U,R,c,g,S,I=!1)=>{const A=m&&m.children,V=m?m.shapeFlag:0,y=h.children,{patchFlag:w,shapeFlag:Y}=h;if(w>0){if(w&128){Be(A,y,L,U,R,c,g,S,I);return}else if(w&256){Pe(A,y,L,U,R,c,g,S,I);return}}Y&8?(V&16&&yt(A,R,c),y!==A&&f(L,y)):V&16?Y&16?Be(A,y,L,U,R,c,g,S,I):yt(A,R,c,!0):(V&8&&f(L,""),Y&16&&ee(y,L,U,R,c,g,S,I))},Pe=(m,h,L,U,R,c,g,S,I)=>{m=m||Es,h=h||Es;const A=m.length,V=h.length,y=Math.min(A,V);let w;for(w=0;w<y;w++){const Y=h[w]=I?qt(h[w]):Pt(h[w]);O(m[w],Y,L,null,R,c,g,S,I)}A>V?yt(m,R,c,!0,!1,y):ee(h,L,U,R,c,g,S,I,y)},Be=(m,h,L,U,R,c,g,S,I)=>{let A=0;const V=h.length;let y=m.length-1,w=V-1;for(;A<=y&&A<=w;){const Y=m[A],X=h[A]=I?qt(h[A]):Pt(h[A]);if(Hs(Y,X))O(Y,X,L,null,R,c,g,S,I);else break;A++}for(;A<=y&&A<=w;){const Y=m[y],X=h[w]=I?qt(h[w]):Pt(h[w]);if(Hs(Y,X))O(Y,X,L,null,R,c,g,S,I);else break;y--,w--}if(A>y){if(A<=w){const Y=w+1,X=Y<V?h[Y].el:U;for(;A<=w;)O(null,h[A]=I?qt(h[A]):Pt(h[A]),L,X,R,c,g,S,I),A++}}else if(A>w)for(;A<=y;)Ae(m[A],R,c,!0),A++;else{const Y=A,X=A,se=new Map;for(A=X;A<=w;A++){const xe=h[A]=I?qt(h[A]):Pt(h[A]);xe.key!=null&&se.set(xe.key,A)}let ie,pe=0;const he=w-X+1;let Ve=!1,ke=0;const Nt=new Array(he);for(A=0;A<he;A++)Nt[A]=0;for(A=Y;A<=y;A++){const xe=m[A];if(pe>=he){Ae(xe,R,c,!0);continue}let it;if(xe.key!=null)it=se.get(xe.key);else for(ie=X;ie<=w;ie++)if(Nt[ie-X]===0&&Hs(xe,h[ie])){it=ie;break}it===void 0?Ae(xe,R,c,!0):(Nt[it-X]=A+1,it>=ke?ke=it:Ve=!0,O(xe,h[it],L,null,R,c,g,S,I),pe++)}const ys=Ve?sf(Nt):Es;for(ie=ys.length-1,A=he-1;A>=0;A--){const xe=X+A,it=h[xe],Xe=xe+1<V?h[xe+1].el:U;Nt[A]===0?O(null,it,L,Xe,R,c,g,S,I):Ve&&(ie<0||A!==ys[ie]?Me(it,L,Xe,2):ie--)}}},Me=(m,h,L,U,R=null)=>{const{el:c,type:g,transition:S,children:I,shapeFlag:A}=m;if(A&6){Me(m.component.subTree,h,L,U);return}if(A&128){m.suspense.move(h,L,U);return}if(A&64){g.move(m,h,L,Ge);return}if(g===Le){n(c,h,L);for(let y=0;y<I.length;y++)Me(I[y],h,L,U);n(m.anchor,h,L);return}if(g===xn){P(m,h,L);return}if(U!==2&&A&1&&S)if(U===0)S.beforeEnter(c),n(c,h,L),ot(()=>S.enter(c),R);else{const{leave:y,delayLeave:w,afterLeave:Y}=S,X=()=>n(c,h,L),se=()=>{y(c,()=>{X(),Y&&Y()})};w?w(c,X,se):se()}else n(c,h,L)},Ae=(m,h,L,U=!1,R=!1)=>{const{type:c,props:g,ref:S,children:I,dynamicChildren:A,shapeFlag:V,patchFlag:y,dirs:w,cacheIndex:Y}=m;if(y===-2&&(R=!1),S!=null&&Si(S,null,L,m,!0),Y!=null&&(h.renderCache[Y]=void 0),V&256){h.ctx.deactivate(m);return}const X=V&1&&w,se=!Ks(m);let ie;if(se&&(ie=g&&g.onVnodeBeforeUnmount)&&Et(ie,h,m),V&6)wt(m.component,L,U);else{if(V&128){m.suspense.unmount(L,U);return}X&&ls(m,null,h,"beforeUnmount"),V&64?m.type.remove(m,h,L,Ge,U):A&&!A.hasOnce&&(c!==Le||y>0&&y&64)?yt(A,h,L,!1,!0):(c===Le&&y&384||!R&&V&16)&&yt(I,h,L),U&&Tt(m)}(se&&(ie=g&&g.onVnodeUnmounted)||X)&&ot(()=>{ie&&Et(ie,h,m),X&&ls(m,null,h,"unmounted")},L)},Tt=m=>{const{type:h,el:L,anchor:U,transition:R}=m;if(h===Le){pt(L,U);return}if(h===xn){T(m);return}const c=()=>{i(L),R&&!R.persisted&&R.afterLeave&&R.afterLeave()};if(m.shapeFlag&1&&R&&!R.persisted){const{leave:g,delayLeave:S}=R,I=()=>g(L,c);S?S(m.el,c,I):I()}else c()},pt=(m,h)=>{let L;for(;m!==h;)L=v(m),i(m),m=L;i(h)},wt=(m,h,L)=>{const{bum:U,scope:R,job:c,subTree:g,um:S,m:I,a:A}=m;Oo(I),Oo(A),U&&An(U),R.stop(),c&&(c.flags|=8,Ae(g,m,h,L)),S&&ot(S,h),ot(()=>{m.isUnmounted=!0},h),h&&h.pendingBranch&&!h.isUnmounted&&m.asyncDep&&!m.asyncResolved&&m.suspenseId===h.pendingId&&(h.deps--,h.deps===0&&h.resolve())},yt=(m,h,L,U=!1,R=!1,c=0)=>{for(let g=c;g<m.length;g++)Ae(m[g],h,L,U,R)},et=m=>{if(m.shapeFlag&6)return et(m.component.subTree);if(m.shapeFlag&128)return m.suspense.next();const h=v(m.anchor||m.el),L=h&&h[wc];return L?v(L):h};let _t=!1;const gt=(m,h,L)=>{m==null?h._vnode&&Ae(h._vnode,null,null,!0):O(h._vnode||null,m,h,null,null,null,L),h._vnode=m,_t||(_t=!0,Po(),fl(),_t=!1)},Ge={p:O,um:Ae,m:Me,r:Tt,mt:ft,mc:ee,pc:ae,pbc:_e,n:et,o:e};let st,nt;return{render:gt,hydrate:st,createApp:Yc(gt,st)}}function pi({type:e,props:t},s){return s==="svg"&&e==="foreignObject"||s==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:s}function rs({effect:e,job:t},s){s?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function tf(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Il(e,t,s=!1){const n=e.children,i=t.children;if(q(n)&&q(i))for(let o=0;o<n.length;o++){const a=n[o];let l=i[o];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=i[o]=qt(i[o]),l.el=a.el),!s&&l.patchFlag!==-2&&Il(a,l)),l.type===hn&&(l.el=a.el)}}function sf(e){const t=e.slice(),s=[0];let n,i,o,a,l;const r=e.length;for(n=0;n<r;n++){const u=e[n];if(u!==0){if(i=s[s.length-1],e[i]<u){t[n]=i,s.push(n);continue}for(o=0,a=s.length-1;o<a;)l=o+a>>1,e[s[l]]<u?o=l+1:a=l;u<e[s[o]]&&(o>0&&(t[n]=s[o-1]),s[o]=n)}}for(o=s.length,a=s[o-1];o-- >0;)s[o]=a,a=t[a];return s}function Ll(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Ll(t)}function Oo(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const nf=Symbol.for("v-scx"),of=()=>qs(nf);function Yt(e,t,s){return Pl(e,t,s)}function Pl(e,t,s=Ce){const{immediate:n,deep:i,flush:o,once:a}=s,l=We({},s);let r;if(Qn)if(o==="sync"){const v=of();r=v.__watcherHandles||(v.__watcherHandles=[])}else if(!t||n)l.once=!0;else{const v=()=>{};return v.stop=Mt,v.resume=Mt,v.pause=Mt,v}const u=He;l.call=(v,E,D)=>xt(v,u,E,D);let f=!1;o==="post"?l.scheduler=v=>{ot(v,u&&u.suspense)}:o!=="sync"&&(f=!0,l.scheduler=(v,E)=>{E?v():so(v)}),l.augmentJob=v=>{t&&(v.flags|=4),f&&(v.flags|=2,u&&(v.id=u.uid,v.i=u))};const d=vc(e,t,l);return r&&r.push(d),d}function af(e,t,s){const n=this.proxy,i=Oe(e)?e.includes(".")?Ml(n,e):()=>n[e]:e.bind(n,n);let o;te(t)?o=t:(o=t.handler,s=t);const a=yn(this),l=Pl(i,o.bind(n),s);return a(),l}function Ml(e,t){const s=t.split(".");return()=>{let n=e;for(let i=0;i<s.length&&n;i++)n=n[s[i]];return n}}const lf=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${gs(t)}Modifiers`]||e[`${hs(t)}Modifiers`];function rf(e,t,...s){if(e.isUnmounted)return;const n=e.vnode.props||Ce;let i=s;const o=t.startsWith("update:"),a=o&&lf(n,t.slice(7));a&&(a.trim&&(i=s.map(f=>Oe(f)?f.trim():f)),a.number&&(i=s.map(Rn)));let l,r=n[l=ai(t)]||n[l=ai(gs(t))];!r&&o&&(r=n[l=ai(hs(t))]),r&&xt(r,e,6,i);const u=n[l+"Once"];if(u){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,xt(u,e,6,i)}}function Al(e,t,s=!1){const n=t.emitsCache,i=n.get(e);if(i!==void 0)return i;const o=e.emits;let a={},l=!1;if(!te(e)){const r=u=>{const f=Al(u,t,!0);f&&(l=!0,We(a,f))};!s&&t.mixins.length&&t.mixins.forEach(r),e.extends&&r(e.extends),e.mixins&&e.mixins.forEach(r)}return!o&&!l?(Se(e)&&n.set(e,null),null):(q(o)?o.forEach(r=>a[r]=null):We(a,o),Se(e)&&n.set(e,a),a)}function Jn(e,t){return!e||!jn(t)?!1:(t=t.slice(2).replace(/Once$/,""),ge(e,t[0].toLowerCase()+t.slice(1))||ge(e,hs(t))||ge(e,t))}function gi(e){const{type:t,vnode:s,proxy:n,withProxy:i,propsOptions:[o],slots:a,attrs:l,emit:r,render:u,renderCache:f,props:d,data:v,setupState:E,ctx:D,inheritAttrs:O}=e,W=Un(e);let b,z;try{if(s.shapeFlag&4){const T=i||n,F=T;b=Pt(u.call(F,T,f,d,E,v,D)),z=l}else{const T=t;b=Pt(T.length>1?T(d,{attrs:l,slots:a,emit:r}):T(d,null)),z=t.props?l:cf(l)}}catch(T){Zs.length=0,Xn(T,e,1),b=je(ms)}let P=b;if(z&&O!==!1){const T=Object.keys(z),{shapeFlag:F}=P;T.length&&F&7&&(o&&T.some(Vi)&&(z=ff(z,o)),P=As(P,z,!1,!0))}return s.dirs&&(P=As(P,null,!1,!0),P.dirs=P.dirs?P.dirs.concat(s.dirs):s.dirs),s.transition&&no(P,s.transition),b=P,Un(W),b}const cf=e=>{let t;for(const s in e)(s==="class"||s==="style"||jn(s))&&((t||(t={}))[s]=e[s]);return t},ff=(e,t)=>{const s={};for(const n in e)(!Vi(n)||!(n.slice(9)in t))&&(s[n]=e[n]);return s};function uf(e,t,s){const{props:n,children:i,component:o}=e,{props:a,children:l,patchFlag:r}=t,u=o.emitsOptions;if(t.dirs||t.transition)return!0;if(s&&r>=0){if(r&1024)return!0;if(r&16)return n?Ro(n,a,u):!!a;if(r&8){const f=t.dynamicProps;for(let d=0;d<f.length;d++){const v=f[d];if(a[v]!==n[v]&&!Jn(u,v))return!0}}}else return(i||l)&&(!l||!l.$stable)?!0:n===a?!1:n?a?Ro(n,a,u):!0:!!a;return!1}function Ro(e,t,s){const n=Object.keys(t);if(n.length!==Object.keys(e).length)return!0;for(let i=0;i<n.length;i++){const o=n[i];if(t[o]!==e[o]&&!Jn(s,o))return!0}return!1}function pf({vnode:e,parent:t},s){for(;t;){const n=t.subTree;if(n.suspense&&n.suspense.activeBranch===e&&(n.el=e.el),n===e)(e=t.vnode).el=s,t=t.parent;else break}}const xl=e=>e.__isSuspense;function gf(e,t){t&&t.pendingBranch?q(e)?t.effects.push(...e):t.effects.push(e):Sc(e)}const Le=Symbol.for("v-fgt"),hn=Symbol.for("v-txt"),ms=Symbol.for("v-cmt"),xn=Symbol.for("v-stc"),Zs=[];let rt=null;function le(e=!1){Zs.push(rt=e?null:[])}function df(){Zs.pop(),rt=Zs[Zs.length-1]||null}let on=1;function Fo(e){on+=e,e<0&&rt&&(rt.hasOnce=!0)}function Nl(e){return e.dynamicChildren=on>0?rt||Es:null,df(),on>0&&rt&&rt.push(e),e}function fe(e,t,s,n,i,o){return Nl(p(e,t,s,n,i,o,!0))}function mf(e,t,s,n,i){return Nl(je(e,t,s,n,i,!0))}function Ii(e){return e?e.__v_isVNode===!0:!1}function Hs(e,t){return e.type===t.type&&e.key===t.key}const Dl=({key:e})=>e??null,Nn=({ref:e,ref_key:t,ref_for:s})=>(typeof e=="number"&&(e=""+e),e!=null?Oe(e)||Ue(e)||te(e)?{i:lt,r:e,k:t,f:!!s}:e:null);function p(e,t=null,s=null,n=0,i=null,o=e===Le?0:1,a=!1,l=!1){const r={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Dl(t),ref:t&&Nn(t),scopeId:pl,slotScopeIds:null,children:s,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:o,patchFlag:n,dynamicProps:i,dynamicChildren:null,appContext:null,ctx:lt};return l?(lo(r,s),o&128&&e.normalize(r)):s&&(r.shapeFlag|=Oe(s)?8:16),on>0&&!a&&rt&&(r.patchFlag>0||o&6)&&r.patchFlag!==32&&rt.push(r),r}const je=hf;function hf(e,t=null,s=null,n=0,i=null,o=!1){if((!e||e===Fc)&&(e=ms),Ii(e)){const l=As(e,t,!0);return s&&lo(l,s),on>0&&!o&&rt&&(l.shapeFlag&6?rt[rt.indexOf(e)]=l:rt.push(l)),l.patchFlag=-2,l}if(zf(e)&&(e=e.__vccOpts),t){t=yf(t);let{class:l,style:r}=t;l&&!Oe(l)&&(t.class=Re(l)),Se(r)&&(Zi(r)&&!q(r)&&(r=We({},r)),t.style=Is(r))}const a=Oe(e)?1:xl(e)?128:Ec(e)?64:Se(e)?4:te(e)?2:0;return p(e,t,s,n,i,a,o,!0)}function yf(e){return e?Zi(e)||bl(e)?We({},e):e:null}function As(e,t,s=!1,n=!1){const{props:i,ref:o,patchFlag:a,children:l,transition:r}=e,u=t?_f(i||{},t):i,f={__v_isVNode:!0,__v_skip:!0,type:e.type,props:u,key:u&&Dl(u),ref:t&&t.ref?s&&o?q(o)?o.concat(Nn(t)):[o,Nn(t)]:Nn(t):o,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Le?a===-1?16:a|16:a,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:r,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&As(e.ssContent),ssFallback:e.ssFallback&&As(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return r&&n&&no(f,r.clone(f)),f}function K(e=" ",t=0){return je(hn,null,e,t)}function In(e,t){const s=je(xn,null,e);return s.staticCount=t,s}function dt(e="",t=!1){return t?(le(),mf(ms,null,e)):je(ms,null,e)}function Pt(e){return e==null||typeof e=="boolean"?je(ms):q(e)?je(Le,null,e.slice()):typeof e=="object"?qt(e):je(hn,null,String(e))}function qt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:As(e)}function lo(e,t){let s=0;const{shapeFlag:n}=e;if(t==null)t=null;else if(q(t))s=16;else if(typeof t=="object")if(n&65){const i=t.default;i&&(i._c&&(i._d=!1),lo(e,i()),i._c&&(i._d=!0));return}else{s=32;const i=t._;!i&&!bl(t)?t._ctx=lt:i===3&&lt&&(lt.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else te(t)?(t={default:t,_ctx:lt},s=32):(t=String(t),n&64?(s=16,t=[K(t)]):s=8);e.children=t,e.shapeFlag|=s}function _f(...e){const t={};for(let s=0;s<e.length;s++){const n=e[s];for(const i in n)if(i==="class")t.class!==n.class&&(t.class=Re([t.class,n.class]));else if(i==="style")t.style=Is([t.style,n.style]);else if(jn(i)){const o=t[i],a=n[i];a&&o!==a&&!(q(o)&&o.includes(a))&&(t[i]=o?[].concat(o,a):a)}else i!==""&&(t[i]=n[i])}return t}function Et(e,t,s,n=null){xt(e,t,7,[s,n])}const vf=yl();let bf=0;function Cf(e,t,s){const n=e.type,i=(t?t.appContext:e.appContext)||vf,o={uid:bf++,vnode:e,type:n,parent:t,appContext:i,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Ha(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(i.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Sl(n,i),emitsOptions:Al(n,i),emit:null,emitted:null,propsDefaults:Ce,inheritAttrs:n.inheritAttrs,ctx:Ce,data:Ce,props:Ce,attrs:Ce,slots:Ce,refs:Ce,setupState:Ce,setupContext:null,suspense:s,suspenseId:s?s.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=t?t.root:o,o.emit=rf.bind(null,o),e.ce&&e.ce(o),o}let He=null;const an=()=>He||lt;let Vn,Li;{const e=Ua(),t=(s,n)=>{let i;return(i=e[s])||(i=e[s]=[]),i.push(n),o=>{i.length>1?i.forEach(a=>a(o)):i[0](o)}};Vn=t("__VUE_INSTANCE_SETTERS__",s=>He=s),Li=t("__VUE_SSR_SETTERS__",s=>Qn=s)}const yn=e=>{const t=He;return Vn(e),e.scope.on(),()=>{e.scope.off(),Vn(t)}},ko=()=>{He&&He.scope.off(),Vn(null)};function Ol(e){return e.vnode.shapeFlag&4}let Qn=!1;function Sf(e,t=!1,s=!1){t&&Li(t);const{props:n,children:i}=e.vnode,o=Ol(e);Gc(e,n,o,t),Qc(e,i,s);const a=o?Tf(e,t):void 0;return t&&Li(!1),a}function Tf(e,t){const s=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,kc);const{setup:n}=s;if(n){const i=e.setupContext=n.length>1?Ef(e):null,o=yn(e);ts();const a=dn(n,e,0,[e.props,i]);if(ss(),o(),Da(a)){if(Ks(e)||gl(e),a.then(ko,ko),t)return a.then(l=>{Uo(e,l,t)}).catch(l=>{Xn(l,e,0)});e.asyncDep=a}else Uo(e,a,t)}else Rl(e,t)}function Uo(e,t,s){te(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:Se(t)&&(e.setupState=al(t)),Rl(e,s)}let Wo;function Rl(e,t,s){const n=e.type;if(!e.render){if(!t&&Wo&&!n.render){const i=n.template||oo(e).template;if(i){const{isCustomElement:o,compilerOptions:a}=e.appContext.config,{delimiters:l,compilerOptions:r}=n,u=We(We({isCustomElement:o,delimiters:l},a),r);n.render=Wo(i,u)}}e.render=n.render||Mt}{const i=yn(e);ts();try{Uc(e)}finally{ss(),i()}}}const wf={get(e,t){return Ye(e,"get",""),e[t]}};function Ef(e){const t=s=>{e.exposed=s||{}};return{attrs:new Proxy(e.attrs,wf),slots:e.slots,emit:e.emit,expose:t}}function qn(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(al(pc(e.exposed)),{get(t,s){if(s in t)return t[s];if(s in Qs)return Qs[s](e)},has(t,s){return s in t||s in Qs}})):e.proxy}function zf(e){return te(e)&&"__vccOpts"in e}const Ts=(e,t)=>yc(e,t,Qn);function Fl(e,t,s){const n=arguments.length;return n===2?Se(t)&&!q(t)?Ii(t)?je(e,null,[t]):je(e,t):je(e,null,t):(n>3?s=Array.prototype.slice.call(arguments,2):n===3&&Ii(s)&&(s=[s]),je(e,t,s))}const If="3.5.8";/**
* @vue/runtime-dom v3.5.8
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Pi;const Vo=typeof window<"u"&&window.trustedTypes;if(Vo)try{Pi=Vo.createPolicy("vue",{createHTML:e=>e})}catch{}const kl=Pi?e=>Pi.createHTML(e):e=>e,Lf="http://www.w3.org/2000/svg",Pf="http://www.w3.org/1998/Math/MathML",Wt=typeof document<"u"?document:null,$o=Wt&&Wt.createElement("template"),Mf={insert:(e,t,s)=>{t.insertBefore(e,s||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,s,n)=>{const i=t==="svg"?Wt.createElementNS(Lf,e):t==="mathml"?Wt.createElementNS(Pf,e):s?Wt.createElement(e,{is:s}):Wt.createElement(e);return e==="select"&&n&&n.multiple!=null&&i.setAttribute("multiple",n.multiple),i},createText:e=>Wt.createTextNode(e),createComment:e=>Wt.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Wt.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,s,n,i,o){const a=s?s.previousSibling:t.lastChild;if(i&&(i===o||i.nextSibling))for(;t.insertBefore(i.cloneNode(!0),s),!(i===o||!(i=i.nextSibling)););else{$o.innerHTML=kl(n==="svg"?`<svg>${e}</svg>`:n==="mathml"?`<math>${e}</math>`:e);const l=$o.content;if(n==="svg"||n==="mathml"){const r=l.firstChild;for(;r.firstChild;)l.appendChild(r.firstChild);l.removeChild(r)}t.insertBefore(l,s)}return[a?a.nextSibling:t.firstChild,s?s.previousSibling:t.lastChild]}},Af=Symbol("_vtc");function xf(e,t,s){const n=e[Af];n&&(t=(t?[t,...n]:[...n]).join(" ")),t==null?e.removeAttribute("class"):s?e.setAttribute("class",t):e.className=t}const $n=Symbol("_vod"),Ul=Symbol("_vsh"),bt={beforeMount(e,{value:t},{transition:s}){e[$n]=e.style.display==="none"?"":e.style.display,s&&t?s.beforeEnter(e):js(e,t)},mounted(e,{value:t},{transition:s}){s&&t&&s.enter(e)},updated(e,{value:t,oldValue:s},{transition:n}){!t!=!s&&(n?t?(n.beforeEnter(e),js(e,!0),n.enter(e)):n.leave(e,()=>{js(e,!1)}):js(e,t))},beforeUnmount(e,{value:t}){js(e,t)}};function js(e,t){e.style.display=t?e[$n]:"none",e[Ul]=!t}const Nf=Symbol(""),Df=/(^|;)\s*display\s*:/;function Of(e,t,s){const n=e.style,i=Oe(s);let o=!1;if(s&&!i){if(t)if(Oe(t))for(const a of t.split(";")){const l=a.slice(0,a.indexOf(":")).trim();s[l]==null&&Dn(n,l,"")}else for(const a in t)s[a]==null&&Dn(n,a,"");for(const a in s)a==="display"&&(o=!0),Dn(n,a,s[a])}else if(i){if(t!==s){const a=n[Nf];a&&(s+=";"+a),n.cssText=s,o=Df.test(s)}}else t&&e.removeAttribute("style");$n in e&&(e[$n]=o?n.display:"",e[Ul]&&(n.display="none"))}const Ho=/\s*!important$/;function Dn(e,t,s){if(q(s))s.forEach(n=>Dn(e,t,n));else if(s==null&&(s=""),t.startsWith("--"))e.setProperty(t,s);else{const n=Rf(e,t);Ho.test(s)?e.setProperty(hs(n),s.replace(Ho,""),"important"):e[n]=s}}const jo=["Webkit","Moz","ms"],di={};function Rf(e,t){const s=di[t];if(s)return s;let n=gs(t);if(n!=="filter"&&n in e)return di[t]=n;n=Fa(n);for(let i=0;i<jo.length;i++){const o=jo[i]+n;if(o in e)return di[t]=o}return t}const Yo="http://www.w3.org/1999/xlink";function Bo(e,t,s,n,i,o=Wr(t)){n&&t.startsWith("xlink:")?s==null?e.removeAttributeNS(Yo,t.slice(6,t.length)):e.setAttributeNS(Yo,t,s):s==null||o&&!Wa(s)?e.removeAttribute(t):e.setAttribute(t,o?"":At(s)?String(s):s)}function Ff(e,t,s,n){if(t==="innerHTML"||t==="textContent"){s!=null&&(e[t]=t==="innerHTML"?kl(s):s);return}const i=e.tagName;if(t==="value"&&i!=="PROGRESS"&&!i.includes("-")){const a=i==="OPTION"?e.getAttribute("value")||"":e.value,l=s==null?e.type==="checkbox"?"on":"":String(s);(a!==l||!("_value"in e))&&(e.value=l),s==null&&e.removeAttribute(t),e._value=s;return}let o=!1;if(s===""||s==null){const a=typeof e[t];a==="boolean"?s=Wa(s):s==null&&a==="string"?(s="",o=!0):a==="number"&&(s=0,o=!0)}try{e[t]=s}catch{}o&&e.removeAttribute(t)}function Zt(e,t,s,n){e.addEventListener(t,s,n)}function kf(e,t,s,n){e.removeEventListener(t,s,n)}const Go=Symbol("_vei");function Uf(e,t,s,n,i=null){const o=e[Go]||(e[Go]={}),a=o[t];if(n&&a)a.value=n;else{const[l,r]=Wf(t);if(n){const u=o[t]=Hf(n,i);Zt(e,l,u,r)}else a&&(kf(e,l,a,r),o[t]=void 0)}}const Xo=/(?:Once|Passive|Capture)$/;function Wf(e){let t;if(Xo.test(e)){t={};let n;for(;n=e.match(Xo);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):hs(e.slice(2)),t]}let mi=0;const Vf=Promise.resolve(),$f=()=>mi||(Vf.then(()=>mi=0),mi=Date.now());function Hf(e,t){const s=n=>{if(!n._vts)n._vts=Date.now();else if(n._vts<=s.attached)return;xt(jf(n,s.value),t,5,[n])};return s.value=e,s.attached=$f(),s}function jf(e,t){if(q(t)){const s=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{s.call(e),e._stopped=!0},t.map(n=>i=>!i._stopped&&n&&n(i))}else return t}const Ko=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Yf=(e,t,s,n,i,o)=>{const a=i==="svg";t==="class"?xf(e,n,a):t==="style"?Of(e,s,n):jn(t)?Vi(t)||Uf(e,t,s,n,o):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):Bf(e,t,n,a))?(Ff(e,t,n),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&Bo(e,t,n,a,o,t!=="value")):(t==="true-value"?e._trueValue=n:t==="false-value"&&(e._falseValue=n),Bo(e,t,n,a))};function Bf(e,t,s,n){if(n)return!!(t==="innerHTML"||t==="textContent"||t in e&&Ko(t)&&te(s));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const i=e.tagName;if(i==="IMG"||i==="VIDEO"||i==="CANVAS"||i==="SOURCE")return!1}return Ko(t)&&Oe(s)?!1:!!(t in e||e._isVueCE&&(/[A-Z]/.test(t)||!Oe(s)))}const xs=e=>{const t=e.props["onUpdate:modelValue"]||!1;return q(t)?s=>An(t,s):t};function Gf(e){e.target.composing=!0}function Jo(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const Bt=Symbol("_assign"),Z={created(e,{modifiers:{lazy:t,trim:s,number:n}},i){e[Bt]=xs(i);const o=n||i.props&&i.props.type==="number";Zt(e,t?"change":"input",a=>{if(a.target.composing)return;let l=e.value;s&&(l=l.trim()),o&&(l=Rn(l)),e[Bt](l)}),s&&Zt(e,"change",()=>{e.value=e.value.trim()}),t||(Zt(e,"compositionstart",Gf),Zt(e,"compositionend",Jo),Zt(e,"change",Jo))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:s,modifiers:{lazy:n,trim:i,number:o}},a){if(e[Bt]=xs(a),e.composing)return;const l=(o||e.type==="number")&&!/^0\d/.test(e.value)?Rn(e.value):e.value,r=t??"";l!==r&&(document.activeElement===e&&e.type!=="range"&&(n&&t===s||i&&e.value.trim()===r)||(e.value=r))}},zt={deep:!0,created(e,t,s){e[Bt]=xs(s),Zt(e,"change",()=>{const n=e._modelValue,i=ln(e),o=e.checked,a=e[Bt];if(q(n)){const l=ji(n,i),r=l!==-1;if(o&&!r)a(n.concat(i));else if(!o&&r){const u=[...n];u.splice(l,1),a(u)}}else if(Rs(n)){const l=new Set(n);o?l.add(i):l.delete(i),a(l)}else a(Wl(e,o))})},mounted:Qo,beforeUpdate(e,t,s){e[Bt]=xs(s),Qo(e,t,s)}};function Qo(e,{value:t,oldValue:s},n){e._modelValue=t;let i;q(t)?i=ji(t,n.props.value)>-1:Rs(t)?i=t.has(n.props.value):i=gn(t,Wl(e,!0)),e.checked!==i&&(e.checked=i)}const cs={deep:!0,created(e,{value:t,modifiers:{number:s}},n){const i=Rs(t);Zt(e,"change",()=>{const o=Array.prototype.filter.call(e.options,a=>a.selected).map(a=>s?Rn(ln(a)):ln(a));e[Bt](e.multiple?i?new Set(o):o:o[0]),e._assigning=!0,rl(()=>{e._assigning=!1})}),e[Bt]=xs(n)},mounted(e,{value:t,modifiers:{number:s}}){qo(e,t)},beforeUpdate(e,t,s){e[Bt]=xs(s)},updated(e,{value:t,modifiers:{number:s}}){e._assigning||qo(e,t)}};function qo(e,t,s){const n=e.multiple,i=q(t);if(!(n&&!i&&!Rs(t))){for(let o=0,a=e.options.length;o<a;o++){const l=e.options[o],r=ln(l);if(n)if(i){const u=typeof r;u==="string"||u==="number"?l.selected=t.some(f=>String(f)===String(r)):l.selected=ji(t,r)>-1}else l.selected=t.has(r);else if(gn(ln(l),t)){e.selectedIndex!==o&&(e.selectedIndex=o);return}}!n&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function ln(e){return"_value"in e?e._value:e.value}function Wl(e,t){const s=t?"_trueValue":"_falseValue";return s in e?e[s]:t}const Xf=We({patchProp:Yf},Mf);let Zo;function Kf(){return Zo||(Zo=Zc(Xf))}const Jf=(...e)=>{const t=Kf().createApp(...e),{mount:s}=t;return t.mount=n=>{const i=qf(n);if(!i)return;const o=t._component;!te(o)&&!o.render&&!o.template&&(o.template=i.innerHTML),i.nodeType===1&&(i.textContent="");const a=s(i,!1,Qf(i));return i instanceof Element&&(i.removeAttribute("v-cloak"),i.setAttribute("data-v-app","")),a},t};function Qf(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function qf(e){return Oe(e)?document.querySelector(e):e}/*!
  * shared v11.0.0-rc.1
  * (c) 2024 kazuya kawaguchi
  * Released under the MIT License.
  */const Hn=typeof window<"u",ns=(e,t=!1)=>t?Symbol.for(e):Symbol(e),Zf=(e,t,s)=>eu({l:e,k:t,s}),eu=e=>JSON.stringify(e).replace(/\u2028/g,"\\u2028").replace(/\u2029/g,"\\u2029").replace(/\u0027/g,"\\u0027"),Ne=e=>typeof e=="number"&&isFinite(e),tu=e=>ro(e)==="[object Date]",Ns=e=>ro(e)==="[object RegExp]",Zn=e=>oe(e)&&Object.keys(e).length===0,Fe=Object.assign,su=Object.create,ye=(e=null)=>su(e);let ea;const us=()=>ea||(ea=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:ye());function ta(e){return e.replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}const nu=Object.prototype.hasOwnProperty;function Ct(e,t){return nu.call(e,t)}const De=Array.isArray,Te=e=>typeof e=="function",G=e=>typeof e=="string",re=e=>typeof e=="boolean",ue=e=>e!==null&&typeof e=="object",iu=e=>ue(e)&&Te(e.then)&&Te(e.catch),Vl=Object.prototype.toString,ro=e=>Vl.call(e),oe=e=>ro(e)==="[object Object]",ou=e=>e==null?"":De(e)||oe(e)&&e.toString===Vl?JSON.stringify(e,null,2):String(e);function co(e,t=""){return e.reduce((s,n,i)=>i===0?s+n:s+t+n,"")}function au(e,t){typeof console<"u"&&(console.warn("[intlify] "+e),t&&console.warn(t.stack))}const Ln=e=>!ue(e)||De(e);function On(e,t){if(Ln(e)||Ln(t))throw new Error("Invalid value");const s=[{src:e,des:t}];for(;s.length;){const{src:n,des:i}=s.pop();Object.keys(n).forEach(o=>{o!=="__proto__"&&(ue(n[o])&&!ue(i[o])&&(i[o]=Array.isArray(n[o])?[]:ye()),Ln(i[o])||Ln(n[o])?i[o]=n[o]:s.push({src:n[o],des:i[o]}))})}}/*!
  * message-compiler v11.0.0-rc.1
  * (c) 2024 kazuya kawaguchi
  * Released under the MIT License.
  */function lu(e,t,s){return{line:e,column:t,offset:s}}function Mi(e,t,s){return{start:e,end:t}}const me={EXPECTED_TOKEN:1,INVALID_TOKEN_IN_PLACEHOLDER:2,UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER:3,UNKNOWN_ESCAPE_SEQUENCE:4,INVALID_UNICODE_ESCAPE_SEQUENCE:5,UNBALANCED_CLOSING_BRACE:6,UNTERMINATED_CLOSING_BRACE:7,EMPTY_PLACEHOLDER:8,NOT_ALLOW_NEST_PLACEHOLDER:9,INVALID_LINKED_FORMAT:10,MUST_HAVE_MESSAGES_IN_PLURAL:11,UNEXPECTED_EMPTY_LINKED_MODIFIER:12,UNEXPECTED_EMPTY_LINKED_KEY:13,UNEXPECTED_LEXICAL_ANALYSIS:14,UNHANDLED_CODEGEN_NODE_TYPE:15,UNHANDLED_MINIFIER_NODE_TYPE:16},ru=17;function ei(e,t,s={}){const{domain:n,messages:i,args:o}=s,a=e,l=new SyntaxError(String(a));return l.code=e,t&&(l.location=t),l.domain=n,l}function cu(e){throw e}const kt=" ",fu="\r",Qe=`
`,uu="\u2028",pu="\u2029";function gu(e){const t=e;let s=0,n=1,i=1,o=0;const a=k=>t[k]===fu&&t[k+1]===Qe,l=k=>t[k]===Qe,r=k=>t[k]===pu,u=k=>t[k]===uu,f=k=>a(k)||l(k)||r(k)||u(k),d=()=>s,v=()=>n,E=()=>i,D=()=>o,O=k=>a(k)||r(k)||u(k)?Qe:t[k],W=()=>O(s),b=()=>O(s+o);function z(){return o=0,f(s)&&(n++,i=0),a(s)&&s++,s++,i++,t[s]}function P(){return a(s+o)&&o++,o++,t[s+o]}function T(){s=0,n=1,i=1,o=0}function F(k=0){o=k}function H(){const k=s+o;for(;k!==s;)z();o=0}return{index:d,line:v,column:E,peekOffset:D,charAt:O,currentChar:W,currentPeek:b,next:z,peek:P,reset:T,resetPeek:F,skipToPeek:H}}const Jt=void 0,du=".",sa="'",mu="tokenizer";function hu(e,t={}){const s=t.location!==!1,n=gu(e),i=()=>n.index(),o=()=>lu(n.line(),n.column(),n.index()),a=o(),l=i(),r={currentType:13,offset:l,startLoc:a,endLoc:a,lastType:13,lastOffset:l,lastStartLoc:a,lastEndLoc:a,braceNest:0,inLinked:!1,text:""},u=()=>r,{onError:f}=t;function d(c,g,S,...I){const A=u();if(g.column+=S,g.offset+=S,f){const V=s?Mi(A.startLoc,g):null,y=ei(c,V,{domain:mu,args:I});f(y)}}function v(c,g,S){c.endLoc=o(),c.currentType=g;const I={type:g};return s&&(I.loc=Mi(c.startLoc,c.endLoc)),S!=null&&(I.value=S),I}const E=c=>v(c,13);function D(c,g){return c.currentChar()===g?(c.next(),g):(d(me.EXPECTED_TOKEN,o(),0,g),"")}function O(c){let g="";for(;c.currentPeek()===kt||c.currentPeek()===Qe;)g+=c.currentPeek(),c.peek();return g}function W(c){const g=O(c);return c.skipToPeek(),g}function b(c){if(c===Jt)return!1;const g=c.charCodeAt(0);return g>=97&&g<=122||g>=65&&g<=90||g===95}function z(c){if(c===Jt)return!1;const g=c.charCodeAt(0);return g>=48&&g<=57}function P(c,g){const{currentType:S}=g;if(S!==2)return!1;O(c);const I=b(c.currentPeek());return c.resetPeek(),I}function T(c,g){const{currentType:S}=g;if(S!==2)return!1;O(c);const I=c.currentPeek()==="-"?c.peek():c.currentPeek(),A=z(I);return c.resetPeek(),A}function F(c,g){const{currentType:S}=g;if(S!==2)return!1;O(c);const I=c.currentPeek()===sa;return c.resetPeek(),I}function H(c,g){const{currentType:S}=g;if(S!==7)return!1;O(c);const I=c.currentPeek()===".";return c.resetPeek(),I}function k(c,g){const{currentType:S}=g;if(S!==8)return!1;O(c);const I=b(c.currentPeek());return c.resetPeek(),I}function ee(c,g){const{currentType:S}=g;if(!(S===7||S===11))return!1;O(c);const I=c.currentPeek()===":";return c.resetPeek(),I}function ne(c,g){const{currentType:S}=g;if(S!==9)return!1;const I=()=>{const V=c.currentPeek();return V==="{"?b(c.peek()):V==="@"||V==="|"||V===":"||V==="."||V===kt||!V?!1:V===Qe?(c.peek(),I()):Ee(c,!1)},A=I();return c.resetPeek(),A}function _e(c){O(c);const g=c.currentPeek()==="|";return c.resetPeek(),g}function Ee(c,g=!0){const S=(A=!1,V="")=>{const y=c.currentPeek();return y==="{"||y==="@"||!y?A:y==="|"?!(V===kt||V===Qe):y===kt?(c.peek(),S(!0,kt)):y===Qe?(c.peek(),S(!0,Qe)):!0},I=S();return g&&c.resetPeek(),I}function ve(c,g){const S=c.currentChar();return S===Jt?Jt:g(S)?(c.next(),S):null}function Ze(c){const g=c.charCodeAt(0);return g>=97&&g<=122||g>=65&&g<=90||g>=48&&g<=57||g===95||g===36}function ft(c){return ve(c,Ze)}function ut(c){const g=c.charCodeAt(0);return g>=97&&g<=122||g>=65&&g<=90||g>=48&&g<=57||g===95||g===36||g===45}function ze(c){return ve(c,ut)}function ce(c){const g=c.charCodeAt(0);return g>=48&&g<=57}function ae(c){return ve(c,ce)}function Pe(c){const g=c.charCodeAt(0);return g>=48&&g<=57||g>=65&&g<=70||g>=97&&g<=102}function Be(c){return ve(c,Pe)}function Me(c){let g="",S="";for(;g=ae(c);)S+=g;return S}function Ae(c){let g="";for(;;){const S=c.currentChar();if(S==="{"||S==="}"||S==="@"||S==="|"||!S)break;if(S===kt||S===Qe)if(Ee(c))g+=S,c.next();else{if(_e(c))break;g+=S,c.next()}else g+=S,c.next()}return g}function Tt(c){W(c);let g="",S="";for(;g=ze(c);)S+=g;return c.currentChar()===Jt&&d(me.UNTERMINATED_CLOSING_BRACE,o(),0),S}function pt(c){W(c);let g="";return c.currentChar()==="-"?(c.next(),g+=`-${Me(c)}`):g+=Me(c),c.currentChar()===Jt&&d(me.UNTERMINATED_CLOSING_BRACE,o(),0),g}function wt(c){return c!==sa&&c!==Qe}function yt(c){W(c),D(c,"'");let g="",S="";for(;g=ve(c,wt);)g==="\\"?S+=et(c):S+=g;const I=c.currentChar();return I===Qe||I===Jt?(d(me.UNTERMINATED_SINGLE_QUOTE_IN_PLACEHOLDER,o(),0),I===Qe&&(c.next(),D(c,"'")),S):(D(c,"'"),S)}function et(c){const g=c.currentChar();switch(g){case"\\":case"'":return c.next(),`\\${g}`;case"u":return _t(c,g,4);case"U":return _t(c,g,6);default:return d(me.UNKNOWN_ESCAPE_SEQUENCE,o(),0,g),""}}function _t(c,g,S){D(c,g);let I="";for(let A=0;A<S;A++){const V=Be(c);if(!V){d(me.INVALID_UNICODE_ESCAPE_SEQUENCE,o(),0,`\\${g}${I}${c.currentChar()}`);break}I+=V}return`\\${g}${I}`}function gt(c){return c!=="{"&&c!=="}"&&c!==kt&&c!==Qe}function Ge(c){W(c);let g="",S="";for(;g=ve(c,gt);)S+=g;return S}function st(c){let g="",S="";for(;g=ft(c);)S+=g;return S}function nt(c){const g=S=>{const I=c.currentChar();return I==="{"||I==="@"||I==="|"||I==="("||I===")"||!I||I===kt?S:(S+=I,c.next(),g(S))};return g("")}function m(c){W(c);const g=D(c,"|");return W(c),g}function h(c,g){let S=null;switch(c.currentChar()){case"{":return g.braceNest>=1&&d(me.NOT_ALLOW_NEST_PLACEHOLDER,o(),0),c.next(),S=v(g,2,"{"),W(c),g.braceNest++,S;case"}":return g.braceNest>0&&g.currentType===2&&d(me.EMPTY_PLACEHOLDER,o(),0),c.next(),S=v(g,3,"}"),g.braceNest--,g.braceNest>0&&W(c),g.inLinked&&g.braceNest===0&&(g.inLinked=!1),S;case"@":return g.braceNest>0&&d(me.UNTERMINATED_CLOSING_BRACE,o(),0),S=L(c,g)||E(g),g.braceNest=0,S;default:{let A=!0,V=!0,y=!0;if(_e(c))return g.braceNest>0&&d(me.UNTERMINATED_CLOSING_BRACE,o(),0),S=v(g,1,m(c)),g.braceNest=0,g.inLinked=!1,S;if(g.braceNest>0&&(g.currentType===4||g.currentType===5||g.currentType===6))return d(me.UNTERMINATED_CLOSING_BRACE,o(),0),g.braceNest=0,U(c,g);if(A=P(c,g))return S=v(g,4,Tt(c)),W(c),S;if(V=T(c,g))return S=v(g,5,pt(c)),W(c),S;if(y=F(c,g))return S=v(g,6,yt(c)),W(c),S;if(!A&&!V&&!y)return S=v(g,12,Ge(c)),d(me.INVALID_TOKEN_IN_PLACEHOLDER,o(),0,S.value),W(c),S;break}}return S}function L(c,g){const{currentType:S}=g;let I=null;const A=c.currentChar();switch((S===7||S===8||S===11||S===9)&&(A===Qe||A===kt)&&d(me.INVALID_LINKED_FORMAT,o(),0),A){case"@":return c.next(),I=v(g,7,"@"),g.inLinked=!0,I;case".":return W(c),c.next(),v(g,8,".");case":":return W(c),c.next(),v(g,9,":");default:return _e(c)?(I=v(g,1,m(c)),g.braceNest=0,g.inLinked=!1,I):H(c,g)||ee(c,g)?(W(c),L(c,g)):k(c,g)?(W(c),v(g,11,st(c))):ne(c,g)?(W(c),A==="{"?h(c,g)||I:v(g,10,nt(c))):(S===7&&d(me.INVALID_LINKED_FORMAT,o(),0),g.braceNest=0,g.inLinked=!1,U(c,g))}}function U(c,g){let S={type:13};if(g.braceNest>0)return h(c,g)||E(g);if(g.inLinked)return L(c,g)||E(g);switch(c.currentChar()){case"{":return h(c,g)||E(g);case"}":return d(me.UNBALANCED_CLOSING_BRACE,o(),0),c.next(),v(g,3,"}");case"@":return L(c,g)||E(g);default:{if(_e(c))return S=v(g,1,m(c)),g.braceNest=0,g.inLinked=!1,S;if(Ee(c))return v(g,0,Ae(c));break}}return S}function R(){const{currentType:c,offset:g,startLoc:S,endLoc:I}=r;return r.lastType=c,r.lastOffset=g,r.lastStartLoc=S,r.lastEndLoc=I,r.offset=i(),r.startLoc=o(),n.currentChar()===Jt?v(r,13):U(n,r)}return{nextToken:R,currentOffset:i,currentPosition:o,context:u}}const yu="parser",_u=/(?:\\\\|\\'|\\u([0-9a-fA-F]{4})|\\U([0-9a-fA-F]{6}))/g;function vu(e,t,s){switch(e){case"\\\\":return"\\";case"\\'":return"'";default:{const n=parseInt(t||s,16);return n<=55295||n>=57344?String.fromCodePoint(n):"�"}}}function bu(e={}){const t=e.location!==!1,{onError:s}=e;function n(b,z,P,T,...F){const H=b.currentPosition();if(H.offset+=T,H.column+=T,s){const k=t?Mi(P,H):null,ee=ei(z,k,{domain:yu,args:F});s(ee)}}function i(b,z,P){const T={type:b};return t&&(T.start=z,T.end=z,T.loc={start:P,end:P}),T}function o(b,z,P,T){t&&(b.end=z,b.loc&&(b.loc.end=P))}function a(b,z){const P=b.context(),T=i(3,P.offset,P.startLoc);return T.value=z,o(T,b.currentOffset(),b.currentPosition()),T}function l(b,z){const P=b.context(),{lastOffset:T,lastStartLoc:F}=P,H=i(5,T,F);return H.index=parseInt(z,10),b.nextToken(),o(H,b.currentOffset(),b.currentPosition()),H}function r(b,z){const P=b.context(),{lastOffset:T,lastStartLoc:F}=P,H=i(4,T,F);return H.key=z,b.nextToken(),o(H,b.currentOffset(),b.currentPosition()),H}function u(b,z){const P=b.context(),{lastOffset:T,lastStartLoc:F}=P,H=i(9,T,F);return H.value=z.replace(_u,vu),b.nextToken(),o(H,b.currentOffset(),b.currentPosition()),H}function f(b){const z=b.nextToken(),P=b.context(),{lastOffset:T,lastStartLoc:F}=P,H=i(8,T,F);return z.type!==11?(n(b,me.UNEXPECTED_EMPTY_LINKED_MODIFIER,P.lastStartLoc,0),H.value="",o(H,T,F),{nextConsumeToken:z,node:H}):(z.value==null&&n(b,me.UNEXPECTED_LEXICAL_ANALYSIS,P.lastStartLoc,0,It(z)),H.value=z.value||"",o(H,b.currentOffset(),b.currentPosition()),{node:H})}function d(b,z){const P=b.context(),T=i(7,P.offset,P.startLoc);return T.value=z,o(T,b.currentOffset(),b.currentPosition()),T}function v(b){const z=b.context(),P=i(6,z.offset,z.startLoc);let T=b.nextToken();if(T.type===8){const F=f(b);P.modifier=F.node,T=F.nextConsumeToken||b.nextToken()}switch(T.type!==9&&n(b,me.UNEXPECTED_LEXICAL_ANALYSIS,z.lastStartLoc,0,It(T)),T=b.nextToken(),T.type===2&&(T=b.nextToken()),T.type){case 10:T.value==null&&n(b,me.UNEXPECTED_LEXICAL_ANALYSIS,z.lastStartLoc,0,It(T)),P.key=d(b,T.value||"");break;case 4:T.value==null&&n(b,me.UNEXPECTED_LEXICAL_ANALYSIS,z.lastStartLoc,0,It(T)),P.key=r(b,T.value||"");break;case 5:T.value==null&&n(b,me.UNEXPECTED_LEXICAL_ANALYSIS,z.lastStartLoc,0,It(T)),P.key=l(b,T.value||"");break;case 6:T.value==null&&n(b,me.UNEXPECTED_LEXICAL_ANALYSIS,z.lastStartLoc,0,It(T)),P.key=u(b,T.value||"");break;default:{n(b,me.UNEXPECTED_EMPTY_LINKED_KEY,z.lastStartLoc,0);const F=b.context(),H=i(7,F.offset,F.startLoc);return H.value="",o(H,F.offset,F.startLoc),P.key=H,o(P,F.offset,F.startLoc),{nextConsumeToken:T,node:P}}}return o(P,b.currentOffset(),b.currentPosition()),{node:P}}function E(b){const z=b.context(),P=z.currentType===1?b.currentOffset():z.offset,T=z.currentType===1?z.endLoc:z.startLoc,F=i(2,P,T);F.items=[];let H=null;do{const ne=H||b.nextToken();switch(H=null,ne.type){case 0:ne.value==null&&n(b,me.UNEXPECTED_LEXICAL_ANALYSIS,z.lastStartLoc,0,It(ne)),F.items.push(a(b,ne.value||""));break;case 5:ne.value==null&&n(b,me.UNEXPECTED_LEXICAL_ANALYSIS,z.lastStartLoc,0,It(ne)),F.items.push(l(b,ne.value||""));break;case 4:ne.value==null&&n(b,me.UNEXPECTED_LEXICAL_ANALYSIS,z.lastStartLoc,0,It(ne)),F.items.push(r(b,ne.value||""));break;case 6:ne.value==null&&n(b,me.UNEXPECTED_LEXICAL_ANALYSIS,z.lastStartLoc,0,It(ne)),F.items.push(u(b,ne.value||""));break;case 7:{const _e=v(b);F.items.push(_e.node),H=_e.nextConsumeToken||null;break}}}while(z.currentType!==13&&z.currentType!==1);const k=z.currentType===1?z.lastOffset:b.currentOffset(),ee=z.currentType===1?z.lastEndLoc:b.currentPosition();return o(F,k,ee),F}function D(b,z,P,T){const F=b.context();let H=T.items.length===0;const k=i(1,z,P);k.cases=[],k.cases.push(T);do{const ee=E(b);H||(H=ee.items.length===0),k.cases.push(ee)}while(F.currentType!==13);return H&&n(b,me.MUST_HAVE_MESSAGES_IN_PLURAL,P,0),o(k,b.currentOffset(),b.currentPosition()),k}function O(b){const z=b.context(),{offset:P,startLoc:T}=z,F=E(b);return z.currentType===13?F:D(b,P,T,F)}function W(b){const z=hu(b,Fe({},e)),P=z.context(),T=i(0,P.offset,P.startLoc);return t&&T.loc&&(T.loc.source=b),T.body=O(z),e.onCacheKey&&(T.cacheKey=e.onCacheKey(b)),P.currentType!==13&&n(z,me.UNEXPECTED_LEXICAL_ANALYSIS,P.lastStartLoc,0,b[P.offset]||""),o(T,z.currentOffset(),z.currentPosition()),T}return{parse:W}}function It(e){if(e.type===13)return"EOF";const t=(e.value||"").replace(/\r?\n/gu,"\\n");return t.length>10?t.slice(0,9)+"…":t}function Cu(e,t={}){const s={ast:e,helpers:new Set};return{context:()=>s,helper:o=>(s.helpers.add(o),o)}}function na(e,t){for(let s=0;s<e.length;s++)fo(e[s],t)}function fo(e,t){switch(e.type){case 1:na(e.cases,t),t.helper("plural");break;case 2:na(e.items,t);break;case 6:{fo(e.key,t),t.helper("linked"),t.helper("type");break}case 5:t.helper("interpolate"),t.helper("list");break;case 4:t.helper("interpolate"),t.helper("named");break}}function Su(e,t={}){const s=Cu(e);s.helper("normalize"),e.body&&fo(e.body,s);const n=s.context();e.helpers=Array.from(n.helpers)}function Tu(e){const t=e.body;return t.type===2?ia(t):t.cases.forEach(s=>ia(s)),e}function ia(e){if(e.items.length===1){const t=e.items[0];(t.type===3||t.type===9)&&(e.static=t.value,delete t.value)}else{const t=[];for(let s=0;s<e.items.length;s++){const n=e.items[s];if(!(n.type===3||n.type===9)||n.value==null)break;t.push(n.value)}if(t.length===e.items.length){e.static=co(t);for(let s=0;s<e.items.length;s++){const n=e.items[s];(n.type===3||n.type===9)&&delete n.value}}}}function ws(e){switch(e.t=e.type,e.type){case 0:{const t=e;ws(t.body),t.b=t.body,delete t.body;break}case 1:{const t=e,s=t.cases;for(let n=0;n<s.length;n++)ws(s[n]);t.c=s,delete t.cases;break}case 2:{const t=e,s=t.items;for(let n=0;n<s.length;n++)ws(s[n]);t.i=s,delete t.items,t.static&&(t.s=t.static,delete t.static);break}case 3:case 9:case 8:case 7:{const t=e;t.value&&(t.v=t.value,delete t.value);break}case 6:{const t=e;ws(t.key),t.k=t.key,delete t.key,t.modifier&&(ws(t.modifier),t.m=t.modifier,delete t.modifier);break}case 5:{const t=e;t.i=t.index,delete t.index;break}case 4:{const t=e;t.k=t.key,delete t.key;break}}delete e.type}function wu(e,t){const{sourceMap:s,filename:n,breakLineCode:i,needIndent:o}=t,a=t.location!==!1,l={filename:n,code:"",column:1,line:1,offset:0,map:void 0,breakLineCode:i,needIndent:o,indentLevel:0};a&&e.loc&&(l.source=e.loc.source);const r=()=>l;function u(W,b){l.code+=W}function f(W,b=!0){const z=b?i:"";u(o?z+"  ".repeat(W):z)}function d(W=!0){const b=++l.indentLevel;W&&f(b)}function v(W=!0){const b=--l.indentLevel;W&&f(b)}function E(){f(l.indentLevel)}return{context:r,push:u,indent:d,deindent:v,newline:E,helper:W=>`_${W}`,needIndent:()=>l.needIndent}}function Eu(e,t){const{helper:s}=e;e.push(`${s("linked")}(`),Ds(e,t.key),t.modifier?(e.push(", "),Ds(e,t.modifier),e.push(", _type")):e.push(", undefined, _type"),e.push(")")}function zu(e,t){const{helper:s,needIndent:n}=e;e.push(`${s("normalize")}([`),e.indent(n());const i=t.items.length;for(let o=0;o<i&&(Ds(e,t.items[o]),o!==i-1);o++)e.push(", ");e.deindent(n()),e.push("])")}function Iu(e,t){const{helper:s,needIndent:n}=e;if(t.cases.length>1){e.push(`${s("plural")}([`),e.indent(n());const i=t.cases.length;for(let o=0;o<i&&(Ds(e,t.cases[o]),o!==i-1);o++)e.push(", ");e.deindent(n()),e.push("])")}}function Lu(e,t){t.body?Ds(e,t.body):e.push("null")}function Ds(e,t){const{helper:s}=e;switch(t.type){case 0:Lu(e,t);break;case 1:Iu(e,t);break;case 2:zu(e,t);break;case 6:Eu(e,t);break;case 8:e.push(JSON.stringify(t.value),t);break;case 7:e.push(JSON.stringify(t.value),t);break;case 5:e.push(`${s("interpolate")}(${s("list")}(${t.index}))`,t);break;case 4:e.push(`${s("interpolate")}(${s("named")}(${JSON.stringify(t.key)}))`,t);break;case 9:e.push(JSON.stringify(t.value),t);break;case 3:e.push(JSON.stringify(t.value),t);break}}const Pu=(e,t={})=>{const s=G(t.mode)?t.mode:"normal",n=G(t.filename)?t.filename:"message.intl",i=!!t.sourceMap,o=t.breakLineCode!=null?t.breakLineCode:s==="arrow"?";":`
`,a=t.needIndent?t.needIndent:s!=="arrow",l=e.helpers||[],r=wu(e,{mode:s,filename:n,sourceMap:i,breakLineCode:o,needIndent:a});r.push(s==="normal"?"function __msg__ (ctx) {":"(ctx) => {"),r.indent(a),l.length>0&&(r.push(`const { ${co(l.map(d=>`${d}: _${d}`),", ")} } = ctx`),r.newline()),r.push("return "),Ds(r,e),r.deindent(a),r.push("}"),delete e.helpers;const{code:u,map:f}=r.context();return{ast:e,code:u,map:f?f.toJSON():void 0}};function Mu(e,t={}){const s=Fe({},t),n=!!s.jit,i=!!s.minify,o=s.optimize==null?!0:s.optimize,l=bu(s).parse(e);return n?(o&&Tu(l),i&&ws(l),{ast:l,code:""}):(Su(l,s),Pu(l,s))}/*!
  * core-base v11.0.0-rc.1
  * (c) 2024 kazuya kawaguchi
  * Released under the MIT License.
  */function Au(){typeof __INTLIFY_PROD_DEVTOOLS__!="boolean"&&(us().__INTLIFY_PROD_DEVTOOLS__=!1),typeof __INTLIFY_DROP_MESSAGE_COMPILER__!="boolean"&&(us().__INTLIFY_DROP_MESSAGE_COMPILER__=!1)}function hi(e){return s=>xu(s,e)}function xu(e,t){const s=Du(t);if(s==null)throw rn(0);if(uo(s)===1){const o=Ru(s);return e.plural(o.reduce((a,l)=>[...a,oa(e,l)],[]))}else return oa(e,s)}const Nu=["b","body"];function Du(e){return is(e,Nu)}const Ou=["c","cases"];function Ru(e){return is(e,Ou,[])}function oa(e,t){const s=ku(t);if(s!=null)return e.type==="text"?s:e.normalize([s]);{const n=Wu(t).reduce((i,o)=>[...i,Ai(e,o)],[]);return e.normalize(n)}}const Fu=["s","static"];function ku(e){return is(e,Fu)}const Uu=["i","items"];function Wu(e){return is(e,Uu,[])}function Ai(e,t){const s=uo(t);switch(s){case 3:return Pn(t,s);case 9:return Pn(t,s);case 4:{const n=t;if(Ct(n,"k")&&n.k)return e.interpolate(e.named(n.k));if(Ct(n,"key")&&n.key)return e.interpolate(e.named(n.key));throw rn(s)}case 5:{const n=t;if(Ct(n,"i")&&Ne(n.i))return e.interpolate(e.list(n.i));if(Ct(n,"index")&&Ne(n.index))return e.interpolate(e.list(n.index));throw rn(s)}case 6:{const n=t,i=ju(n),o=Bu(n);return e.linked(Ai(e,o),i?Ai(e,i):void 0,e.type)}case 7:return Pn(t,s);case 8:return Pn(t,s);default:throw new Error(`unhandled node on format message part: ${s}`)}}const Vu=["t","type"];function uo(e){return is(e,Vu)}const $u=["v","value"];function Pn(e,t){const s=is(e,$u);if(s)return s;throw rn(t)}const Hu=["m","modifier"];function ju(e){return is(e,Hu)}const Yu=["k","key"];function Bu(e){const t=is(e,Yu);if(t)return t;throw rn(6)}function is(e,t,s){for(let n=0;n<t.length;n++){const i=t[n];if(Ct(e,i)&&e[i]!=null)return e[i]}return s}function rn(e){return new Error(`unhandled node type: ${e}`)}const Gu=e=>e;let Mn=ye();function Os(e){return ue(e)&&uo(e)===0&&(Ct(e,"b")||Ct(e,"body"))}function Xu(e,t={}){let s=!1;const n=t.onError||cu;return t.onError=i=>{s=!0,n(i)},{...Mu(e,t),detectError:s}}function Ku(e,t){if(!__INTLIFY_DROP_MESSAGE_COMPILER__&&G(e)){re(t.warnHtmlMessage)&&t.warnHtmlMessage;const n=(t.onCacheKey||Gu)(e),i=Mn[n];if(i)return i;const{ast:o,detectError:a}=Xu(e,{...t,location:!1,jit:!0}),l=hi(o);return a?l:Mn[n]=l}else{const s=e.cacheKey;if(s){const n=Mn[s];return n||(Mn[s]=hi(e))}else return hi(e)}}let cn=null;function Ju(e){cn=e}function Qu(e,t,s){cn&&cn.emit("i18n:init",{timestamp:Date.now(),i18n:e,version:t,meta:s})}const qu=Zu("function:translate");function Zu(e){return t=>cn&&cn.emit(e,t)}const $t={INVALID_ARGUMENT:ru,INVALID_DATE_ARGUMENT:18,INVALID_ISO_DATE_ARGUMENT:19,NOT_SUPPORT_NON_STRING_MESSAGE:20,NOT_SUPPORT_LOCALE_PROMISE_VALUE:21,NOT_SUPPORT_LOCALE_ASYNC_FUNCTION:22,NOT_SUPPORT_LOCALE_TYPE:23},e0=24;function Ht(e){return ei(e,null,void 0)}function po(e,t){return t.locale!=null?aa(t.locale):aa(e.locale)}let yi;function aa(e){if(G(e))return e;if(Te(e)){if(e.resolvedOnce&&yi!=null)return yi;if(e.constructor.name==="Function"){const t=e();if(iu(t))throw Ht($t.NOT_SUPPORT_LOCALE_PROMISE_VALUE);return yi=t}else throw Ht($t.NOT_SUPPORT_LOCALE_ASYNC_FUNCTION)}else throw Ht($t.NOT_SUPPORT_LOCALE_TYPE)}function t0(e,t,s){return[...new Set([s,...De(t)?t:ue(t)?Object.keys(t):G(t)?[t]:[s]])]}function $l(e,t,s){const n=G(s)?s:fn,i=e;i.__localeChainCache||(i.__localeChainCache=new Map);let o=i.__localeChainCache.get(n);if(!o){o=[];let a=[s];for(;De(a);)a=la(o,a,t);const l=De(t)||!oe(t)?t:t.default?t.default:null;a=G(l)?[l]:l,De(a)&&la(o,a,!1),i.__localeChainCache.set(n,o)}return o}function la(e,t,s){let n=!0;for(let i=0;i<t.length&&re(n);i++){const o=t[i];G(o)&&(n=s0(e,t[i],s))}return n}function s0(e,t,s){let n;const i=t.split("-");do{const o=i.join("-");n=n0(e,o,s),i.splice(-1,1)}while(i.length&&n===!0);return n}function n0(e,t,s){let n=!1;if(!e.includes(t)&&(n=!0,t)){n=t[t.length-1]!=="!";const i=t.replace(/!/g,"");e.push(i),(De(s)||oe(s))&&s[i]&&(n=s[i])}return n}const os=[];os[0]={w:[0],i:[3,0],"[":[4],o:[7]};os[1]={w:[1],".":[2],"[":[4],o:[7]};os[2]={w:[2],i:[3,0],0:[3,0]};os[3]={i:[3,0],0:[3,0],w:[1,1],".":[2,1],"[":[4,1],o:[7,1]};os[4]={"'":[5,0],'"':[6,0],"[":[4,2],"]":[1,3],o:8,l:[4,0]};os[5]={"'":[4,0],o:8,l:[5,0]};os[6]={'"':[4,0],o:8,l:[6,0]};const i0=/^\s?(?:true|false|-?[\d.]+|'[^']*'|"[^"]*")\s?$/;function o0(e){return i0.test(e)}function a0(e){const t=e.charCodeAt(0),s=e.charCodeAt(e.length-1);return t===s&&(t===34||t===39)?e.slice(1,-1):e}function l0(e){if(e==null)return"o";switch(e.charCodeAt(0)){case 91:case 93:case 46:case 34:case 39:return e;case 95:case 36:case 45:return"i";case 9:case 10:case 13:case 160:case 65279:case 8232:case 8233:return"w"}return"i"}function r0(e){const t=e.trim();return e.charAt(0)==="0"&&isNaN(parseInt(e))?!1:o0(t)?a0(t):"*"+t}function c0(e){const t=[];let s=-1,n=0,i=0,o,a,l,r,u,f,d;const v=[];v[0]=()=>{a===void 0?a=l:a+=l},v[1]=()=>{a!==void 0&&(t.push(a),a=void 0)},v[2]=()=>{v[0](),i++},v[3]=()=>{if(i>0)i--,n=4,v[0]();else{if(i=0,a===void 0||(a=r0(a),a===!1))return!1;v[1]()}};function E(){const D=e[s+1];if(n===5&&D==="'"||n===6&&D==='"')return s++,l="\\"+D,v[0](),!0}for(;n!==null;)if(s++,o=e[s],!(o==="\\"&&E())){if(r=l0(o),d=os[n],u=d[r]||d.l||8,u===8||(n=u[0],u[1]!==void 0&&(f=v[u[1]],f&&(l=o,f()===!1))))return;if(n===7)return t}}const ra=new Map;function f0(e,t){return ue(e)?e[t]:null}function u0(e,t){if(!ue(e))return null;let s=ra.get(t);if(s||(s=c0(t),s&&ra.set(t,s)),!s)return null;const n=s.length;let i=e,o=0;for(;o<n;){const a=i[s[o]];if(a===void 0||Te(i))return null;i=a,o++}return i}const p0="11.0.0-rc.1",ti=-1,fn="en-US",ca="",fa=e=>`${e.charAt(0).toLocaleUpperCase()}${e.substr(1)}`;function g0(){return{upper:(e,t)=>t==="text"&&G(e)?e.toUpperCase():t==="vnode"&&ue(e)&&"__v_isVNode"in e?e.children.toUpperCase():e,lower:(e,t)=>t==="text"&&G(e)?e.toLowerCase():t==="vnode"&&ue(e)&&"__v_isVNode"in e?e.children.toLowerCase():e,capitalize:(e,t)=>t==="text"&&G(e)?fa(e):t==="vnode"&&ue(e)&&"__v_isVNode"in e?fa(e.children):e}}let Hl;function d0(e){Hl=e}let jl;function m0(e){jl=e}let Yl;function h0(e){Yl=e}let Bl=null;const y0=e=>{Bl=e},_0=()=>Bl;let Gl=null;const ua=e=>{Gl=e},v0=()=>Gl;let pa=0;function b0(e={}){const t=Te(e.onWarn)?e.onWarn:au,s=G(e.version)?e.version:p0,n=G(e.locale)||Te(e.locale)?e.locale:fn,i=Te(n)?fn:n,o=De(e.fallbackLocale)||oe(e.fallbackLocale)||G(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:i,a=oe(e.messages)?e.messages:_i(i),l=oe(e.datetimeFormats)?e.datetimeFormats:_i(i),r=oe(e.numberFormats)?e.numberFormats:_i(i),u=Fe(ye(),e.modifiers,g0()),f=e.pluralRules||ye(),d=Te(e.missing)?e.missing:null,v=re(e.missingWarn)||Ns(e.missingWarn)?e.missingWarn:!0,E=re(e.fallbackWarn)||Ns(e.fallbackWarn)?e.fallbackWarn:!0,D=!!e.fallbackFormat,O=!!e.unresolving,W=Te(e.postTranslation)?e.postTranslation:null,b=oe(e.processor)?e.processor:null,z=re(e.warnHtmlMessage)?e.warnHtmlMessage:!0,P=!!e.escapeParameter,T=Te(e.messageCompiler)?e.messageCompiler:Hl,F=Te(e.messageResolver)?e.messageResolver:jl||f0,H=Te(e.localeFallbacker)?e.localeFallbacker:Yl||t0,k=ue(e.fallbackContext)?e.fallbackContext:void 0,ee=e,ne=ue(ee.__datetimeFormatters)?ee.__datetimeFormatters:new Map,_e=ue(ee.__numberFormatters)?ee.__numberFormatters:new Map,Ee=ue(ee.__meta)?ee.__meta:{};pa++;const ve={version:s,cid:pa,locale:n,fallbackLocale:o,messages:a,modifiers:u,pluralRules:f,missing:d,missingWarn:v,fallbackWarn:E,fallbackFormat:D,unresolving:O,postTranslation:W,processor:b,warnHtmlMessage:z,escapeParameter:P,messageCompiler:T,messageResolver:F,localeFallbacker:H,fallbackContext:k,onWarn:t,__meta:Ee};return ve.datetimeFormats=l,ve.numberFormats=r,ve.__datetimeFormatters=ne,ve.__numberFormatters=_e,__INTLIFY_PROD_DEVTOOLS__&&Qu(ve,s,Ee),ve}const _i=e=>({[e]:ye()});function go(e,t,s,n,i){const{missing:o,onWarn:a}=e;if(o!==null){const l=o(e,s,t,i);return G(l)?l:t}else return t}function Ys(e,t,s){const n=e;n.__localeChainCache=new Map,e.localeFallbacker(e,s,t)}function C0(e,t){return e===t?!1:e.split("-")[0]===t.split("-")[0]}function S0(e,t){const s=t.indexOf(e);if(s===-1)return!1;for(let n=s+1;n<t.length;n++)if(C0(e,t[n]))return!0;return!1}function ga(e,...t){const{datetimeFormats:s,unresolving:n,fallbackLocale:i,onWarn:o,localeFallbacker:a}=e,{__datetimeFormatters:l}=e,[r,u,f,d]=xi(...t),v=re(f.missingWarn)?f.missingWarn:e.missingWarn;re(f.fallbackWarn)?f.fallbackWarn:e.fallbackWarn;const E=!!f.part,D=po(e,f),O=a(e,i,D);if(!G(r)||r==="")return new Intl.DateTimeFormat(D,d).format(u);let W={},b,z=null;const P="datetime format";for(let H=0;H<O.length&&(b=O[H],W=s[b]||{},z=W[r],!oe(z));H++)go(e,r,b,v,P);if(!oe(z)||!G(b))return n?ti:r;let T=`${b}__${r}`;Zn(d)||(T=`${T}__${JSON.stringify(d)}`);let F=l.get(T);return F||(F=new Intl.DateTimeFormat(b,Fe({},z,d)),l.set(T,F)),E?F.formatToParts(u):F.format(u)}const Xl=["localeMatcher","weekday","era","year","month","day","hour","minute","second","timeZoneName","formatMatcher","hour12","timeZone","dateStyle","timeStyle","calendar","dayPeriod","numberingSystem","hourCycle","fractionalSecondDigits"];function xi(...e){const[t,s,n,i]=e,o=ye();let a=ye(),l;if(G(t)){const r=t.match(/(\d{4}-\d{2}-\d{2})(T|\s)?(.*)/);if(!r)throw Ht($t.INVALID_ISO_DATE_ARGUMENT);const u=r[3]?r[3].trim().startsWith("T")?`${r[1].trim()}${r[3].trim()}`:`${r[1].trim()}T${r[3].trim()}`:r[1].trim();l=new Date(u);try{l.toISOString()}catch{throw Ht($t.INVALID_ISO_DATE_ARGUMENT)}}else if(tu(t)){if(isNaN(t.getTime()))throw Ht($t.INVALID_DATE_ARGUMENT);l=t}else if(Ne(t))l=t;else throw Ht($t.INVALID_ARGUMENT);return G(s)?o.key=s:oe(s)&&Object.keys(s).forEach(r=>{Xl.includes(r)?a[r]=s[r]:o[r]=s[r]}),G(n)?o.locale=n:oe(n)&&(a=n),oe(i)&&(a=i),[o.key||"",l,o,a]}function da(e,t,s){const n=e;for(const i in s){const o=`${t}__${i}`;n.__datetimeFormatters.has(o)&&n.__datetimeFormatters.delete(o)}}function ma(e,...t){const{numberFormats:s,unresolving:n,fallbackLocale:i,onWarn:o,localeFallbacker:a}=e,{__numberFormatters:l}=e,[r,u,f,d]=Ni(...t),v=re(f.missingWarn)?f.missingWarn:e.missingWarn;re(f.fallbackWarn)?f.fallbackWarn:e.fallbackWarn;const E=!!f.part,D=po(e,f),O=a(e,i,D);if(!G(r)||r==="")return new Intl.NumberFormat(D,d).format(u);let W={},b,z=null;const P="number format";for(let H=0;H<O.length&&(b=O[H],W=s[b]||{},z=W[r],!oe(z));H++)go(e,r,b,v,P);if(!oe(z)||!G(b))return n?ti:r;let T=`${b}__${r}`;Zn(d)||(T=`${T}__${JSON.stringify(d)}`);let F=l.get(T);return F||(F=new Intl.NumberFormat(b,Fe({},z,d)),l.set(T,F)),E?F.formatToParts(u):F.format(u)}const Kl=["localeMatcher","style","currency","currencyDisplay","currencySign","useGrouping","minimumIntegerDigits","minimumFractionDigits","maximumFractionDigits","minimumSignificantDigits","maximumSignificantDigits","compactDisplay","notation","signDisplay","unit","unitDisplay","roundingMode","roundingPriority","roundingIncrement","trailingZeroDisplay"];function Ni(...e){const[t,s,n,i]=e,o=ye();let a=ye();if(!Ne(t))throw Ht($t.INVALID_ARGUMENT);const l=t;return G(s)?o.key=s:oe(s)&&Object.keys(s).forEach(r=>{Kl.includes(r)?a[r]=s[r]:o[r]=s[r]}),G(n)?o.locale=n:oe(n)&&(a=n),oe(i)&&(a=i),[o.key||"",l,o,a]}function ha(e,t,s){const n=e;for(const i in s){const o=`${t}__${i}`;n.__numberFormatters.has(o)&&n.__numberFormatters.delete(o)}}const T0=e=>e,w0=e=>"",E0="text",z0=e=>e.length===0?"":co(e),I0=ou;function ya(e,t){return e=Math.abs(e),t===2?e?e>1?1:0:1:e?Math.min(e,2):0}function L0(e){const t=Ne(e.pluralIndex)?e.pluralIndex:-1;return e.named&&(Ne(e.named.count)||Ne(e.named.n))?Ne(e.named.count)?e.named.count:Ne(e.named.n)?e.named.n:t:t}function P0(e,t){t.count||(t.count=e),t.n||(t.n=e)}function M0(e={}){const t=e.locale,s=L0(e),n=ue(e.pluralRules)&&G(t)&&Te(e.pluralRules[t])?e.pluralRules[t]:ya,i=ue(e.pluralRules)&&G(t)&&Te(e.pluralRules[t])?ya:void 0,o=b=>b[n(s,b.length,i)],a=e.list||[],l=b=>a[b],r=e.named||ye();Ne(e.pluralIndex)&&P0(s,r);const u=b=>r[b];function f(b,z){const P=Te(e.messages)?e.messages(b,!!z):ue(e.messages)?e.messages[b]:!1;return P||(e.parent?e.parent.message(b):w0)}const d=b=>e.modifiers?e.modifiers[b]:T0,v=oe(e.processor)&&Te(e.processor.normalize)?e.processor.normalize:z0,E=oe(e.processor)&&Te(e.processor.interpolate)?e.processor.interpolate:I0,D=oe(e.processor)&&G(e.processor.type)?e.processor.type:E0,W={list:l,named:u,plural:o,linked:(b,...z)=>{const[P,T]=z;let F="text",H="";z.length===1?ue(P)?(H=P.modifier||H,F=P.type||F):G(P)&&(H=P||H):z.length===2&&(G(P)&&(H=P||H),G(T)&&(F=T||F));const k=f(b,!0)(W),ee=F==="vnode"&&De(k)&&H?k[0]:k;return H?d(H)(ee,F):ee},message:f,type:D,interpolate:E,normalize:v,values:Fe(ye(),a,r)};return W}const _a=()=>"",mt=e=>Te(e);function va(e,...t){const{fallbackFormat:s,postTranslation:n,unresolving:i,messageCompiler:o,fallbackLocale:a,messages:l}=e,[r,u]=Di(...t),f=re(u.missingWarn)?u.missingWarn:e.missingWarn,d=re(u.fallbackWarn)?u.fallbackWarn:e.fallbackWarn,v=re(u.escapeParameter)?u.escapeParameter:e.escapeParameter,E=!!u.resolvedMessage,D=G(u.default)||re(u.default)?re(u.default)?o?r:()=>r:u.default:s?o?r:()=>r:null,O=s||D!=null&&(G(D)||Te(D)),W=po(e,u);v&&A0(u);let[b,z,P]=E?[r,W,l[W]||ye()]:Jl(e,r,W,a,d,f),T=b,F=r;if(!E&&!(G(T)||Os(T)||mt(T))&&O&&(T=D,F=T),!E&&(!(G(T)||Os(T)||mt(T))||!G(z)))return i?ti:r;let H=!1;const k=()=>{H=!0},ee=mt(T)?T:Ql(e,r,z,T,F,k);if(H)return T;const ne=D0(e,z,P,u),_e=M0(ne),Ee=x0(e,ee,_e),ve=n?n(Ee,r):Ee;if(__INTLIFY_PROD_DEVTOOLS__){const Ze={timestamp:Date.now(),key:G(r)?r:mt(T)?T.key:"",locale:z||(mt(T)?T.locale:""),format:G(T)?T:mt(T)?T.source:"",message:ve};Ze.meta=Fe({},e.__meta,_0()||{}),qu(Ze)}return ve}function A0(e){De(e.list)?e.list=e.list.map(t=>G(t)?ta(t):t):ue(e.named)&&Object.keys(e.named).forEach(t=>{G(e.named[t])&&(e.named[t]=ta(e.named[t]))})}function Jl(e,t,s,n,i,o){const{messages:a,onWarn:l,messageResolver:r,localeFallbacker:u}=e,f=u(e,n,s);let d=ye(),v,E=null;const D="translate";for(let O=0;O<f.length&&(v=f[O],d=a[v]||ye(),(E=r(d,t))===null&&(E=d[t]),!(G(E)||Os(E)||mt(E)));O++)if(!S0(v,f)){const W=go(e,t,v,o,D);W!==t&&(E=W)}return[E,v,d]}function Ql(e,t,s,n,i,o){const{messageCompiler:a,warnHtmlMessage:l}=e;if(mt(n)){const u=n;return u.locale=u.locale||s,u.key=u.key||t,u}if(a==null){const u=()=>n;return u.locale=s,u.key=t,u}const r=a(n,N0(e,s,i,n,l,o));return r.locale=s,r.key=t,r.source=n,r}function x0(e,t,s){return t(s)}function Di(...e){const[t,s,n]=e,i=ye();if(!G(t)&&!Ne(t)&&!mt(t)&&!Os(t))throw Ht($t.INVALID_ARGUMENT);const o=Ne(t)?String(t):(mt(t),t);return Ne(s)?i.plural=s:G(s)?i.default=s:oe(s)&&!Zn(s)?i.named=s:De(s)&&(i.list=s),Ne(n)?i.plural=n:G(n)?i.default=n:oe(n)&&Fe(i,n),[o,i]}function N0(e,t,s,n,i,o){return{locale:t,key:s,warnHtmlMessage:i,onError:a=>{throw o&&o(a),a},onCacheKey:a=>Zf(t,s,a)}}function D0(e,t,s,n){const{modifiers:i,pluralRules:o,messageResolver:a,fallbackLocale:l,fallbackWarn:r,missingWarn:u,fallbackContext:f}=e,v={locale:t,modifiers:i,pluralRules:o,messages:(E,D)=>{let O=a(s,E);if(O==null&&(f||D)){const[,,W]=Jl(f||e,E,t,l,r,u);O=a(W,E)}if(G(O)||Os(O)){let W=!1;const z=Ql(e,E,t,O,E,()=>{W=!0});return W?_a:z}else return mt(O)?O:_a}};return e.processor&&(v.processor=e.processor),n.list&&(v.list=n.list),n.named&&(v.named=n.named),Ne(n.plural)&&(v.pluralIndex=n.plural),v}Au();/*!
  * vue-i18n v11.0.0-rc.1
  * (c) 2024 kazuya kawaguchi
  * Released under the MIT License.
  */const O0="11.0.0-rc.1";function R0(){typeof __VUE_I18N_FULL_INSTALL__!="boolean"&&(us().__VUE_I18N_FULL_INSTALL__=!0),typeof __VUE_I18N_LEGACY_API__!="boolean"&&(us().__VUE_I18N_LEGACY_API__=!0),typeof __INTLIFY_DROP_MESSAGE_COMPILER__!="boolean"&&(us().__INTLIFY_DROP_MESSAGE_COMPILER__=!1),typeof __INTLIFY_PROD_DEVTOOLS__!="boolean"&&(us().__INTLIFY_PROD_DEVTOOLS__=!1)}const tt={UNEXPECTED_RETURN_TYPE:e0,INVALID_ARGUMENT:25,MUST_BE_CALL_SETUP_TOP:26,NOT_INSTALLED:27,REQUIRED_VALUE:28,INVALID_VALUE:29,CANNOT_SETUP_VUE_DEVTOOLS_PLUGIN:30,NOT_INSTALLED_WITH_PROVIDE:31,UNEXPECTED_ERROR:32,NOT_COMPATIBLE_LEGACY_VUE_I18N:33,NOT_AVAILABLE_COMPOSITION_IN_LEGACY:34};function ct(e,...t){return ei(e,null,void 0)}const Oi=ns("__translateVNode"),Ri=ns("__datetimeParts"),Fi=ns("__numberParts"),ql=ns("__setPluralRules"),Zl=ns("__injectWithOption"),ki=ns("__dispose");function un(e){if(!ue(e))return e;for(const t in e)if(Ct(e,t))if(!t.includes("."))ue(e[t])&&un(e[t]);else{const s=t.split("."),n=s.length-1;let i=e,o=!1;for(let a=0;a<n;a++){if(s[a]in i||(i[s[a]]=ye()),!ue(i[s[a]])){o=!0;break}i=i[s[a]]}o||(i[s[n]]=e[t],delete e[t]),ue(i[s[n]])&&un(i[s[n]])}return e}function mo(e,t){const{messages:s,__i18n:n,messageResolver:i,flatJson:o}=t,a=oe(s)?s:De(n)?ye():{[e]:ye()};if(De(n)&&n.forEach(l=>{if("locale"in l&&"resource"in l){const{locale:r,resource:u}=l;r?(a[r]=a[r]||ye(),On(u,a[r])):On(u,a)}else G(l)&&On(JSON.parse(l),a)}),i==null&&o)for(const l in a)Ct(a,l)&&un(a[l]);return a}function er(e){return e.type}function tr(e,t,s){let n=ue(t.messages)?t.messages:ye();"__i18nGlobal"in s&&(n=mo(e.locale.value,{messages:n,__i18n:s.__i18nGlobal}));const i=Object.keys(n);i.length&&i.forEach(o=>{e.mergeLocaleMessage(o,n[o])});{if(ue(t.datetimeFormats)){const o=Object.keys(t.datetimeFormats);o.length&&o.forEach(a=>{e.mergeDateTimeFormat(a,t.datetimeFormats[a])})}if(ue(t.numberFormats)){const o=Object.keys(t.numberFormats);o.length&&o.forEach(a=>{e.mergeNumberFormat(a,t.numberFormats[a])})}}}function ba(e){return je(hn,null,e,0)}const Ca="__INTLIFY_META__",Sa=()=>[],F0=()=>!1;let Ta=0;function wa(e){return(t,s,n,i)=>e(s,n,an()||void 0,i)}const k0=()=>{const e=an();let t=null;return e&&(t=er(e)[Ca])?{[Ca]:t}:null};function ho(e={}){const{__root:t,__injectWithOption:s}=e,n=t===void 0,i=e.flatJson,o=Hn?$:gc;let a=re(e.inheritLocale)?e.inheritLocale:!0;const l=o(t&&a?t.locale.value:G(e.locale)?e.locale:fn),r=o(t&&a?t.fallbackLocale.value:G(e.fallbackLocale)||De(e.fallbackLocale)||oe(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:l.value),u=o(mo(l.value,e)),f=o(oe(e.datetimeFormats)?e.datetimeFormats:{[l.value]:{}}),d=o(oe(e.numberFormats)?e.numberFormats:{[l.value]:{}});let v=t?t.missingWarn:re(e.missingWarn)||Ns(e.missingWarn)?e.missingWarn:!0,E=t?t.fallbackWarn:re(e.fallbackWarn)||Ns(e.fallbackWarn)?e.fallbackWarn:!0,D=t?t.fallbackRoot:re(e.fallbackRoot)?e.fallbackRoot:!0,O=!!e.fallbackFormat,W=Te(e.missing)?e.missing:null,b=Te(e.missing)?wa(e.missing):null,z=Te(e.postTranslation)?e.postTranslation:null,P=t?t.warnHtmlMessage:re(e.warnHtmlMessage)?e.warnHtmlMessage:!0,T=!!e.escapeParameter;const F=t?t.modifiers:oe(e.modifiers)?e.modifiers:{};let H=e.pluralRules||t&&t.pluralRules,k;k=(()=>{n&&ua(null);const y={version:O0,locale:l.value,fallbackLocale:r.value,messages:u.value,modifiers:F,pluralRules:H,missing:b===null?void 0:b,missingWarn:v,fallbackWarn:E,fallbackFormat:O,unresolving:!0,postTranslation:z===null?void 0:z,warnHtmlMessage:P,escapeParameter:T,messageResolver:e.messageResolver,messageCompiler:e.messageCompiler,__meta:{framework:"vue"}};y.datetimeFormats=f.value,y.numberFormats=d.value,y.__datetimeFormatters=oe(k)?k.__datetimeFormatters:void 0,y.__numberFormatters=oe(k)?k.__numberFormatters:void 0;const w=b0(y);return n&&ua(w),w})(),Ys(k,l.value,r.value);function ne(){return[l.value,r.value,u.value,f.value,d.value]}const _e=Ts({get:()=>l.value,set:y=>{l.value=y,k.locale=l.value}}),Ee=Ts({get:()=>r.value,set:y=>{r.value=y,k.fallbackLocale=r.value,Ys(k,l.value,y)}}),ve=Ts(()=>u.value),Ze=Ts(()=>f.value),ft=Ts(()=>d.value);function ut(){return Te(z)?z:null}function ze(y){z=y,k.postTranslation=y}function ce(){return W}function ae(y){y!==null&&(b=wa(y)),W=y,k.missing=b}const Pe=(y,w,Y,X,se,ie)=>{ne();let pe;try{__INTLIFY_PROD_DEVTOOLS__,n||(k.fallbackContext=t?v0():void 0),pe=y(k)}finally{__INTLIFY_PROD_DEVTOOLS__,n||(k.fallbackContext=void 0)}if(Y!=="translate exists"&&Ne(pe)&&pe===ti||Y==="translate exists"&&!pe){const[he,Ve]=w();return t&&D?X(t):se(he)}else{if(ie(pe))return pe;throw ct(tt.UNEXPECTED_RETURN_TYPE)}};function Be(...y){return Pe(w=>Reflect.apply(va,null,[w,...y]),()=>Di(...y),"translate",w=>Reflect.apply(w.t,w,[...y]),w=>w,w=>G(w))}function Me(...y){const[w,Y,X]=y;if(X&&!ue(X))throw ct(tt.INVALID_ARGUMENT);return Be(w,Y,Fe({resolvedMessage:!0},X||{}))}function Ae(...y){return Pe(w=>Reflect.apply(ga,null,[w,...y]),()=>xi(...y),"datetime format",w=>Reflect.apply(w.d,w,[...y]),()=>ca,w=>G(w))}function Tt(...y){return Pe(w=>Reflect.apply(ma,null,[w,...y]),()=>Ni(...y),"number format",w=>Reflect.apply(w.n,w,[...y]),()=>ca,w=>G(w))}function pt(y){return y.map(w=>G(w)||Ne(w)||re(w)?ba(String(w)):w)}const yt={normalize:pt,interpolate:y=>y,type:"vnode"};function et(...y){return Pe(w=>{let Y;const X=w;try{X.processor=yt,Y=Reflect.apply(va,null,[X,...y])}finally{X.processor=null}return Y},()=>Di(...y),"translate",w=>w[Oi](...y),w=>[ba(w)],w=>De(w))}function _t(...y){return Pe(w=>Reflect.apply(ma,null,[w,...y]),()=>Ni(...y),"number format",w=>w[Fi](...y),Sa,w=>G(w)||De(w))}function gt(...y){return Pe(w=>Reflect.apply(ga,null,[w,...y]),()=>xi(...y),"datetime format",w=>w[Ri](...y),Sa,w=>G(w)||De(w))}function Ge(y){H=y,k.pluralRules=H}function st(y,w){return Pe(()=>{if(!y)return!1;const Y=G(w)?w:l.value,X=h(Y),se=k.messageResolver(X,y);return Os(se)||mt(se)||G(se)},()=>[y],"translate exists",Y=>Reflect.apply(Y.te,Y,[y,w]),F0,Y=>re(Y))}function nt(y){let w=null;const Y=$l(k,r.value,l.value);for(let X=0;X<Y.length;X++){const se=u.value[Y[X]]||{},ie=k.messageResolver(se,y);if(ie!=null){w=ie;break}}return w}function m(y){const w=nt(y);return w??(t?t.tm(y)||{}:{})}function h(y){return u.value[y]||{}}function L(y,w){if(i){const Y={[y]:w};for(const X in Y)Ct(Y,X)&&un(Y[X]);w=Y[y]}u.value[y]=w,k.messages=u.value}function U(y,w){u.value[y]=u.value[y]||{};const Y={[y]:w};if(i)for(const X in Y)Ct(Y,X)&&un(Y[X]);w=Y[y],On(w,u.value[y]),k.messages=u.value}function R(y){return f.value[y]||{}}function c(y,w){f.value[y]=w,k.datetimeFormats=f.value,da(k,y,w)}function g(y,w){f.value[y]=Fe(f.value[y]||{},w),k.datetimeFormats=f.value,da(k,y,w)}function S(y){return d.value[y]||{}}function I(y,w){d.value[y]=w,k.numberFormats=d.value,ha(k,y,w)}function A(y,w){d.value[y]=Fe(d.value[y]||{},w),k.numberFormats=d.value,ha(k,y,w)}Ta++,t&&Hn&&(Yt(t.locale,y=>{a&&(l.value=y,k.locale=y,Ys(k,l.value,r.value))}),Yt(t.fallbackLocale,y=>{a&&(r.value=y,k.fallbackLocale=y,Ys(k,l.value,r.value))}));const V={id:Ta,locale:_e,fallbackLocale:Ee,get inheritLocale(){return a},set inheritLocale(y){a=y,y&&t&&(l.value=t.locale.value,r.value=t.fallbackLocale.value,Ys(k,l.value,r.value))},get availableLocales(){return Object.keys(u.value).sort()},messages:ve,get modifiers(){return F},get pluralRules(){return H||{}},get isGlobal(){return n},get missingWarn(){return v},set missingWarn(y){v=y,k.missingWarn=v},get fallbackWarn(){return E},set fallbackWarn(y){E=y,k.fallbackWarn=E},get fallbackRoot(){return D},set fallbackRoot(y){D=y},get fallbackFormat(){return O},set fallbackFormat(y){O=y,k.fallbackFormat=O},get warnHtmlMessage(){return P},set warnHtmlMessage(y){P=y,k.warnHtmlMessage=y},get escapeParameter(){return T},set escapeParameter(y){T=y,k.escapeParameter=y},t:Be,getLocaleMessage:h,setLocaleMessage:L,mergeLocaleMessage:U,getPostTranslationHandler:ut,setPostTranslationHandler:ze,getMissingHandler:ce,setMissingHandler:ae,[ql]:Ge};return V.datetimeFormats=Ze,V.numberFormats=ft,V.rt=Me,V.te=st,V.tm=m,V.d=Ae,V.n=Tt,V.getDateTimeFormat=R,V.setDateTimeFormat=c,V.mergeDateTimeFormat=g,V.getNumberFormat=S,V.setNumberFormat=I,V.mergeNumberFormat=A,V[Zl]=s,V[Oi]=et,V[Ri]=gt,V[Fi]=_t,V}function U0(e){const t=G(e.locale)?e.locale:fn,s=G(e.fallbackLocale)||De(e.fallbackLocale)||oe(e.fallbackLocale)||e.fallbackLocale===!1?e.fallbackLocale:t,n=Te(e.missing)?e.missing:void 0,i=re(e.silentTranslationWarn)||Ns(e.silentTranslationWarn)?!e.silentTranslationWarn:!0,o=re(e.silentFallbackWarn)||Ns(e.silentFallbackWarn)?!e.silentFallbackWarn:!0,a=re(e.fallbackRoot)?e.fallbackRoot:!0,l=!!e.formatFallbackMessages,r=oe(e.modifiers)?e.modifiers:{},u=e.pluralizationRules,f=Te(e.postTranslation)?e.postTranslation:void 0,d=G(e.warnHtmlInMessage)?e.warnHtmlInMessage!=="off":!0,v=!!e.escapeParameterHtml,E=re(e.sync)?e.sync:!0;let D=e.messages;if(oe(e.sharedMessages)){const F=e.sharedMessages;D=Object.keys(F).reduce((k,ee)=>{const ne=k[ee]||(k[ee]={});return Fe(ne,F[ee]),k},D||{})}const{__i18n:O,__root:W,__injectWithOption:b}=e,z=e.datetimeFormats,P=e.numberFormats,T=e.flatJson;return{locale:t,fallbackLocale:s,messages:D,flatJson:T,datetimeFormats:z,numberFormats:P,missing:n,missingWarn:i,fallbackWarn:o,fallbackRoot:a,fallbackFormat:l,modifiers:r,pluralRules:u,postTranslation:f,warnHtmlMessage:d,escapeParameter:v,messageResolver:e.messageResolver,inheritLocale:E,__i18n:O,__root:W,__injectWithOption:b}}function Ui(e={}){const t=ho(U0(e)),{__extender:s}=e,n={id:t.id,get locale(){return t.locale.value},set locale(i){t.locale.value=i},get fallbackLocale(){return t.fallbackLocale.value},set fallbackLocale(i){t.fallbackLocale.value=i},get messages(){return t.messages.value},get datetimeFormats(){return t.datetimeFormats.value},get numberFormats(){return t.numberFormats.value},get availableLocales(){return t.availableLocales},get missing(){return t.getMissingHandler()},set missing(i){t.setMissingHandler(i)},get silentTranslationWarn(){return re(t.missingWarn)?!t.missingWarn:t.missingWarn},set silentTranslationWarn(i){t.missingWarn=re(i)?!i:i},get silentFallbackWarn(){return re(t.fallbackWarn)?!t.fallbackWarn:t.fallbackWarn},set silentFallbackWarn(i){t.fallbackWarn=re(i)?!i:i},get modifiers(){return t.modifiers},get formatFallbackMessages(){return t.fallbackFormat},set formatFallbackMessages(i){t.fallbackFormat=i},get postTranslation(){return t.getPostTranslationHandler()},set postTranslation(i){t.setPostTranslationHandler(i)},get sync(){return t.inheritLocale},set sync(i){t.inheritLocale=i},get warnHtmlInMessage(){return t.warnHtmlMessage?"warn":"off"},set warnHtmlInMessage(i){t.warnHtmlMessage=i!=="off"},get escapeParameterHtml(){return t.escapeParameter},set escapeParameterHtml(i){t.escapeParameter=i},get pluralizationRules(){return t.pluralRules||{}},__composer:t,t(...i){return Reflect.apply(t.t,t,[...i])},rt(...i){return Reflect.apply(t.rt,t,[...i])},te(i,o){return t.te(i,o)},tm(i){return t.tm(i)},getLocaleMessage(i){return t.getLocaleMessage(i)},setLocaleMessage(i,o){t.setLocaleMessage(i,o)},mergeLocaleMessage(i,o){t.mergeLocaleMessage(i,o)},d(...i){return Reflect.apply(t.d,t,[...i])},getDateTimeFormat(i){return t.getDateTimeFormat(i)},setDateTimeFormat(i,o){t.setDateTimeFormat(i,o)},mergeDateTimeFormat(i,o){t.mergeDateTimeFormat(i,o)},n(...i){return Reflect.apply(t.n,t,[...i])},getNumberFormat(i){return t.getNumberFormat(i)},setNumberFormat(i,o){t.setNumberFormat(i,o)},mergeNumberFormat(i,o){t.mergeNumberFormat(i,o)}};return n.__extender=s,n}function W0(e,t,s){return{beforeCreate(){const n=an();if(!n)throw ct(tt.UNEXPECTED_ERROR);const i=this.$options;if(i.i18n){const o=i.i18n;if(i.__i18n&&(o.__i18n=i.__i18n),o.__root=t,this===this.$root)this.$i18n=Ea(e,o);else{o.__injectWithOption=!0,o.__extender=s.__vueI18nExtend,this.$i18n=Ui(o);const a=this.$i18n;a.__extender&&(a.__disposer=a.__extender(this.$i18n))}}else if(i.__i18n)if(this===this.$root)this.$i18n=Ea(e,i);else{this.$i18n=Ui({__i18n:i.__i18n,__injectWithOption:!0,__extender:s.__vueI18nExtend,__root:t});const o=this.$i18n;o.__extender&&(o.__disposer=o.__extender(this.$i18n))}else this.$i18n=e;i.__i18nGlobal&&tr(t,i,i),this.$t=(...o)=>this.$i18n.t(...o),this.$rt=(...o)=>this.$i18n.rt(...o),this.$te=(o,a)=>this.$i18n.te(o,a),this.$d=(...o)=>this.$i18n.d(...o),this.$n=(...o)=>this.$i18n.n(...o),this.$tm=o=>this.$i18n.tm(o),s.__setInstance(n,this.$i18n)},mounted(){},unmounted(){const n=an();if(!n)throw ct(tt.UNEXPECTED_ERROR);const i=this.$i18n;delete this.$t,delete this.$rt,delete this.$te,delete this.$d,delete this.$n,delete this.$tm,i.__disposer&&(i.__disposer(),delete i.__disposer,delete i.__extender),s.__deleteInstance(n),delete this.$i18n}}}function Ea(e,t){e.locale=t.locale||e.locale,e.fallbackLocale=t.fallbackLocale||e.fallbackLocale,e.missing=t.missing||e.missing,e.silentTranslationWarn=t.silentTranslationWarn||e.silentFallbackWarn,e.silentFallbackWarn=t.silentFallbackWarn||e.silentFallbackWarn,e.formatFallbackMessages=t.formatFallbackMessages||e.formatFallbackMessages,e.postTranslation=t.postTranslation||e.postTranslation,e.warnHtmlInMessage=t.warnHtmlInMessage||e.warnHtmlInMessage,e.escapeParameterHtml=t.escapeParameterHtml||e.escapeParameterHtml,e.sync=t.sync||e.sync,e.__composer[ql](t.pluralizationRules||e.pluralizationRules);const s=mo(e.locale,{messages:t.messages,__i18n:t.__i18n});return Object.keys(s).forEach(n=>e.mergeLocaleMessage(n,s[n])),t.datetimeFormats&&Object.keys(t.datetimeFormats).forEach(n=>e.mergeDateTimeFormat(n,t.datetimeFormats[n])),t.numberFormats&&Object.keys(t.numberFormats).forEach(n=>e.mergeNumberFormat(n,t.numberFormats[n])),e}const yo={tag:{type:[String,Object]},locale:{type:String},scope:{type:String,validator:e=>e==="parent"||e==="global",default:"parent"},i18n:{type:Object}};function V0({slots:e},t){return t.length===1&&t[0]==="default"?(e.default?e.default():[]).reduce((n,i)=>[...n,...i.type===Le?i.children:[i]],[]):t.reduce((s,n)=>{const i=e[n];return i&&(s[n]=i()),s},ye())}function sr(){return Le}const $0=mn({name:"i18n-t",props:Fe({keypath:{type:String,required:!0},plural:{type:[Number,String],validator:e=>Ne(e)||!isNaN(e)}},yo),setup(e,t){const{slots:s,attrs:n}=t,i=e.i18n||_n({useScope:e.scope,__useComponent:!0});return()=>{const o=Object.keys(s).filter(d=>d!=="_"),a=ye();e.locale&&(a.locale=e.locale),e.plural!==void 0&&(a.plural=G(e.plural)?+e.plural:e.plural);const l=V0(t,o),r=i[Oi](e.keypath,l,a),u=Fe(ye(),n),f=G(e.tag)||ue(e.tag)?e.tag:sr();return Fl(f,u,r)}}}),za=$0;function H0(e){return De(e)&&!G(e[0])}function nr(e,t,s,n){const{slots:i,attrs:o}=t;return()=>{const a={part:!0};let l=ye();e.locale&&(a.locale=e.locale),G(e.format)?a.key=e.format:ue(e.format)&&(G(e.format.key)&&(a.key=e.format.key),l=Object.keys(e.format).reduce((v,E)=>s.includes(E)?Fe(ye(),v,{[E]:e.format[E]}):v,ye()));const r=n(e.value,a,l);let u=[a.key];De(r)?u=r.map((v,E)=>{const D=i[v.type],O=D?D({[v.type]:v.value,index:E,parts:r}):[v.value];return H0(O)&&(O[0].key=`${v.type}-${E}`),O}):G(r)&&(u=[r]);const f=Fe(ye(),o),d=G(e.tag)||ue(e.tag)?e.tag:sr();return Fl(d,f,u)}}const j0=mn({name:"i18n-n",props:Fe({value:{type:Number,required:!0},format:{type:[String,Object]}},yo),setup(e,t){const s=e.i18n||_n({useScope:e.scope,__useComponent:!0});return nr(e,t,Kl,(...n)=>s[Fi](...n))}}),Ia=j0;function Y0(e,t){const s=e;if(e.mode==="composition")return s.__getInstance(t)||e.global;{const n=s.__getInstance(t);return n!=null?n.__composer:e.global.__composer}}function B0(e){const t=a=>{const{instance:l,value:r}=a;if(!l||!l.$)throw ct(tt.UNEXPECTED_ERROR);const u=Y0(e,l.$),f=La(r);return[Reflect.apply(u.t,u,[...Pa(f)]),u]};return{created:(a,l)=>{const[r,u]=t(l);Hn&&e.global===u&&(a.__i18nWatcher=Yt(u.locale,()=>{l.instance&&l.instance.$forceUpdate()})),a.__composer=u,a.textContent=r},unmounted:a=>{Hn&&a.__i18nWatcher&&(a.__i18nWatcher(),a.__i18nWatcher=void 0,delete a.__i18nWatcher),a.__composer&&(a.__composer=void 0,delete a.__composer)},beforeUpdate:(a,{value:l})=>{if(a.__composer){const r=a.__composer,u=La(l);a.textContent=Reflect.apply(r.t,r,[...Pa(u)])}},getSSRProps:a=>{const[l]=t(a);return{textContent:l}}}}function La(e){if(G(e))return{path:e};if(oe(e)){if(!("path"in e))throw ct(tt.REQUIRED_VALUE,"path");return e}else throw ct(tt.INVALID_VALUE)}function Pa(e){const{path:t,locale:s,args:n,choice:i,plural:o}=e,a={},l=n||{};return G(s)&&(a.locale=s),Ne(i)&&(a.plural=i),Ne(o)&&(a.plural=o),[t,l,a]}function G0(e,t,...s){const n=oe(s[0])?s[0]:{};(re(n.globalInstall)?n.globalInstall:!0)&&([za.name,"I18nT"].forEach(o=>e.component(o,za)),[Ia.name,"I18nN"].forEach(o=>e.component(o,Ia)),[Aa.name,"I18nD"].forEach(o=>e.component(o,Aa))),e.directive("t",B0(t))}const X0=ns("global-vue-i18n");function K0(e={}){const t=__VUE_I18N_LEGACY_API__&&re(e.legacy)?e.legacy:__VUE_I18N_LEGACY_API__,s=re(e.globalInjection)?e.globalInjection:!0,n=new Map,[i,o]=J0(e,t),a=ns("");function l(d){return n.get(d)||null}function r(d,v){n.set(d,v)}function u(d){n.delete(d)}const f={get mode(){return __VUE_I18N_LEGACY_API__&&t?"legacy":"composition"},async install(d,...v){if(d.__VUE_I18N_SYMBOL__=a,d.provide(d.__VUE_I18N_SYMBOL__,f),oe(v[0])){const O=v[0];f.__composerExtend=O.__composerExtend,f.__vueI18nExtend=O.__vueI18nExtend}let E=null;!t&&s&&(E=i1(d,f.global)),__VUE_I18N_FULL_INSTALL__&&G0(d,f,...v),__VUE_I18N_LEGACY_API__&&t&&d.mixin(W0(o,o.__composer,f));const D=d.unmount;d.unmount=()=>{E&&E(),f.dispose(),D()}},get global(){return o},dispose(){i.stop()},__instances:n,__getInstance:l,__setInstance:r,__deleteInstance:u};return f}function _n(e={}){const t=an();if(t==null)throw ct(tt.MUST_BE_CALL_SETUP_TOP);if(!t.isCE&&t.appContext.app!=null&&!t.appContext.app.__VUE_I18N_SYMBOL__)throw ct(tt.NOT_INSTALLED);const s=Q0(t),n=Z0(s),i=er(t),o=q0(e,i);if(o==="global")return tr(n,e,i),n;if(o==="parent"){let r=e1(s,t,e.__useComponent);return r==null&&(r=n),r}const a=s;let l=a.__getInstance(t);if(l==null){const r=Fe({},e);"__i18n"in i&&(r.__i18n=i.__i18n),n&&(r.__root=n),l=ho(r),a.__composerExtend&&(l[ki]=a.__composerExtend(l)),s1(a,t,l),a.__setInstance(t,l)}return l}function J0(e,t){const s=$r(),n=__VUE_I18N_LEGACY_API__&&t?s.run(()=>Ui(e)):s.run(()=>ho(e));if(n==null)throw ct(tt.UNEXPECTED_ERROR);return[s,n]}function Q0(e){const t=qs(e.isCE?X0:e.appContext.app.__VUE_I18N_SYMBOL__);if(!t)throw ct(e.isCE?tt.NOT_INSTALLED_WITH_PROVIDE:tt.UNEXPECTED_ERROR);return t}function q0(e,t){return Zn(e)?"__i18n"in t?"local":"global":e.useScope?e.useScope:"local"}function Z0(e){return e.mode==="composition"?e.global:e.global.__composer}function e1(e,t,s=!1){let n=null;const i=t.root;let o=t1(t,s);for(;o!=null;){const a=e;if(e.mode==="composition")n=a.__getInstance(o);else if(__VUE_I18N_LEGACY_API__){const l=a.__getInstance(o);l!=null&&(n=l.__composer,s&&n&&!n[Zl]&&(n=null))}if(n!=null||i===o)break;o=o.parent}return n}function t1(e,t=!1){return e==null?null:t&&e.vnode.ctx||e.parent}function s1(e,t,s){Js(()=>{},t),io(()=>{const n=s;e.__deleteInstance(t);const i=n[ki];i&&(i(),delete n[ki])},t)}const n1=["locale","fallbackLocale","availableLocales"],Ma=["t","rt","d","n","tm","te"];function i1(e,t){const s=Object.create(null);return n1.forEach(i=>{const o=Object.getOwnPropertyDescriptor(t,i);if(!o)throw ct(tt.UNEXPECTED_ERROR);const a=Ue(o.value)?{get(){return o.value.value},set(l){o.value.value=l}}:{get(){return o.get&&o.get()}};Object.defineProperty(s,i,a)}),e.config.globalProperties.$i18n=s,Ma.forEach(i=>{const o=Object.getOwnPropertyDescriptor(t,i);if(!o||!o.value)throw ct(tt.UNEXPECTED_ERROR);Object.defineProperty(e.config.globalProperties,`$${i}`,o)}),()=>{delete e.config.globalProperties.$i18n,Ma.forEach(i=>{delete e.config.globalProperties[`$${i}`]})}}const o1=mn({name:"i18n-d",props:Fe({value:{type:[Number,Date],required:!0},format:{type:[String,Object]}},yo),setup(e,t){const s=e.i18n||_n({useScope:e.scope,__useComponent:!0});return nr(e,t,Xl,(...n)=>s[Ri](...n))}}),Aa=o1;R0();d0(Ku);m0(u0);h0($l);if(__INTLIFY_PROD_DEVTOOLS__){const e=us();e.__INTLIFY__=!0,Ju(e.__INTLIFY_DEVTOOLS_GLOBAL_HOOK__)}function a1(e,t,s,n,i,o,a){e.beginPath(),e.ellipse(t,s,n,i,0,0,Math.PI*2),e.strokeStyle=a,e.lineWidth=o,e.stroke()}class l1{constructor(t){J(this,"mmToPixel",10);this.mmToPixel=t}drawCircleList(t,s,n,i,o){s.forEach(a=>{a.drawInnerCircle&&this.drawCircle(t,n,i,o,a)})}drawCircle(t,s,n,i,o){const a=(o.innerCircleLineRadiusX-o.innerCircleLineWidth)/2,l=(o.innerCircleLineRadiusY-o.innerCircleLineWidth)/2;this.drawEllipse(t,s,n,a*this.mmToPixel,l*this.mmToPixel,o.innerCircleLineWidth*this.mmToPixel,i)}drawEllipse(t,s,n,i,o,a,l){t.beginPath(),t.ellipse(s,n,i,o,0,0,Math.PI*2),t.strokeStyle=l,t.lineWidth=a,t.stroke()}}class r1{constructor(t){J(this,"mmToPixel",10);this.mmToPixel=t}drawCompanyList(t,s,n,i,o,a,l){s.forEach(r=>{this.drawCompanyName(t,r,n,i,o,a,r.color||l)})}drawCompanyName(t,s,n,i,o,a,l){const r=s.fontHeight*this.mmToPixel,u=s.fontWeight||"normal";t.save(),t.font=`${u} ${r}px ${s.fontFamily}`,t.fillStyle=l,t.textAlign="center",t.textBaseline="bottom";const f=s.companyName.split(""),d=f.length,v=s.borderOffset*this.mmToPixel,E=Math.PI*(.5+d/(s.textDistributionFactor*4)),D=E/d,O=s.rotateDirection==="clockwise"?-1:1,W=(s.startAngle?s.startAngle:0)+(s.rotateDirection==="clockwise"?Math.PI-E/2:Math.PI+(Math.PI-E)/2);if(s.adjustEllipseText){const b=(d+1)/2;f.forEach((z,P)=>{const T=b-P-1,H=Math.pow(T/b,2)*D*s.adjustEllipseTextFactor,k=P-b,ee=k/Math.abs(k);let ne=W+O*D*(P+.5);ne+=H*ee;const _e=n+Math.cos(ne)*(o-r-v),Ee=i+Math.sin(ne)*(a-r-v);t.save(),t.translate(_e,Ee),t.rotate(ne+(s.rotateDirection==="clockwise"?-Math.PI/2:Math.PI/2)),t.scale(s.compression,1),t.fillText(z,0,0),t.restore()})}else f.forEach((b,z)=>{const P=W+O*D*(z+.5),T=n+Math.cos(P)*(o-r-v),F=i+Math.sin(P)*(a-r-v);t.save(),t.translate(T,F),t.rotate(P+(s.rotateDirection==="clockwise"?-Math.PI/2:Math.PI/2)),t.scale(s.compression,1),t.fillText(b,0,0),t.restore()});t.restore()}}class c1{constructor(t,s){J(this,"mmToPixel",10);J(this,"rulerSize",80);J(this,"drawPositionCrossLines",(t,s,n,i,o,a,l)=>{const r=t;if(!r)return;const u=r.getContext("2d");if(u&&(u.clearRect(0,0,r.width,r.height),u.beginPath(),u.strokeStyle=l,u.lineWidth=1,u.moveTo(n,a),u.lineTo(r.width,a),u.moveTo(o,i),u.lineTo(o,r.height),u.stroke(),s)){const f=s.getContext("2d");f&&f.drawImage(r,0,0)}});J(this,"drawCurrentPositionText",(t,s,n,i,o,a)=>{t.fillStyle="black",t.font="bold 12px Arial",t.textAlign="left",t.textBaseline="top";const l=s/i,r=n/i;t.fillText(`${l.toFixed(1)}mm, ${r.toFixed(1)}mm, scale: ${i.toFixed(2)}`,o+5,a+5)});this.mmToPixel=t,this.rulerSize=s}drawRuler(t,s,n,i,o,a){if(!s.showRuler)return;const l=1/this.mmToPixel;t.save(),t.fillStyle="lightgray",a?t.fillRect(0,0,i,o):t.fillRect(0,0,o,i),t.fillStyle="black",t.font="10px Arial",t.textAlign="center",t.textBaseline="top";const r=this.mmToPixel,u=Math.ceil((i-o)*l/n);for(let f=0;f<=u;f++){const d=f*r*n+o;f%5===0?(t.beginPath(),a?(t.moveTo(d,0),t.lineTo(d,o*.8)):(t.moveTo(0,d),t.lineTo(o*.8,d)),t.lineWidth=1,t.stroke(),t.save(),a?t.fillText(f.toString(),d,o*.8):(t.translate(o*.8,d),t.rotate(-Math.PI/2),t.fillText(f.toString(),0,0)),t.restore()):(t.beginPath(),a?(t.moveTo(d,0),t.lineTo(d,o*.6)):(t.moveTo(0,d),t.lineTo(o*.6,d)),t.lineWidth=.5,t.stroke())}t.restore()}showCrossDashLine(t,s,n,i,o,a,l){if(!s.showDashLine)return;t.save(),t.strokeStyle="#bbbbbb",t.lineWidth=1,t.setLineDash([5,5]);const r=this.mmToPixel*5;for(let u=this.rulerSize;u<a;u+=r*n)t.beginPath(),t.moveTo(u,o),t.lineTo(u,l),t.stroke();for(let u=this.rulerSize;u<l;u+=r*n)t.beginPath(),t.moveTo(i,u),t.lineTo(a,u),t.stroke();t.restore()}}class f1{constructor(t){J(this,"mmToPixel",10);this.mmToPixel=t}drawSecurityPattern(t,s,n,i,o,a,l){t.save(),t.strokeStyle="#FFFFFF",t.lineWidth=s.securityPatternWidth*this.mmToPixel,t.globalCompositeOperation="destination-out";const r=s.securityPatternAngleRange*Math.PI/180;if(l||s.securityPatternParams.length===0){s.securityPatternParams=[];for(let u=0;u<s.securityPatternCount;u++){const f=Math.random()*Math.PI*2,v=Math.atan2(a*Math.cos(f),o*Math.sin(f))+(Math.random()-.5)*r;s.securityPatternParams.push({angle:f,lineAngle:v})}}s.securityPatternParams.forEach(({angle:u,lineAngle:f})=>{const d=n+o*Math.cos(u),v=i+a*Math.sin(u),E=s.securityPatternLength*this.mmToPixel,D=d-E/2*Math.cos(f),O=v-E/2*Math.sin(f),W=d+E/2*Math.cos(f),b=v+E/2*Math.sin(f);t.beginPath(),t.moveTo(D,O),t.lineTo(W,b),t.stroke()}),t.restore()}}class xa{constructor(t){J(this,"mmToPixel",10);this.mmToPixel=t}async drawSVGContent(t,s,n,i,o=1){try{const a=10*this.mmToPixel,l=document.createElement("div");l.innerHTML=s;const r=l.querySelector("svg");if(!r)throw new Error("Invalid SVG content");r.hasAttribute("width")||r.setAttribute("width","100"),r.hasAttribute("height")||r.setAttribute("height","100");const u=parseFloat(r.getAttribute("width")||"100"),f=parseFloat(r.getAttribute("height")||"100"),d=new XMLSerializer().serializeToString(r),E=`data:image/svg+xml;base64,${btoa(unescape(encodeURIComponent(d)))}`,D=new Image;D.src=E,await new Promise((O,W)=>{D.onload=O,D.onerror=W}),t.save(),t.translate(n,i),t.scale(1,1),t.strokeStyle="blue",t.lineWidth=1,t.strokeRect(10,10,a,a),t.restore(),console.log("draw svg base64Url")}catch(a){console.error("Error drawing SVG:",a)}}async loadAndDrawSVG(t,s,n,i,o=1){try{const a=10*this.mmToPixel;console.log("draw test svg content",s,n,i,o),t.save(),t.translate(n,i),t.scale(1,1),t.strokeStyle="blue",t.lineWidth=1,t.strokeRect(10,10,a,a),t.restore()}catch(a){console.error("Error loading SVG:",a)}}async drawStarShape(t,s,n,i,o){try{if(s.svgPath.startsWith("<svg"))await this.drawSVGContent(t,s.svgPath,n,i+s.starPositionY*this.mmToPixel,s.starDiameter*this.mmToPixel/40);else if(s.svgPath.endsWith(".svg"))await this.loadAndDrawSVG(t,s.svgPath,n,i+s.starPositionY*this.mmToPixel,s.starDiameter*this.mmToPixel/40);else{t.save(),t.translate(n,i+s.starPositionY*this.mmToPixel),t.scale(s.starDiameter*this.mmToPixel/2,s.starDiameter*this.mmToPixel/2);const a=new Path2D(s.svgPath);t.fillStyle=o,t.fill(a),t.restore()}}catch(a){console.error("Error in drawStarShape:",a)}}}class u1{constructor(){J(this,"primaryColor","blue");J(this,"ruler",{showRuler:!0,showFullRuler:!0,showCrossLine:!0,showDashLine:!0,showSideRuler:!0,showCurrentPositionText:!0});J(this,"drawStar",{svgPath:"M 0 -1 L 0.588 0.809 L -0.951 -0.309 L 0.951 -0.309 L -0.588 0.809 Z",drawStar:!1,starDiameter:14,starPositionY:0,scaleToSmallStar:!1});J(this,"securityPattern",{openSecurityPattern:!0,securityPatternWidth:.15,securityPatternLength:3,securityPatternCount:5,securityPatternAngleRange:40,securityPatternParams:[]});J(this,"company",{companyName:"印章绘制有限责任公司",compression:1,borderOffset:1,textDistributionFactor:5,fontFamily:"SimSun",fontHeight:4.2,fontWeight:"normal",shape:"ellipse",adjustEllipseText:!1,adjustEllipseTextFactor:.5,startAngle:0,rotateDirection:"counterclockwise"});J(this,"taxNumber",{code:"000000000000000000",compression:.7,fontHeight:3.7,fontFamily:"Arial",fontWidth:1.3,letterSpacing:8,positionY:0,totalWidth:26,fontWeight:"normal"});J(this,"stampCode",{code:"1234567890",compression:1,fontHeight:1.2,fontFamily:"Arial",borderOffset:1,fontWidth:1.2,textDistributionFactor:50,fontWeight:"normal"});J(this,"stampType",{stampType:"印章类型",fontHeight:4.6,fontFamily:"Arial",fontWidth:3,compression:.75,letterSpacing:0,positionY:-3,fontWeight:"normal",lineSpacing:2});J(this,"agingEffect",{applyAging:!1,agingIntensity:50,agingEffectParams:[]});J(this,"outBorder",{drawInnerCircle:!0,innerCircleLineWidth:1,innerCircleLineRadiusX:20,innerCircleLineRadiusY:15});J(this,"innerCircle",{drawInnerCircle:!0,innerCircleLineWidth:.5,innerCircleLineRadiusX:16,innerCircleLineRadiusY:12});J(this,"outThinCircle",{drawInnerCircle:!0,innerCircleLineWidth:.2,innerCircleLineRadiusX:36,innerCircleLineRadiusY:27});J(this,"roughEdge",{drawRoughEdge:!0,roughEdgeWidth:.2,roughEdgeHeight:5,roughEdgeParams:[],roughEdgeProbability:.3,roughEdgeShift:8,roughEdgePoints:360});J(this,"stampTypeList",[{stampType:"印章类型",fontHeight:4.6,fontFamily:"Arial",fontWidth:3,compression:.75,letterSpacing:0,positionY:-3,fontWeight:"normal",lineSpacing:2}]);J(this,"companyList",[{companyName:"绘制印章有限责任公司",compression:1,borderOffset:1,textDistributionFactor:3,fontFamily:"SimSun",fontHeight:4.2,fontWeight:"normal",shape:"ellipse",adjustEllipseText:!0,adjustEllipseTextFactor:.5,startAngle:0,rotateDirection:"counterclockwise"}]);J(this,"innerCircleList",[]);J(this,"imageList",[]);J(this,"drawStampConfigs",{roughEdge:this.roughEdge,ruler:this.ruler,drawStar:this.drawStar,securityPattern:this.securityPattern,company:this.company,stampCode:this.stampCode,width:40,height:30,stampType:this.stampType,primaryColor:this.primaryColor,borderWidth:1,refreshSecurityPattern:!1,refreshOld:!1,taxNumber:this.taxNumber,agingEffect:this.agingEffect,innerCircle:this.innerCircle,outThinCircle:this.outThinCircle,openManualAging:!1,stampTypeList:this.stampTypeList,companyList:this.companyList,innerCircleList:this.innerCircleList,imageList:this.imageList,scale:1,offsetX:0,offsetY:0,mmToPixel:0,outBorder:this.outBorder})}initDrawStampConfigs(){return this.drawStampConfigs}}class p1{constructor(t,s){J(this,"imageCanvas");J(this,"imageCtx");this.imageCanvas=document.createElement("canvas"),this.imageCanvas.width=t,this.imageCanvas.height=s;const n=this.imageCanvas.getContext("2d");if(!n)throw new Error("Failed to get image canvas context");this.imageCtx=n}async drawImage(t,s,n,i,o){this.imageCtx.clearRect(0,0,this.imageCanvas.width,this.imageCanvas.height);const a=document.createElement("div");a.innerHTML=t;const l=a.querySelector("svg");if(!l)throw new Error("Invalid SVG content");l.hasAttribute("width")||l.setAttribute("width",i.toString()),l.hasAttribute("height")||l.setAttribute("height",o.toString());const r=new XMLSerializer().serializeToString(l),f=`data:image/svg+xml;base64,${btoa(unescape(encodeURIComponent(r)))}`,d=new Image;return await new Promise((v,E)=>{d.onload=v,d.onerror=E,d.src=f}),this.imageCtx.drawImage(d,s,n,i,o),this.imageCanvas}getCanvas(){return this.imageCanvas}clear(){this.imageCtx.clearRect(0,0,this.imageCanvas.width,this.imageCanvas.height)}}const Cs=8,Ut=8;class Na{constructor(t,s){J(this,"scale",1);J(this,"offsetX",0);J(this,"offsetY",0);J(this,"mmToPixel");J(this,"canvasCtx");J(this,"offscreenCanvas");J(this,"canvas");J(this,"stampOffsetX",0);J(this,"stampOffsetY",0);J(this,"drawStampConfigs");J(this,"imageCache",new Map);J(this,"drawCircleUtils");J(this,"drawSvgUtils");J(this,"drawCompanyUtils");J(this,"drawRulerUtils");J(this,"drawSecurityPatternUtils");J(this,"initDrawStampConfigsUtils");J(this,"imageCanvas");J(this,"isDragging",!1);J(this,"dragStartX",0);J(this,"dragStartY",0);J(this,"onMouseUp",()=>{this.isDragging=!1,this.refreshStamp(!1,!1)});J(this,"onCanvasClick",t=>{this.canvas});J(this,"onMouseLeave",t=>{this.isDragging=!1,this.refreshStamp()});J(this,"onMouseDown",t=>{this.isDragging=!0,this.dragStartX=t.clientX-this.stampOffsetX*this.mmToPixel,this.dragStartY=t.clientY-this.stampOffsetY*this.mmToPixel});J(this,"onMouseMove",t=>{if(!this.drawStampConfigs.openManualAging)if(this.isDragging){const s=(t.clientX-this.dragStartX)/this.mmToPixel,n=(t.clientY-this.dragStartY)/this.mmToPixel;this.stampOffsetX=Math.round(s*10)/10,this.stampOffsetY=Math.round(n*10)/10,this.refreshStamp()}else{const s=this.canvas.getBoundingClientRect(),n=t.clientX-s.left,i=t.clientY-s.top,o=Math.round((n-Cs*this.mmToPixel)/this.mmToPixel*10)/10,a=Math.round((i-Ut*this.mmToPixel)/this.mmToPixel*10)/10;this.refreshStamp(),this.drawStampConfigs.ruler.showCurrentPositionText&&this.drawRulerUtils.drawCurrentPositionText(this.canvasCtx,o,a,this.scale,Cs*this.mmToPixel,Ut*this.mmToPixel),this.drawStampConfigs.ruler.showCrossLine&&this.drawRulerUtils.drawPositionCrossLines(this.offscreenCanvas,this.canvas,Cs*this.mmToPixel,Ut*this.mmToPixel,n,i,this.drawStampConfigs.primaryColor)}});if(!t)throw new Error("Canvas is null");const n=t.getContext("2d");if(!n)throw new Error("Failed to get canvas context");this.initDrawStampConfigsUtils=new u1,this.drawStampConfigs=this.initDrawStampConfigsUtils.initDrawStampConfigs(),this.canvasCtx=n,this.mmToPixel=s,this.canvas=t,this.offscreenCanvas=document.createElement("canvas"),this.canvas&&this.offscreenCanvas&&(this.offscreenCanvas.width=t.width,this.offscreenCanvas.height=t.height),this.addCanvasListener(),this.initDrawUtils(),this.drawSvgUtils=new xa(s),this.imageCanvas=new p1(t.width,t.height)}initDrawUtils(){this.drawCircleUtils=new l1(this.mmToPixel),this.drawSvgUtils=new xa(this.mmToPixel),this.drawCompanyUtils=new r1(this.mmToPixel),this.drawRulerUtils=new c1(this.mmToPixel,Cs*this.mmToPixel),this.drawSecurityPatternUtils=new f1(this.mmToPixel)}getDrawConfigs(){return this.drawStampConfigs}addManualAgingEffect(t,s,n){console.log("手动做旧   1",t,s,this.drawStampConfigs.agingEffect.agingEffectParams);const i=1*this.mmToPixel,o=t-this.stampOffsetX*this.mmToPixel,a=s-this.stampOffsetY*this.mmToPixel;for(let l=0;l<10;l++)this.drawStampConfigs.agingEffect.agingEffectParams.push({x:o,y:a,noiseSize:Math.random()*3+1,noise:Math.random()*200*n,strongNoiseSize:Math.random()*5+2,strongNoise:Math.random()*250*n+5,fade:Math.random()*50*n,seed:Math.random()});this.refreshStamp(!1,!1),this.canvasCtx.save(),this.canvasCtx.globalCompositeOperation="destination-out",this.canvasCtx.beginPath(),this.canvasCtx.arc(t,s,i,0,Math.PI*2,!0),this.canvasCtx.fillStyle="rgba(255, 255, 255, 0.5)",this.canvasCtx.fill(),this.canvasCtx.restore()}setDrawConfigs(t){this.drawStampConfigs=t}addCanvasListener(){this.canvas.addEventListener("mousemove",t=>{if(this.drawStampConfigs.openManualAging&&t.buttons===1){const s=this.canvas.getBoundingClientRect(),n=t.clientX-s.left,i=t.clientY-s.top,o=this.drawStampConfigs.agingEffect.agingIntensity/100;this.addManualAgingEffect(n,i,o)}else this.onMouseMove(t)}),this.canvas.addEventListener("mouseleave",t=>{this.onMouseLeave(t)}),this.canvas.addEventListener("mousedown",t=>{if(this.onMouseDown(t),this.drawStampConfigs.openManualAging){const s=this.canvas.getBoundingClientRect(),n=t.clientX-s.left,i=t.clientY-s.top,o=this.drawStampConfigs.agingEffect.agingIntensity/100;this.addManualAgingEffect(n,i,o)}}),this.canvas.addEventListener("mouseup",t=>{this.onMouseUp()}),this.canvas.addEventListener("click",t=>{this.onCanvasClick(t)}),this.canvas.addEventListener("wheel",t=>{if(t.ctrlKey){t.preventDefault();const s=t.deltaY>0?.9:1.1;this.zoomCanvas(t.offsetX,t.offsetY,s)}})}zoomCanvas(t,s,n){const i=this.scale;this.scale*=n,this.scale=Math.max(.1,Math.min(5,this.scale)),this.offsetX=t-(t-this.offsetX)*(this.scale/i),this.offsetY=s-(s-this.offsetY)*(this.scale/i),this.refreshStamp()}async drawSvgImage(t,s,n,i){try{const l=await this.imageCanvas.drawImage(s.svgPath,n-100,i-100,200,200);t.drawImage(l,0,0)}catch(o){console.error("Error drawing SVG:",o)}}async drawImageList(t,s,n,i){for(const o of s)if(o.imageUrl){let a=this.imageCache.get(o.imageUrl);if(a)this.drawSingleImage(t,a,o,n,i);else try{const l=new Image;l.src=o.imageUrl,await new Promise((u,f)=>{l.onload=u,l.onerror=f});const r=await createImageBitmap(l);this.imageCache.set(o.imageUrl,r),this.drawSingleImage(t,r,o,n,i),requestAnimationFrame(()=>{this.refreshStamp()})}catch(l){console.error("Error loading or processing image:",l)}}}drawSingleImage(t,s,n,i,o){let a=n.imageWidth*this.mmToPixel,l=n.imageHeight*this.mmToPixel;if(n.keepAspectRatio){const f=Math.min(a/s.width,l/s.height);a=s.width*f,l=s.height*f}const r=i-a/2+n.positionX*this.mmToPixel,u=o-l/2+n.positionY*this.mmToPixel;t.save(),t.drawImage(s,r,u,a,l),t.restore()}async clearImageCache(){for(const t of this.imageCache.values())t.close();this.imageCache.clear()}drawStampType(t,s,n,i,o){const a=s.fontHeight*this.mmToPixel,l=s.letterSpacing,r=s.positionY,u=s.fontWeight||"normal",f=s.lineSpacing*this.mmToPixel;t.save(),t.font=`${u} ${a}px ${s.fontFamily}`,t.fillStyle=this.drawStampConfigs.primaryColor,t.textAlign="center",t.textBaseline="middle";const d=s.stampType.split(`
`),v=d.length;d.forEach((E,D)=>{const O=E.split(""),W=O.map(F=>t.measureText(F).width),b=W.reduce((F,H)=>F+H,0)+(O.length-1)*l*this.mmToPixel,z=(D-(v-1)/2)*(a+f),P=i+o*.5+r*this.mmToPixel+z;t.save(),t.translate(n,P);let T=-b/2;t.scale(s.compression,1),O.forEach((F,H)=>{t.fillText(F,T+W[H]/2,0),T+=W[H]+l*this.mmToPixel}),t.restore()}),t.restore()}drawStampTypeList(t,s,n,i,o){s.forEach(a=>{this.drawStampType(t,a,n,i,o)}),t.restore()}drawEllipse(t,s,n,i,o,a,l){t.beginPath(),t.ellipse(s,n,i,o,0,0,Math.PI*2),t.strokeStyle=l,t.lineWidth=a,t.stroke()}drawCode(t,s,n,i,o,a){const l=s.fontHeight*this.mmToPixel,r=s.code,u=s.fontWeight||"normal";t.save(),t.font=`${u} ${l}px ${s.fontFamily}`,t.fillStyle=this.drawStampConfigs.primaryColor,t.textAlign="center",t.textBaseline="middle";const f=r.split(""),d=f.length;if(d===1){const v=n,E=i+a-l-s.borderOffset*this.mmToPixel;t.save(),t.translate(v,E),t.scale(s.compression,1),t.fillText(r,0,0),t.restore()}else{const v=Math.PI*((1+d)/s.textDistributionFactor),E=Math.PI/2+v/2,D=v/(d-1);f.forEach((O,W)=>{const b=E-D*W,z=n+Math.cos(b)*(o-l/2-s.borderOffset*this.mmToPixel),P=i+Math.sin(b)*(a-l/2-s.borderOffset*this.mmToPixel);t.save(),t.translate(z,P),t.rotate(b-Math.PI/2),t.scale(s.compression,1),t.fillText(O,0,0),t.restore()})}t.restore()}drawTaxNumber(t,s,n,i){const o=s.fontHeight*this.mmToPixel,a=s.totalWidth*this.mmToPixel,l=s.positionY*this.mmToPixel+.3,r=s.fontWeight||"normal";t.save(),t.font=`${r} ${o}px ${s.fontFamily}`,t.fillStyle=this.drawStampConfigs.primaryColor,t.textAlign="center",t.textBaseline="middle";const u=s.code.split(""),f=u.length,d=this.drawStampConfigs.taxNumber.letterSpacing*this.mmToPixel,E=(a*this.drawStampConfigs.taxNumber.compression-(f-1)*d)/f,D=f*E+(f-1)*d,O=n-D/2+E/2,W=i+l*this.mmToPixel;u.forEach((b,z)=>{const P=O+z*(E+d);t.save(),t.translate(P,W),t.scale(this.drawStampConfigs.taxNumber.compression,1.35),t.fillText(b,0,0),t.restore()}),t.restore()}addRoughEdge(t,s,n,i,o,a,l=!1){const r=a*this.drawStampConfigs.roughEdge.roughEdgeHeight*.01,u=this.drawStampConfigs.roughEdge.roughEdgePoints,f=this.drawStampConfigs.roughEdge.roughEdgeShift;if(t.save(),t.fillStyle="white",t.globalCompositeOperation="destination-out",l||this.drawStampConfigs.roughEdge.roughEdgeParams.length===0){this.drawStampConfigs.roughEdge.roughEdgeParams=[];for(let d=0;d<u;d++){const v=d/u*Math.PI*2,D=Math.random()>this.drawStampConfigs.roughEdge.roughEdgeProbability?Math.random()*r*Math.random()+this.drawStampConfigs.roughEdge.roughEdgeWidth:0;this.drawStampConfigs.roughEdge.roughEdgeParams.push({angle:v,size:D,offset:f,opacity:1})}}this.drawStampConfigs.roughEdge.roughEdgeParams.forEach(({angle:d,size:v})=>{const E=s+Math.cos(d)*(i+f),D=n+Math.sin(d)*(o+f);v>0&&(t.beginPath(),t.arc(E,D,v*this.mmToPixel,0,Math.PI*2),t.fill())}),t.restore()}addAgingEffect(t,s,n,i=!1){if(console.log("addAgingEffect","width",s,"height",n,"forceRefresh",this.drawStampConfigs.agingEffect.applyAging),!this.drawStampConfigs.agingEffect.applyAging)return;const o=t.getImageData(0,0,s,n),a=o.data,l=s/(2*this.scale)+this.stampOffsetX*this.mmToPixel/this.scale,r=n/(2*this.scale)+this.stampOffsetY*this.mmToPixel/this.scale,u=Math.max(s,n)/2*this.mmToPixel/this.scale;if(i||this.drawStampConfigs.agingEffect.agingEffectParams.length===0){this.drawStampConfigs.agingEffect.agingEffectParams=[];for(let f=0;f<n;f++)for(let d=0;d<s;d++){const v=(f*s+d)*4;if(Math.sqrt(Math.pow(d-l,2)+Math.pow(f-r,2))<=u&&a[v+3]>0){const D=this.drawStampConfigs.agingEffect.agingIntensity/100,O=Math.random();this.drawStampConfigs.agingEffect.agingEffectParams.push({x:d-this.stampOffsetX*this.mmToPixel,y:f-this.stampOffsetY*this.mmToPixel,noiseSize:Math.random()*3+1,noise:Math.random()*200*D,strongNoiseSize:Math.random()*5+2,strongNoise:Math.random()*250*D+5,fade:Math.random()*50*D,seed:O})}}}this.drawStampConfigs.agingEffect.agingEffectParams.forEach(f=>{const{x:d,y:v,noiseSize:E,noise:D,strongNoiseSize:O,strongNoise:W,fade:b,seed:z}=f,P=d+this.stampOffsetX*this.mmToPixel,T=v+this.stampOffsetY*this.mmToPixel,F=(Math.round(T)*s+Math.round(P))*4;z<.4&&this.addCircularNoise(a,s,P,T,E,D,!0),z<.05&&this.addCircularNoise(a,s,P,T,O,W,!0),z<.2&&(a[F+3]=Math.max(0,a[F+3]-b))}),t.putImageData(o,0,0)}addCircularNoise(t,s,n,i,o,a,l=!1){const r=o*o/4;for(let u=-o/2;u<o/2;u++)for(let f=-o/2;f<o/2;f++)if(f*f+u*u<=r){const d=Math.round(n+f),E=(Math.round(i+u)*s+d)*4;E>=0&&E<t.length&&(l?t[E+3]=Math.max(0,t[E+3]-a):(t[E]=Math.min(255,t[E]+a),t[E+1]=Math.min(255,t[E+1]+a),t[E+2]=Math.min(255,t[E+2]+a)))}}saveStampAsPNG(){let t=1,s=Math.max(this.drawStampConfigs.width,this.drawStampConfigs.height),n=(this.drawStampConfigs.width+t*2)*this.mmToPixel,i=(this.drawStampConfigs.height+t*2)*this.mmToPixel;(s+t)*this.mmToPixel,this.drawStampConfigs.ruler.showCrossLine=!1,this.drawStampConfigs.ruler.showRuler=!1,this.drawStampConfigs.ruler.showDashLine=!1,this.drawStampConfigs.ruler.showSideRuler=!1,this.drawStampConfigs.ruler.showFullRuler=!1,this.drawStampConfigs.ruler.showCurrentPositionText=!1,this.refreshStamp(),setTimeout(()=>{const o=document.createElement("canvas");o.width=n,o.height=i;const a=o.getContext("2d");if(!a)return;a.clearRect(0,0,n,i),a.drawImage(this.canvas,Cs*this.mmToPixel+this.stampOffsetX*this.mmToPixel,Ut*this.mmToPixel+this.stampOffsetY*this.mmToPixel,n,i,t*this.mmToPixel,t*this.mmToPixel,n,i),this.drawStampConfigs.agingEffect.applyAging&&this.addAgingEffect(a,n,i,!1);const l=o.toDataURL("image/png"),r=document.createElement("a");r.href=l,r.download="mystamp.png",document.body.appendChild(r),r.click(),document.body.removeChild(r),this.drawStampConfigs.ruler.showCrossLine=!0,this.drawStampConfigs.ruler.showRuler=!0,this.drawStampConfigs.ruler.showDashLine=!0,this.drawStampConfigs.ruler.showSideRuler=!0,this.drawStampConfigs.ruler.showFullRuler=!0,this.drawStampConfigs.ruler.showCurrentPositionText=!0,this.refreshStamp()},50)}refreshStamp(t=!1,s=!1,n=!1){this.canvasCtx.clearRect(0,0,this.canvas.width,this.canvas.height),this.canvasCtx.save(),this.canvasCtx.translate(this.offsetX,this.offsetY),this.canvasCtx.scale(this.scale,this.scale);const i=this.drawStampConfigs.width/2*10+Cs*this.mmToPixel,o=this.drawStampConfigs.height/2*10+Ut*this.mmToPixel,a=i/this.scale,l=o/this.scale,r=this.mmToPixel,u=(this.drawStampConfigs.width-this.drawStampConfigs.outBorder.innerCircleLineWidth)/2,f=(this.drawStampConfigs.height-this.drawStampConfigs.outBorder.innerCircleLineWidth)/2,d=this.stampOffsetX*this.mmToPixel,v=this.stampOffsetY*this.mmToPixel,E=a+d,D=l+v;this.drawStamp(this.canvasCtx,E,D,u*r,f*r,this.drawStampConfigs.primaryColor,t,s,n),this.canvasCtx.restore(),this.drawStampConfigs.ruler.showRuler&&(this.drawStampConfigs.ruler.showSideRuler&&(this.drawRulerUtils.drawRuler(this.canvasCtx,this.drawStampConfigs.ruler,this.scale,this.canvas.width,Ut*this.mmToPixel,!0),this.drawRulerUtils.drawRuler(this.canvasCtx,this.drawStampConfigs.ruler,this.scale,this.canvas.height,Ut*this.mmToPixel,!1)),this.drawStampConfigs.ruler.showDashLine&&this.drawRulerUtils.showCrossDashLine(this.canvasCtx,this.drawStampConfigs.ruler,this.scale,Ut,Ut,this.canvas.width,this.canvas.height))}resetZoom(){this.scale=1,this.offsetX=0,this.offsetY=0,this.refreshStamp()}drawStamp(t,s,n,i,o,a,l=!1,r=!1,u=!1){t.clearRect(0,0,this.canvas.width,this.canvas.height);const f=this.offscreenCanvas;f.width=this.canvas.width,f.height=this.canvas.height;const d=f.getContext("2d");if(!d)return;const v=document.createElement("canvas");v.width=this.canvas.width,v.height=this.canvas.height,v.getContext("2d")&&(this.drawStampConfigs.outBorder.drawInnerCircle&&a1(d,s,n,i,o,this.drawStampConfigs.outBorder.innerCircleLineWidth*this.mmToPixel,a),d.save(),d.beginPath(),d.ellipse(s,n,i,o,0,0,Math.PI*2),d.clip(),this.drawStampConfigs.innerCircleList.length>0&&this.drawCircleUtils.drawCircleList(d,this.drawStampConfigs.innerCircleList,s,n,a),this.drawStampConfigs.drawStar.drawStar&&this.drawSvgUtils.drawStarShape(d,this.drawStampConfigs.drawStar,s,n,this.drawStampConfigs.primaryColor),this.drawStampConfigs.imageList&&this.drawStampConfigs.imageList.length>0&&this.drawImageList(d,this.drawStampConfigs.imageList,s,n),this.drawCompanyUtils.drawCompanyList(d,this.drawStampConfigs.companyList,s,n,i,o,this.drawStampConfigs.primaryColor),this.drawStampTypeList(d,this.drawStampConfigs.stampTypeList,s,n,i),this.drawCode(d,this.drawStampConfigs.stampCode,s,n,i,o),this.drawTaxNumber(d,this.drawStampConfigs.taxNumber,s,n),d.restore(),t.save(),this.drawStampConfigs.roughEdge.drawRoughEdge&&this.addRoughEdge(d,s,n,i,o,this.drawStampConfigs.outBorder.innerCircleLineWidth*this.mmToPixel,u),this.drawStampConfigs.securityPattern.openSecurityPattern&&this.drawSecurityPatternUtils.drawSecurityPattern(d,this.drawStampConfigs.securityPattern,s,n,i,o,l),t.globalCompositeOperation="source-over",t.drawImage(f,0,0),t.restore(),this.drawStampConfigs.agingEffect.applyAging&&this.addAgingEffect(t,this.canvas.width,this.canvas.height,r))}}async function g1(){try{if(window.queryLocalFonts){const e=await window.queryLocalFonts();return[...new Set(e.map(t=>t.family))]}else return["SimSun","SimHei","Microsoft YaHei","KaiTi","FangSong","STHeiti","STKaiti","STSong","STFangsong","LiSu","YouYuan","STZhongsong","STXihei","Arial","Times New Roman","Helvetica"]}catch(e){return console.error("获取系统字体失败:",e),["SimSun","SimHei","Microsoft YaHei","KaiTi"]}}const d1={drawRoughEdge:!1,roughEdgeWidth:.2,roughEdgeHeight:5,roughEdgeParams:[{angle:0,size:.5032563701178842,offset:8,opacity:1},{angle:.017453292519943295,size:.3379166289700789,offset:8,opacity:1},{angle:.03490658503988659,size:.49850828105362677,offset:8,opacity:1},{angle:.05235987755982988,size:0,offset:8,opacity:1},{angle:.06981317007977318,size:.33027000340902746,offset:8,opacity:1},{angle:.08726646259971647,size:0,offset:8,opacity:1},{angle:.10471975511965977,size:.47393696973712246,offset:8,opacity:1},{angle:.12217304763960307,size:.6350416726108645,offset:8,opacity:1},{angle:.13962634015954636,size:.42238834211115167,offset:8,opacity:1},{angle:.15707963267948966,size:.2486073483438663,offset:8,opacity:1},{angle:.17453292519943295,size:0,offset:8,opacity:1},{angle:.19198621771937624,size:.35123274555265016,offset:8,opacity:1},{angle:.20943951023931953,size:.22448742164649077,offset:8,opacity:1},{angle:.22689280275926282,size:.4586736104699405,offset:8,opacity:1},{angle:.24434609527920614,size:.3043430828713793,offset:8,opacity:1},{angle:.2617993877991494,size:.3678224039628227,offset:8,opacity:1},{angle:.2792526803190927,size:.6163525747317525,offset:8,opacity:1},{angle:.296705972839036,size:.4363211517667617,offset:8,opacity:1},{angle:.3141592653589793,size:.205723548131973,offset:8,opacity:1},{angle:.33161255787892263,size:.4441622976512367,offset:8,opacity:1},{angle:.3490658503988659,size:0,offset:8,opacity:1},{angle:.3665191429188092,size:0,offset:8,opacity:1},{angle:.3839724354387525,size:.43017918657846765,offset:8,opacity:1},{angle:.40142572795869574,size:.4300022134137814,offset:8,opacity:1},{angle:.41887902047863906,size:0,offset:8,opacity:1},{angle:.4363323129985824,size:.25786098537584295,offset:8,opacity:1},{angle:.45378560551852565,size:.26525313850692,offset:8,opacity:1},{angle:.47123889803846897,size:.2838264875019364,offset:8,opacity:1},{angle:.4886921905584123,size:.4487316582245055,offset:8,opacity:1},{angle:.5061454830783556,size:.22112605086657722,offset:8,opacity:1},{angle:.5235987755982988,size:.4294648804576925,offset:8,opacity:1},{angle:.5410520681182421,size:.2181025744046031,offset:8,opacity:1},{angle:.5585053606381855,size:.20053237605486948,offset:8,opacity:1},{angle:.5759586531581287,size:0,offset:8,opacity:1},{angle:.593411945678072,size:0,offset:8,opacity:1},{angle:.6108652381980153,size:.20041438725089644,offset:8,opacity:1},{angle:.6283185307179586,size:.2609460635118062,offset:8,opacity:1},{angle:.6457718232379019,size:.39197138754198557,offset:8,opacity:1},{angle:.6632251157578453,size:.2605111967121892,offset:8,opacity:1},{angle:.6806784082777886,size:.20565487747739025,offset:8,opacity:1},{angle:.6981317007977318,size:.28654344221403566,offset:8,opacity:1},{angle:.7155849933176751,size:0,offset:8,opacity:1},{angle:.7330382858376184,size:.4584907994244955,offset:8,opacity:1},{angle:.7504915783575618,size:0,offset:8,opacity:1},{angle:.767944870877505,size:0,offset:8,opacity:1},{angle:.7853981633974483,size:0,offset:8,opacity:1},{angle:.8028514559173915,size:.32662457556616653,offset:8,opacity:1},{angle:.8203047484373349,size:.3119138674864993,offset:8,opacity:1},{angle:.8377580409572781,size:.4433990641657177,offset:8,opacity:1},{angle:.8552113334772213,size:.2123628886068149,offset:8,opacity:1},{angle:.8726646259971648,size:0,offset:8,opacity:1},{angle:.890117918517108,size:.34950148564089184,offset:8,opacity:1},{angle:.9075712110370513,size:0,offset:8,opacity:1},{angle:.9250245035569946,size:.20120500318879,offset:8,opacity:1},{angle:.9424777960769379,size:0,offset:8,opacity:1},{angle:.9599310885968813,size:.5188164082336375,offset:8,opacity:1},{angle:.9773843811168246,size:0,offset:8,opacity:1},{angle:.9948376736367678,size:.22172251375465574,offset:8,opacity:1},{angle:1.0122909661567112,size:.2432685121130981,offset:8,opacity:1},{angle:1.0297442586766543,size:.2320690651003732,offset:8,opacity:1},{angle:1.0471975511965976,size:.3907006251295331,offset:8,opacity:1},{angle:1.064650843716541,size:0,offset:8,opacity:1},{angle:1.0821041362364843,size:.2625156540222333,offset:8,opacity:1},{angle:1.0995574287564276,size:0,offset:8,opacity:1},{angle:1.117010721276371,size:.3145596222093862,offset:8,opacity:1},{angle:1.1344640137963142,size:.32936787676169677,offset:8,opacity:1},{angle:1.1519173063162573,size:.2857231314976785,offset:8,opacity:1},{angle:1.1693705988362009,size:.20120534399793935,offset:8,opacity:1},{angle:1.186823891356144,size:0,offset:8,opacity:1},{angle:1.2042771838760875,size:.23237394092882147,offset:8,opacity:1},{angle:1.2217304763960306,size:.28398641365511185,offset:8,opacity:1},{angle:1.239183768915974,size:.21502333555596864,offset:8,opacity:1},{angle:1.2566370614359172,size:.3499693244354978,offset:8,opacity:1},{angle:1.2740903539558606,size:0,offset:8,opacity:1},{angle:1.2915436464758039,size:.5105882933252887,offset:8,opacity:1},{angle:1.3089969389957472,size:.21784406246195218,offset:8,opacity:1},{angle:1.3264502315156905,size:.22514376311106876,offset:8,opacity:1},{angle:1.3439035240356336,size:.29704092249825453,offset:8,opacity:1},{angle:1.3613568165555772,size:.4521472353131752,offset:8,opacity:1},{angle:1.3788101090755203,size:.21507145749905754,offset:8,opacity:1},{angle:1.3962634015954636,size:.21682236241700453,offset:8,opacity:1},{angle:1.413716694115407,size:.22356961113236007,offset:8,opacity:1},{angle:1.4311699866353502,size:.22219417312865522,offset:8,opacity:1},{angle:1.4486232791552935,size:.2977119909206255,offset:8,opacity:1},{angle:1.4660765716752369,size:.38291243837511746,offset:8,opacity:1},{angle:1.48352986419518,size:.3116663219443704,offset:8,opacity:1},{angle:1.5009831567151235,size:0,offset:8,opacity:1},{angle:1.5184364492350666,size:.25492313554632756,offset:8,opacity:1},{angle:1.53588974175501,size:.2228509582782908,offset:8,opacity:1},{angle:1.5533430342749532,size:.35672171117898743,offset:8,opacity:1},{angle:1.5707963267948966,size:0,offset:8,opacity:1},{angle:1.5882496193148399,size:0,offset:8,opacity:1},{angle:1.605702911834783,size:.4388252813562349,offset:8,opacity:1},{angle:1.6231562043547265,size:.2273036372658915,offset:8,opacity:1},{angle:1.6406094968746698,size:.21718818137496743,offset:8,opacity:1},{angle:1.6580627893946132,size:.36941527530149404,offset:8,opacity:1},{angle:1.6755160819145563,size:.21623671572399,offset:8,opacity:1},{angle:1.6929693744344996,size:.636688937830729,offset:8,opacity:1},{angle:1.7104226669544427,size:0,offset:8,opacity:1},{angle:1.7278759594743864,size:0,offset:8,opacity:1},{angle:1.7453292519943295,size:.27728150462159734,offset:8,opacity:1},{angle:1.7627825445142729,size:0,offset:8,opacity:1},{angle:1.780235837034216,size:0,offset:8,opacity:1},{angle:1.7976891295541593,size:.23328034069543777,offset:8,opacity:1},{angle:1.8151424220741026,size:0,offset:8,opacity:1},{angle:1.8325957145940461,size:.2586898150005329,offset:8,opacity:1},{angle:1.8500490071139892,size:.4994559385312126,offset:8,opacity:1},{angle:1.8675022996339325,size:.26421680867532127,offset:8,opacity:1},{angle:1.8849555921538759,size:.3209021989338088,offset:8,opacity:1},{angle:1.902408884673819,size:0,offset:8,opacity:1},{angle:1.9198621771937625,size:0,offset:8,opacity:1},{angle:1.9373154697137058,size:.26002544806143374,offset:8,opacity:1},{angle:1.9547687622336491,size:.2924936993062236,offset:8,opacity:1},{angle:1.9722220547535922,size:.43140240061138796,offset:8,opacity:1},{angle:1.9896753472735356,size:.29591579647411836,offset:8,opacity:1},{angle:2.007128639793479,size:.46532747343985814,offset:8,opacity:1},{angle:2.0245819323134224,size:0,offset:8,opacity:1},{angle:2.0420352248333655,size:.37989836106928254,offset:8,opacity:1},{angle:2.0594885173533086,size:.43824671847111324,offset:8,opacity:1},{angle:2.076941809873252,size:.21491306461629336,offset:8,opacity:1},{angle:2.0943951023931953,size:.2576066045616476,offset:8,opacity:1},{angle:2.111848394913139,size:.20559969896825836,offset:8,opacity:1},{angle:2.129301687433082,size:.5452053035796387,offset:8,opacity:1},{angle:2.1467549799530254,size:.4317948579735969,offset:8,opacity:1},{angle:2.1642082724729685,size:.2926508010599716,offset:8,opacity:1},{angle:2.1816615649929116,size:.37646244630618103,offset:8,opacity:1},{angle:2.199114857512855,size:.5182160912889464,offset:8,opacity:1},{angle:2.2165681500327987,size:.5838728943805604,offset:8,opacity:1},{angle:2.234021442552742,size:.21844249399465382,offset:8,opacity:1},{angle:2.251474735072685,size:0,offset:8,opacity:1},{angle:2.2689280275926285,size:.3721009993624145,offset:8,opacity:1},{angle:2.2863813201125716,size:0,offset:8,opacity:1},{angle:2.3038346126325147,size:.24598938578156437,offset:8,opacity:1},{angle:2.321287905152458,size:.4507505938631045,offset:8,opacity:1},{angle:2.3387411976724017,size:.25469635650569583,offset:8,opacity:1},{angle:2.356194490192345,size:.30528741051655217,offset:8,opacity:1},{angle:2.373647782712288,size:.37088412070072785,offset:8,opacity:1},{angle:2.3911010752322315,size:.24486197147462863,offset:8,opacity:1},{angle:2.408554367752175,size:0,offset:8,opacity:1},{angle:2.426007660272118,size:.43989471208136854,offset:8,opacity:1},{angle:2.443460952792061,size:.33696542573155486,offset:8,opacity:1},{angle:2.4609142453120048,size:0,offset:8,opacity:1},{angle:2.478367537831948,size:0,offset:8,opacity:1},{angle:2.495820830351891,size:0,offset:8,opacity:1},{angle:2.5132741228718345,size:.2505063689411901,offset:8,opacity:1},{angle:2.530727415391778,size:.31438011396387455,offset:8,opacity:1},{angle:2.548180707911721,size:.34374426546984016,offset:8,opacity:1},{angle:2.5656340004316642,size:.2305610481543743,offset:8,opacity:1},{angle:2.5830872929516078,size:.37268657957858453,offset:8,opacity:1},{angle:2.600540585471551,size:.25219421624230426,offset:8,opacity:1},{angle:2.6179938779914944,size:0,offset:8,opacity:1},{angle:2.6354471705114375,size:.23021680363052838,offset:8,opacity:1},{angle:2.652900463031381,size:.3483359449322281,offset:8,opacity:1},{angle:2.670353755551324,size:.3251780474107786,offset:8,opacity:1},{angle:2.6878070480712672,size:0,offset:8,opacity:1},{angle:2.705260340591211,size:.3687577362310519,offset:8,opacity:1},{angle:2.7227136331111543,size:.5694461018098402,offset:8,opacity:1},{angle:2.7401669256310974,size:.22425733709526832,offset:8,opacity:1},{angle:2.7576202181510405,size:0,offset:8,opacity:1},{angle:2.7750735106709836,size:.32709123488963454,offset:8,opacity:1},{angle:2.792526803190927,size:.3643797544475289,offset:8,opacity:1},{angle:2.8099800957108707,size:.25481296814968835,offset:8,opacity:1},{angle:2.827433388230814,size:.20233815801319835,offset:8,opacity:1},{angle:2.844886680750757,size:.22289439004543232,offset:8,opacity:1},{angle:2.8623399732707004,size:0,offset:8,opacity:1},{angle:2.8797932657906435,size:0,offset:8,opacity:1},{angle:2.897246558310587,size:0,offset:8,opacity:1},{angle:2.91469985083053,size:.22662095056906018,offset:8,opacity:1},{angle:2.9321531433504737,size:.6177358599983805,offset:8,opacity:1},{angle:2.949606435870417,size:.5579600289881892,offset:8,opacity:1},{angle:2.96705972839036,size:.24919239443796898,offset:8,opacity:1},{angle:2.9845130209103035,size:0,offset:8,opacity:1},{angle:3.001966313430247,size:.20285345071151972,offset:8,opacity:1},{angle:3.01941960595019,size:.29633213437720063,offset:8,opacity:1},{angle:3.036872898470133,size:.31615740448077223,offset:8,opacity:1},{angle:3.0543261909900767,size:.4883995719883713,offset:8,opacity:1},{angle:3.07177948351002,size:.2500925025911332,offset:8,opacity:1},{angle:3.089232776029963,size:.262931178068741,offset:8,opacity:1},{angle:3.1066860685499065,size:.3135512137978654,offset:8,opacity:1},{angle:3.12413936106985,size:.31083588965839803,offset:8,opacity:1},{angle:3.141592653589793,size:0,offset:8,opacity:1},{angle:3.159045946109736,size:.24804439339468767,offset:8,opacity:1},{angle:3.1764992386296798,size:0,offset:8,opacity:1},{angle:3.193952531149623,size:.2571395452468249,offset:8,opacity:1},{angle:3.211405823669566,size:.6279202198461746,offset:8,opacity:1},{angle:3.2288591161895095,size:.24288586668685336,offset:8,opacity:1},{angle:3.246312408709453,size:.34718500726687895,offset:8,opacity:1},{angle:3.2637657012293966,size:.47061815108690846,offset:8,opacity:1},{angle:3.2812189937493397,size:0,offset:8,opacity:1},{angle:3.2986722862692828,size:.4866126208626991,offset:8,opacity:1},{angle:3.3161255787892263,size:0,offset:8,opacity:1},{angle:3.3335788713091694,size:0,offset:8,opacity:1},{angle:3.3510321638291125,size:.22976093136313686,offset:8,opacity:1},{angle:3.368485456349056,size:0,offset:8,opacity:1},{angle:3.385938748868999,size:0,offset:8,opacity:1},{angle:3.4033920413889422,size:.4397259724357342,offset:8,opacity:1},{angle:3.4208453339088853,size:.24667841492062575,offset:8,opacity:1},{angle:3.4382986264288293,size:.43391379529064145,offset:8,opacity:1},{angle:3.455751918948773,size:0,offset:8,opacity:1},{angle:3.473205211468716,size:0,offset:8,opacity:1},{angle:3.490658503988659,size:.21961175000891414,offset:8,opacity:1},{angle:3.5081117965086026,size:.36890033272657746,offset:8,opacity:1},{angle:3.5255650890285457,size:.4326945036689108,offset:8,opacity:1},{angle:3.543018381548489,size:.3268470087082487,offset:8,opacity:1},{angle:3.560471674068432,size:.20854219238334568,offset:8,opacity:1},{angle:3.5779249665883754,size:.2423254936922336,offset:8,opacity:1},{angle:3.5953782591083185,size:.4064744910955269,offset:8,opacity:1},{angle:3.6128315516282616,size:0,offset:8,opacity:1},{angle:3.630284844148205,size:.31287388031447483,offset:8,opacity:1},{angle:3.647738136668149,size:.3948160804791036,offset:8,opacity:1},{angle:3.6651914291880923,size:0,offset:8,opacity:1},{angle:3.6826447217080354,size:.2026956152601606,offset:8,opacity:1},{angle:3.7000980142279785,size:0,offset:8,opacity:1},{angle:3.717551306747922,size:.44054519647144813,offset:8,opacity:1},{angle:3.735004599267865,size:.4130009051490618,offset:8,opacity:1},{angle:3.752457891787808,size:0,offset:8,opacity:1},{angle:3.7699111843077517,size:.49311184417141246,offset:8,opacity:1},{angle:3.787364476827695,size:.45508968556466084,offset:8,opacity:1},{angle:3.804817769347638,size:0,offset:8,opacity:1},{angle:3.8222710618675815,size:.48005037780936805,offset:8,opacity:1},{angle:3.839724354387525,size:0,offset:8,opacity:1},{angle:3.8571776469074686,size:.4561164475672816,offset:8,opacity:1},{angle:3.8746309394274117,size:.3002000652009621,offset:8,opacity:1},{angle:3.8920842319473548,size:.3336541884527151,offset:8,opacity:1},{angle:3.9095375244672983,size:0,offset:8,opacity:1},{angle:3.9269908169872414,size:.4811384274609927,offset:8,opacity:1},{angle:3.9444441095071845,size:0,offset:8,opacity:1},{angle:3.961897402027128,size:.5102504861379599,offset:8,opacity:1},{angle:3.979350694547071,size:.23214101697543765,offset:8,opacity:1},{angle:3.9968039870670142,size:.29156434123379016,offset:8,opacity:1},{angle:4.014257279586958,size:0,offset:8,opacity:1},{angle:4.031710572106902,size:.20004717274584805,offset:8,opacity:1},{angle:4.049163864626845,size:.3614701974794731,offset:8,opacity:1},{angle:4.066617157146788,size:0,offset:8,opacity:1},{angle:4.084070449666731,size:.2219991701875027,offset:8,opacity:1},{angle:4.101523742186674,size:.22431067260162876,offset:8,opacity:1},{angle:4.118977034706617,size:.30926005945781443,offset:8,opacity:1},{angle:4.136430327226561,size:.2513871895964691,offset:8,opacity:1},{angle:4.153883619746504,size:.2659224601694766,offset:8,opacity:1},{angle:4.171336912266447,size:.2216658406470619,offset:8,opacity:1},{angle:4.1887902047863905,size:.4273566387084955,offset:8,opacity:1},{angle:4.206243497306334,size:.34036553217806864,offset:8,opacity:1},{angle:4.223696789826278,size:.32533678816516465,offset:8,opacity:1},{angle:4.241150082346221,size:.23594875228493112,offset:8,opacity:1},{angle:4.258603374866164,size:0,offset:8,opacity:1},{angle:4.276056667386108,size:0,offset:8,opacity:1},{angle:4.293509959906051,size:.31051254932537203,offset:8,opacity:1},{angle:4.310963252425994,size:0,offset:8,opacity:1},{angle:4.328416544945937,size:0,offset:8,opacity:1},{angle:4.34586983746588,size:0,offset:8,opacity:1},{angle:4.363323129985823,size:.41758554970783024,offset:8,opacity:1},{angle:4.380776422505767,size:0,offset:8,opacity:1},{angle:4.39822971502571,size:.5165181485571547,offset:8,opacity:1},{angle:4.4156830075456535,size:0,offset:8,opacity:1},{angle:4.4331363000655974,size:0,offset:8,opacity:1},{angle:4.4505895925855405,size:.2875659335408256,offset:8,opacity:1},{angle:4.468042885105484,size:.2630697344221547,offset:8,opacity:1},{angle:4.485496177625427,size:.3044067435482997,offset:8,opacity:1},{angle:4.50294947014537,size:.3224161157725142,offset:8,opacity:1},{angle:4.520402762665314,size:.29296045447988417,offset:8,opacity:1},{angle:4.537856055185257,size:0,offset:8,opacity:1},{angle:4.5553093477052,size:.5348621754537155,offset:8,opacity:1},{angle:4.572762640225143,size:.23724437888510921,offset:8,opacity:1},{angle:4.590215932745086,size:0,offset:8,opacity:1},{angle:4.607669225265029,size:.23471290119278718,offset:8,opacity:1},{angle:4.625122517784973,size:.23001169467314136,offset:8,opacity:1},{angle:4.642575810304916,size:.3381947981140539,offset:8,opacity:1},{angle:4.66002910282486,size:0,offset:8,opacity:1},{angle:4.6774823953448035,size:.2820194526396866,offset:8,opacity:1},{angle:4.694935687864747,size:.27589340223228825,offset:8,opacity:1},{angle:4.71238898038469,size:0,offset:8,opacity:1},{angle:4.729842272904633,size:0,offset:8,opacity:1},{angle:4.747295565424576,size:0,offset:8,opacity:1},{angle:4.764748857944519,size:0,offset:8,opacity:1},{angle:4.782202150464463,size:.6047061570324029,offset:8,opacity:1},{angle:4.799655442984406,size:0,offset:8,opacity:1},{angle:4.81710873550435,size:0,offset:8,opacity:1},{angle:4.834562028024293,size:.4385393337320528,offset:8,opacity:1},{angle:4.852015320544236,size:0,offset:8,opacity:1},{angle:4.869468613064179,size:.3738861085343654,offset:8,opacity:1},{angle:4.886921905584122,size:.2369083255375507,offset:8,opacity:1},{angle:4.9043751981040655,size:.20042730995532476,offset:8,opacity:1},{angle:4.9218284906240095,size:.27407857191507573,offset:8,opacity:1},{angle:4.939281783143953,size:.260307602595212,offset:8,opacity:1},{angle:4.956735075663896,size:.22693010922108042,offset:8,opacity:1},{angle:4.974188368183839,size:0,offset:8,opacity:1},{angle:4.991641660703782,size:.36404125447608293,offset:8,opacity:1},{angle:5.009094953223726,size:.36660145102645764,offset:8,opacity:1},{angle:5.026548245743669,size:.21844437325488894,offset:8,opacity:1},{angle:5.044001538263612,size:0,offset:8,opacity:1},{angle:5.061454830783556,size:0,offset:8,opacity:1},{angle:5.078908123303499,size:0,offset:8,opacity:1},{angle:5.096361415823442,size:.6203742746502853,offset:8,opacity:1},{angle:5.113814708343385,size:0,offset:8,opacity:1},{angle:5.1312680008633285,size:0,offset:8,opacity:1},{angle:5.148721293383272,size:0,offset:8,opacity:1},{angle:5.1661745859032155,size:0,offset:8,opacity:1},{angle:5.183627878423159,size:.4394142349769681,offset:8,opacity:1},{angle:5.201081170943102,size:.46186021808530575,offset:8,opacity:1},{angle:5.218534463463046,size:.29002729040435316,offset:8,opacity:1},{angle:5.235987755982989,size:0,offset:8,opacity:1},{angle:5.253441048502932,size:0,offset:8,opacity:1},{angle:5.270894341022875,size:0,offset:8,opacity:1},{angle:5.288347633542818,size:.2776981969296649,offset:8,opacity:1},{angle:5.305800926062762,size:0,offset:8,opacity:1},{angle:5.323254218582705,size:.3066397012962211,offset:8,opacity:1},{angle:5.340707511102648,size:.22441601610004672,offset:8,opacity:1},{angle:5.358160803622591,size:.25396857567416625,offset:8,opacity:1},{angle:5.3756140961425345,size:.5404051622973916,offset:8,opacity:1},{angle:5.393067388662478,size:.274204802103521,offset:8,opacity:1},{angle:5.410520681182422,size:.2565405657819229,offset:8,opacity:1},{angle:5.427973973702365,size:.3725761880437103,offset:8,opacity:1},{angle:5.445427266222309,size:0,offset:8,opacity:1},{angle:5.462880558742252,size:0,offset:8,opacity:1},{angle:5.480333851262195,size:.25731937569411467,offset:8,opacity:1},{angle:5.497787143782138,size:.3188635809253325,offset:8,opacity:1},{angle:5.515240436302081,size:.3356669541977725,offset:8,opacity:1},{angle:5.532693728822024,size:.20400657089323843,offset:8,opacity:1},{angle:5.550147021341967,size:0,offset:8,opacity:1},{angle:5.567600313861911,size:0,offset:8,opacity:1},{angle:5.585053606381854,size:0,offset:8,opacity:1},{angle:5.602506898901798,size:.3165635361425635,offset:8,opacity:1},{angle:5.619960191421741,size:.3235314722259551,offset:8,opacity:1},{angle:5.6374134839416845,size:.26584898777242194,offset:8,opacity:1},{angle:5.654866776461628,size:.21243062483790226,offset:8,opacity:1},{angle:5.672320068981571,size:.30724136900399,offset:8,opacity:1},{angle:5.689773361501514,size:.2501099291145934,offset:8,opacity:1},{angle:5.707226654021458,size:.39400265901275955,offset:8,opacity:1},{angle:5.724679946541401,size:.2093418399866516,offset:8,opacity:1},{angle:5.742133239061344,size:.32923152005471246,offset:8,opacity:1},{angle:5.759586531581287,size:0,offset:8,opacity:1},{angle:5.77703982410123,size:.56456531278707,offset:8,opacity:1},{angle:5.794493116621174,size:.20557753914211502,offset:8,opacity:1},{angle:5.811946409141117,size:.5684634070194285,offset:8,opacity:1},{angle:5.82939970166106,size:.20249056891565564,offset:8,opacity:1},{angle:5.846852994181004,size:.3190036119950446,offset:8,opacity:1},{angle:5.8643062867009474,size:0,offset:8,opacity:1},{angle:5.8817595792208905,size:.20533088638096786,offset:8,opacity:1},{angle:5.899212871740834,size:.3241677112796085,offset:8,opacity:1},{angle:5.916666164260777,size:.3624193559997502,offset:8,opacity:1},{angle:5.93411945678072,size:.40343678768322566,offset:8,opacity:1},{angle:5.951572749300664,size:.4188672613641715,offset:8,opacity:1},{angle:5.969026041820607,size:.2285210234832376,offset:8,opacity:1},{angle:5.98647933434055,size:0,offset:8,opacity:1},{angle:6.003932626860494,size:.48733967322430105,offset:8,opacity:1},{angle:6.021385919380437,size:.24186501745395364,offset:8,opacity:1},{angle:6.03883921190038,size:.4584926710071769,offset:8,opacity:1},{angle:6.056292504420323,size:.25479465338552776,offset:8,opacity:1},{angle:6.073745796940266,size:.2281956836229175,offset:8,opacity:1},{angle:6.09119908946021,size:.29373851820189983,offset:8,opacity:1},{angle:6.1086523819801535,size:.3027785858358294,offset:8,opacity:1},{angle:6.126105674500097,size:.39200941647226684,offset:8,opacity:1},{angle:6.14355896702004,size:.5167887646509625,offset:8,opacity:1},{angle:6.161012259539983,size:.48184476186013575,offset:8,opacity:1},{angle:6.178465552059926,size:.2170741818198825,offset:8,opacity:1},{angle:6.19591884457987,size:0,offset:8,opacity:1},{angle:6.213372137099813,size:.20735775148015026,offset:8,opacity:1},{angle:6.230825429619757,size:.38122080663892877,offset:8,opacity:1},{angle:6.2482787221397,size:.25023583869705845,offset:8,opacity:1},{angle:6.265732014659643,size:.2303276717210175,offset:8,opacity:1}],roughEdgeProbability:.3,roughEdgeShift:8,roughEdgePoints:360},m1={showRuler:!0,showFullRuler:!0,showCrossLine:!0,showDashLine:!0,showSideRuler:!0,showCurrentPositionText:!0},h1={svgPath:"M 0 -1 L 0.588 0.809 L -0.951 -0.309 L 0.951 -0.309 L -0.588 0.809 Z",drawStar:!1,starDiameter:14,starPositionY:0,scaleToSmallStar:!1},y1={openSecurityPattern:!0,securityPatternWidth:.15,securityPatternLength:3,securityPatternCount:5,securityPatternAngleRange:40,securityPatternParams:[{angle:1.409748801283011,lineAngle:.1920074644466812},{angle:5.32114078029858,lineAngle:2.6432851725092466},{angle:4.1098611216984136,lineAngle:-2.5979078269198292},{angle:4.994408472565594,lineAngle:2.8406667666143948},{angle:2.962601457171679,lineAngle:-1.5064351132749016}]},_1={companyName:"印章绘制有限责任公司",compression:1,borderOffset:1,textDistributionFactor:5,fontFamily:"Songti SC",fontHeight:4.2,fontWeight:400,shape:"ellipse",adjustEllipseText:!1,adjustEllipseTextFactor:.5,startAngle:0,rotateDirection:"counterclockwise"},v1={code:"1234567890",compression:1,fontHeight:1.2,fontFamily:"Arial",borderOffset:1,fontWidth:1.2,textDistributionFactor:50,fontWeight:"normal"},b1=40,C1=40,S1={stampType:"印章类型",fontHeight:4.6,fontFamily:"Arial",fontWidth:3,compression:.75,letterSpacing:0,positionY:-3,fontWeight:"normal",lineSpacing:2},T1="blue",w1=1,E1=!1,z1=!1,I1={code:"000000000000000000",compression:.7,fontHeight:3.7,fontFamily:"Arial",fontWidth:1.3,letterSpacing:8,positionY:0,totalWidth:26,fontWeight:"normal"},L1={applyAging:!1,agingIntensity:50,agingEffectParams:[]},P1={drawInnerCircle:!0,innerCircleLineWidth:.5,innerCircleLineRadiusX:16,innerCircleLineRadiusY:12},M1={drawInnerCircle:!0,innerCircleLineWidth:.2,innerCircleLineRadiusX:36,innerCircleLineRadiusY:27},A1=!1,x1=[{stampType:"印章类型",fontHeight:4.6,fontFamily:"Arial",fontWidth:3,compression:.75,letterSpacing:0,positionY:-3,fontWeight:"normal",lineSpacing:2}],N1=[{companyName:"绘制印章有限责任公司",compression:1,borderOffset:1,textDistributionFactor:3,fontFamily:"SimSun",fontHeight:4.2,fontWeight:"normal",shape:"ellipse",adjustEllipseText:!0,adjustEllipseTextFactor:.5,startAngle:0,rotateDirection:"counterclockwise"}],D1=[],O1=[],R1=1,F1=0,k1=0,U1=0,W1={drawInnerCircle:!0,innerCircleLineWidth:1,innerCircleLineRadiusX:20,innerCircleLineRadiusY:15},V1={roughEdge:d1,ruler:m1,drawStar:h1,securityPattern:y1,company:_1,stampCode:v1,width:b1,height:C1,stampType:S1,primaryColor:T1,borderWidth:w1,refreshSecurityPattern:E1,refreshOld:z1,taxNumber:I1,agingEffect:L1,innerCircle:P1,outThinCircle:M1,openManualAging:A1,stampTypeList:x1,companyList:N1,innerCircleList:D1,imageList:O1,scale:R1,offsetX:F1,offsetY:k1,mmToPixel:U1,outBorder:W1},$1={drawRoughEdge:!1,roughEdgeWidth:.2,roughEdgeHeight:5,roughEdgeParams:[{angle:0,size:.5032563701178842,offset:8,opacity:1},{angle:.017453292519943295,size:.3379166289700789,offset:8,opacity:1},{angle:.03490658503988659,size:.49850828105362677,offset:8,opacity:1},{angle:.05235987755982988,size:0,offset:8,opacity:1},{angle:.06981317007977318,size:.33027000340902746,offset:8,opacity:1},{angle:.08726646259971647,size:0,offset:8,opacity:1},{angle:.10471975511965977,size:.47393696973712246,offset:8,opacity:1},{angle:.12217304763960307,size:.6350416726108645,offset:8,opacity:1},{angle:.13962634015954636,size:.42238834211115167,offset:8,opacity:1},{angle:.15707963267948966,size:.2486073483438663,offset:8,opacity:1},{angle:.17453292519943295,size:0,offset:8,opacity:1},{angle:.19198621771937624,size:.35123274555265016,offset:8,opacity:1},{angle:.20943951023931953,size:.22448742164649077,offset:8,opacity:1},{angle:.22689280275926282,size:.4586736104699405,offset:8,opacity:1},{angle:.24434609527920614,size:.3043430828713793,offset:8,opacity:1},{angle:.2617993877991494,size:.3678224039628227,offset:8,opacity:1},{angle:.2792526803190927,size:.6163525747317525,offset:8,opacity:1},{angle:.296705972839036,size:.4363211517667617,offset:8,opacity:1},{angle:.3141592653589793,size:.205723548131973,offset:8,opacity:1},{angle:.33161255787892263,size:.4441622976512367,offset:8,opacity:1},{angle:.3490658503988659,size:0,offset:8,opacity:1},{angle:.3665191429188092,size:0,offset:8,opacity:1},{angle:.3839724354387525,size:.43017918657846765,offset:8,opacity:1},{angle:.40142572795869574,size:.4300022134137814,offset:8,opacity:1},{angle:.41887902047863906,size:0,offset:8,opacity:1},{angle:.4363323129985824,size:.25786098537584295,offset:8,opacity:1},{angle:.45378560551852565,size:.26525313850692,offset:8,opacity:1},{angle:.47123889803846897,size:.2838264875019364,offset:8,opacity:1},{angle:.4886921905584123,size:.4487316582245055,offset:8,opacity:1},{angle:.5061454830783556,size:.22112605086657722,offset:8,opacity:1},{angle:.5235987755982988,size:.4294648804576925,offset:8,opacity:1},{angle:.5410520681182421,size:.2181025744046031,offset:8,opacity:1},{angle:.5585053606381855,size:.20053237605486948,offset:8,opacity:1},{angle:.5759586531581287,size:0,offset:8,opacity:1},{angle:.593411945678072,size:0,offset:8,opacity:1},{angle:.6108652381980153,size:.20041438725089644,offset:8,opacity:1},{angle:.6283185307179586,size:.2609460635118062,offset:8,opacity:1},{angle:.6457718232379019,size:.39197138754198557,offset:8,opacity:1},{angle:.6632251157578453,size:.2605111967121892,offset:8,opacity:1},{angle:.6806784082777886,size:.20565487747739025,offset:8,opacity:1},{angle:.6981317007977318,size:.28654344221403566,offset:8,opacity:1},{angle:.7155849933176751,size:0,offset:8,opacity:1},{angle:.7330382858376184,size:.4584907994244955,offset:8,opacity:1},{angle:.7504915783575618,size:0,offset:8,opacity:1},{angle:.767944870877505,size:0,offset:8,opacity:1},{angle:.7853981633974483,size:0,offset:8,opacity:1},{angle:.8028514559173915,size:.32662457556616653,offset:8,opacity:1},{angle:.8203047484373349,size:.3119138674864993,offset:8,opacity:1},{angle:.8377580409572781,size:.4433990641657177,offset:8,opacity:1},{angle:.8552113334772213,size:.2123628886068149,offset:8,opacity:1},{angle:.8726646259971648,size:0,offset:8,opacity:1},{angle:.890117918517108,size:.34950148564089184,offset:8,opacity:1},{angle:.9075712110370513,size:0,offset:8,opacity:1},{angle:.9250245035569946,size:.20120500318879,offset:8,opacity:1},{angle:.9424777960769379,size:0,offset:8,opacity:1},{angle:.9599310885968813,size:.5188164082336375,offset:8,opacity:1},{angle:.9773843811168246,size:0,offset:8,opacity:1},{angle:.9948376736367678,size:.22172251375465574,offset:8,opacity:1},{angle:1.0122909661567112,size:.2432685121130981,offset:8,opacity:1},{angle:1.0297442586766543,size:.2320690651003732,offset:8,opacity:1},{angle:1.0471975511965976,size:.3907006251295331,offset:8,opacity:1},{angle:1.064650843716541,size:0,offset:8,opacity:1},{angle:1.0821041362364843,size:.2625156540222333,offset:8,opacity:1},{angle:1.0995574287564276,size:0,offset:8,opacity:1},{angle:1.117010721276371,size:.3145596222093862,offset:8,opacity:1},{angle:1.1344640137963142,size:.32936787676169677,offset:8,opacity:1},{angle:1.1519173063162573,size:.2857231314976785,offset:8,opacity:1},{angle:1.1693705988362009,size:.20120534399793935,offset:8,opacity:1},{angle:1.186823891356144,size:0,offset:8,opacity:1},{angle:1.2042771838760875,size:.23237394092882147,offset:8,opacity:1},{angle:1.2217304763960306,size:.28398641365511185,offset:8,opacity:1},{angle:1.239183768915974,size:.21502333555596864,offset:8,opacity:1},{angle:1.2566370614359172,size:.3499693244354978,offset:8,opacity:1},{angle:1.2740903539558606,size:0,offset:8,opacity:1},{angle:1.2915436464758039,size:.5105882933252887,offset:8,opacity:1},{angle:1.3089969389957472,size:.21784406246195218,offset:8,opacity:1},{angle:1.3264502315156905,size:.22514376311106876,offset:8,opacity:1},{angle:1.3439035240356336,size:.29704092249825453,offset:8,opacity:1},{angle:1.3613568165555772,size:.4521472353131752,offset:8,opacity:1},{angle:1.3788101090755203,size:.21507145749905754,offset:8,opacity:1},{angle:1.3962634015954636,size:.21682236241700453,offset:8,opacity:1},{angle:1.413716694115407,size:.22356961113236007,offset:8,opacity:1},{angle:1.4311699866353502,size:.22219417312865522,offset:8,opacity:1},{angle:1.4486232791552935,size:.2977119909206255,offset:8,opacity:1},{angle:1.4660765716752369,size:.38291243837511746,offset:8,opacity:1},{angle:1.48352986419518,size:.3116663219443704,offset:8,opacity:1},{angle:1.5009831567151235,size:0,offset:8,opacity:1},{angle:1.5184364492350666,size:.25492313554632756,offset:8,opacity:1},{angle:1.53588974175501,size:.2228509582782908,offset:8,opacity:1},{angle:1.5533430342749532,size:.35672171117898743,offset:8,opacity:1},{angle:1.5707963267948966,size:0,offset:8,opacity:1},{angle:1.5882496193148399,size:0,offset:8,opacity:1},{angle:1.605702911834783,size:.4388252813562349,offset:8,opacity:1},{angle:1.6231562043547265,size:.2273036372658915,offset:8,opacity:1},{angle:1.6406094968746698,size:.21718818137496743,offset:8,opacity:1},{angle:1.6580627893946132,size:.36941527530149404,offset:8,opacity:1},{angle:1.6755160819145563,size:.21623671572399,offset:8,opacity:1},{angle:1.6929693744344996,size:.636688937830729,offset:8,opacity:1},{angle:1.7104226669544427,size:0,offset:8,opacity:1},{angle:1.7278759594743864,size:0,offset:8,opacity:1},{angle:1.7453292519943295,size:.27728150462159734,offset:8,opacity:1},{angle:1.7627825445142729,size:0,offset:8,opacity:1},{angle:1.780235837034216,size:0,offset:8,opacity:1},{angle:1.7976891295541593,size:.23328034069543777,offset:8,opacity:1},{angle:1.8151424220741026,size:0,offset:8,opacity:1},{angle:1.8325957145940461,size:.2586898150005329,offset:8,opacity:1},{angle:1.8500490071139892,size:.4994559385312126,offset:8,opacity:1},{angle:1.8675022996339325,size:.26421680867532127,offset:8,opacity:1},{angle:1.8849555921538759,size:.3209021989338088,offset:8,opacity:1},{angle:1.902408884673819,size:0,offset:8,opacity:1},{angle:1.9198621771937625,size:0,offset:8,opacity:1},{angle:1.9373154697137058,size:.26002544806143374,offset:8,opacity:1},{angle:1.9547687622336491,size:.2924936993062236,offset:8,opacity:1},{angle:1.9722220547535922,size:.43140240061138796,offset:8,opacity:1},{angle:1.9896753472735356,size:.29591579647411836,offset:8,opacity:1},{angle:2.007128639793479,size:.46532747343985814,offset:8,opacity:1},{angle:2.0245819323134224,size:0,offset:8,opacity:1},{angle:2.0420352248333655,size:.37989836106928254,offset:8,opacity:1},{angle:2.0594885173533086,size:.43824671847111324,offset:8,opacity:1},{angle:2.076941809873252,size:.21491306461629336,offset:8,opacity:1},{angle:2.0943951023931953,size:.2576066045616476,offset:8,opacity:1},{angle:2.111848394913139,size:.20559969896825836,offset:8,opacity:1},{angle:2.129301687433082,size:.5452053035796387,offset:8,opacity:1},{angle:2.1467549799530254,size:.4317948579735969,offset:8,opacity:1},{angle:2.1642082724729685,size:.2926508010599716,offset:8,opacity:1},{angle:2.1816615649929116,size:.37646244630618103,offset:8,opacity:1},{angle:2.199114857512855,size:.5182160912889464,offset:8,opacity:1},{angle:2.2165681500327987,size:.5838728943805604,offset:8,opacity:1},{angle:2.234021442552742,size:.21844249399465382,offset:8,opacity:1},{angle:2.251474735072685,size:0,offset:8,opacity:1},{angle:2.2689280275926285,size:.3721009993624145,offset:8,opacity:1},{angle:2.2863813201125716,size:0,offset:8,opacity:1},{angle:2.3038346126325147,size:.24598938578156437,offset:8,opacity:1},{angle:2.321287905152458,size:.4507505938631045,offset:8,opacity:1},{angle:2.3387411976724017,size:.25469635650569583,offset:8,opacity:1},{angle:2.356194490192345,size:.30528741051655217,offset:8,opacity:1},{angle:2.373647782712288,size:.37088412070072785,offset:8,opacity:1},{angle:2.3911010752322315,size:.24486197147462863,offset:8,opacity:1},{angle:2.408554367752175,size:0,offset:8,opacity:1},{angle:2.426007660272118,size:.43989471208136854,offset:8,opacity:1},{angle:2.443460952792061,size:.33696542573155486,offset:8,opacity:1},{angle:2.4609142453120048,size:0,offset:8,opacity:1},{angle:2.478367537831948,size:0,offset:8,opacity:1},{angle:2.495820830351891,size:0,offset:8,opacity:1},{angle:2.5132741228718345,size:.2505063689411901,offset:8,opacity:1},{angle:2.530727415391778,size:.31438011396387455,offset:8,opacity:1},{angle:2.548180707911721,size:.34374426546984016,offset:8,opacity:1},{angle:2.5656340004316642,size:.2305610481543743,offset:8,opacity:1},{angle:2.5830872929516078,size:.37268657957858453,offset:8,opacity:1},{angle:2.600540585471551,size:.25219421624230426,offset:8,opacity:1},{angle:2.6179938779914944,size:0,offset:8,opacity:1},{angle:2.6354471705114375,size:.23021680363052838,offset:8,opacity:1},{angle:2.652900463031381,size:.3483359449322281,offset:8,opacity:1},{angle:2.670353755551324,size:.3251780474107786,offset:8,opacity:1},{angle:2.6878070480712672,size:0,offset:8,opacity:1},{angle:2.705260340591211,size:.3687577362310519,offset:8,opacity:1},{angle:2.7227136331111543,size:.5694461018098402,offset:8,opacity:1},{angle:2.7401669256310974,size:.22425733709526832,offset:8,opacity:1},{angle:2.7576202181510405,size:0,offset:8,opacity:1},{angle:2.7750735106709836,size:.32709123488963454,offset:8,opacity:1},{angle:2.792526803190927,size:.3643797544475289,offset:8,opacity:1},{angle:2.8099800957108707,size:.25481296814968835,offset:8,opacity:1},{angle:2.827433388230814,size:.20233815801319835,offset:8,opacity:1},{angle:2.844886680750757,size:.22289439004543232,offset:8,opacity:1},{angle:2.8623399732707004,size:0,offset:8,opacity:1},{angle:2.8797932657906435,size:0,offset:8,opacity:1},{angle:2.897246558310587,size:0,offset:8,opacity:1},{angle:2.91469985083053,size:.22662095056906018,offset:8,opacity:1},{angle:2.9321531433504737,size:.6177358599983805,offset:8,opacity:1},{angle:2.949606435870417,size:.5579600289881892,offset:8,opacity:1},{angle:2.96705972839036,size:.24919239443796898,offset:8,opacity:1},{angle:2.9845130209103035,size:0,offset:8,opacity:1},{angle:3.001966313430247,size:.20285345071151972,offset:8,opacity:1},{angle:3.01941960595019,size:.29633213437720063,offset:8,opacity:1},{angle:3.036872898470133,size:.31615740448077223,offset:8,opacity:1},{angle:3.0543261909900767,size:.4883995719883713,offset:8,opacity:1},{angle:3.07177948351002,size:.2500925025911332,offset:8,opacity:1},{angle:3.089232776029963,size:.262931178068741,offset:8,opacity:1},{angle:3.1066860685499065,size:.3135512137978654,offset:8,opacity:1},{angle:3.12413936106985,size:.31083588965839803,offset:8,opacity:1},{angle:3.141592653589793,size:0,offset:8,opacity:1},{angle:3.159045946109736,size:.24804439339468767,offset:8,opacity:1},{angle:3.1764992386296798,size:0,offset:8,opacity:1},{angle:3.193952531149623,size:.2571395452468249,offset:8,opacity:1},{angle:3.211405823669566,size:.6279202198461746,offset:8,opacity:1},{angle:3.2288591161895095,size:.24288586668685336,offset:8,opacity:1},{angle:3.246312408709453,size:.34718500726687895,offset:8,opacity:1},{angle:3.2637657012293966,size:.47061815108690846,offset:8,opacity:1},{angle:3.2812189937493397,size:0,offset:8,opacity:1},{angle:3.2986722862692828,size:.4866126208626991,offset:8,opacity:1},{angle:3.3161255787892263,size:0,offset:8,opacity:1},{angle:3.3335788713091694,size:0,offset:8,opacity:1},{angle:3.3510321638291125,size:.22976093136313686,offset:8,opacity:1},{angle:3.368485456349056,size:0,offset:8,opacity:1},{angle:3.385938748868999,size:0,offset:8,opacity:1},{angle:3.4033920413889422,size:.4397259724357342,offset:8,opacity:1},{angle:3.4208453339088853,size:.24667841492062575,offset:8,opacity:1},{angle:3.4382986264288293,size:.43391379529064145,offset:8,opacity:1},{angle:3.455751918948773,size:0,offset:8,opacity:1},{angle:3.473205211468716,size:0,offset:8,opacity:1},{angle:3.490658503988659,size:.21961175000891414,offset:8,opacity:1},{angle:3.5081117965086026,size:.36890033272657746,offset:8,opacity:1},{angle:3.5255650890285457,size:.4326945036689108,offset:8,opacity:1},{angle:3.543018381548489,size:.3268470087082487,offset:8,opacity:1},{angle:3.560471674068432,size:.20854219238334568,offset:8,opacity:1},{angle:3.5779249665883754,size:.2423254936922336,offset:8,opacity:1},{angle:3.5953782591083185,size:.4064744910955269,offset:8,opacity:1},{angle:3.6128315516282616,size:0,offset:8,opacity:1},{angle:3.630284844148205,size:.31287388031447483,offset:8,opacity:1},{angle:3.647738136668149,size:.3948160804791036,offset:8,opacity:1},{angle:3.6651914291880923,size:0,offset:8,opacity:1},{angle:3.6826447217080354,size:.2026956152601606,offset:8,opacity:1},{angle:3.7000980142279785,size:0,offset:8,opacity:1},{angle:3.717551306747922,size:.44054519647144813,offset:8,opacity:1},{angle:3.735004599267865,size:.4130009051490618,offset:8,opacity:1},{angle:3.752457891787808,size:0,offset:8,opacity:1},{angle:3.7699111843077517,size:.49311184417141246,offset:8,opacity:1},{angle:3.787364476827695,size:.45508968556466084,offset:8,opacity:1},{angle:3.804817769347638,size:0,offset:8,opacity:1},{angle:3.8222710618675815,size:.48005037780936805,offset:8,opacity:1},{angle:3.839724354387525,size:0,offset:8,opacity:1},{angle:3.8571776469074686,size:.4561164475672816,offset:8,opacity:1},{angle:3.8746309394274117,size:.3002000652009621,offset:8,opacity:1},{angle:3.8920842319473548,size:.3336541884527151,offset:8,opacity:1},{angle:3.9095375244672983,size:0,offset:8,opacity:1},{angle:3.9269908169872414,size:.4811384274609927,offset:8,opacity:1},{angle:3.9444441095071845,size:0,offset:8,opacity:1},{angle:3.961897402027128,size:.5102504861379599,offset:8,opacity:1},{angle:3.979350694547071,size:.23214101697543765,offset:8,opacity:1},{angle:3.9968039870670142,size:.29156434123379016,offset:8,opacity:1},{angle:4.014257279586958,size:0,offset:8,opacity:1},{angle:4.031710572106902,size:.20004717274584805,offset:8,opacity:1},{angle:4.049163864626845,size:.3614701974794731,offset:8,opacity:1},{angle:4.066617157146788,size:0,offset:8,opacity:1},{angle:4.084070449666731,size:.2219991701875027,offset:8,opacity:1},{angle:4.101523742186674,size:.22431067260162876,offset:8,opacity:1},{angle:4.118977034706617,size:.30926005945781443,offset:8,opacity:1},{angle:4.136430327226561,size:.2513871895964691,offset:8,opacity:1},{angle:4.153883619746504,size:.2659224601694766,offset:8,opacity:1},{angle:4.171336912266447,size:.2216658406470619,offset:8,opacity:1},{angle:4.1887902047863905,size:.4273566387084955,offset:8,opacity:1},{angle:4.206243497306334,size:.34036553217806864,offset:8,opacity:1},{angle:4.223696789826278,size:.32533678816516465,offset:8,opacity:1},{angle:4.241150082346221,size:.23594875228493112,offset:8,opacity:1},{angle:4.258603374866164,size:0,offset:8,opacity:1},{angle:4.276056667386108,size:0,offset:8,opacity:1},{angle:4.293509959906051,size:.31051254932537203,offset:8,opacity:1},{angle:4.310963252425994,size:0,offset:8,opacity:1},{angle:4.328416544945937,size:0,offset:8,opacity:1},{angle:4.34586983746588,size:0,offset:8,opacity:1},{angle:4.363323129985823,size:.41758554970783024,offset:8,opacity:1},{angle:4.380776422505767,size:0,offset:8,opacity:1},{angle:4.39822971502571,size:.5165181485571547,offset:8,opacity:1},{angle:4.4156830075456535,size:0,offset:8,opacity:1},{angle:4.4331363000655974,size:0,offset:8,opacity:1},{angle:4.4505895925855405,size:.2875659335408256,offset:8,opacity:1},{angle:4.468042885105484,size:.2630697344221547,offset:8,opacity:1},{angle:4.485496177625427,size:.3044067435482997,offset:8,opacity:1},{angle:4.50294947014537,size:.3224161157725142,offset:8,opacity:1},{angle:4.520402762665314,size:.29296045447988417,offset:8,opacity:1},{angle:4.537856055185257,size:0,offset:8,opacity:1},{angle:4.5553093477052,size:.5348621754537155,offset:8,opacity:1},{angle:4.572762640225143,size:.23724437888510921,offset:8,opacity:1},{angle:4.590215932745086,size:0,offset:8,opacity:1},{angle:4.607669225265029,size:.23471290119278718,offset:8,opacity:1},{angle:4.625122517784973,size:.23001169467314136,offset:8,opacity:1},{angle:4.642575810304916,size:.3381947981140539,offset:8,opacity:1},{angle:4.66002910282486,size:0,offset:8,opacity:1},{angle:4.6774823953448035,size:.2820194526396866,offset:8,opacity:1},{angle:4.694935687864747,size:.27589340223228825,offset:8,opacity:1},{angle:4.71238898038469,size:0,offset:8,opacity:1},{angle:4.729842272904633,size:0,offset:8,opacity:1},{angle:4.747295565424576,size:0,offset:8,opacity:1},{angle:4.764748857944519,size:0,offset:8,opacity:1},{angle:4.782202150464463,size:.6047061570324029,offset:8,opacity:1},{angle:4.799655442984406,size:0,offset:8,opacity:1},{angle:4.81710873550435,size:0,offset:8,opacity:1},{angle:4.834562028024293,size:.4385393337320528,offset:8,opacity:1},{angle:4.852015320544236,size:0,offset:8,opacity:1},{angle:4.869468613064179,size:.3738861085343654,offset:8,opacity:1},{angle:4.886921905584122,size:.2369083255375507,offset:8,opacity:1},{angle:4.9043751981040655,size:.20042730995532476,offset:8,opacity:1},{angle:4.9218284906240095,size:.27407857191507573,offset:8,opacity:1},{angle:4.939281783143953,size:.260307602595212,offset:8,opacity:1},{angle:4.956735075663896,size:.22693010922108042,offset:8,opacity:1},{angle:4.974188368183839,size:0,offset:8,opacity:1},{angle:4.991641660703782,size:.36404125447608293,offset:8,opacity:1},{angle:5.009094953223726,size:.36660145102645764,offset:8,opacity:1},{angle:5.026548245743669,size:.21844437325488894,offset:8,opacity:1},{angle:5.044001538263612,size:0,offset:8,opacity:1},{angle:5.061454830783556,size:0,offset:8,opacity:1},{angle:5.078908123303499,size:0,offset:8,opacity:1},{angle:5.096361415823442,size:.6203742746502853,offset:8,opacity:1},{angle:5.113814708343385,size:0,offset:8,opacity:1},{angle:5.1312680008633285,size:0,offset:8,opacity:1},{angle:5.148721293383272,size:0,offset:8,opacity:1},{angle:5.1661745859032155,size:0,offset:8,opacity:1},{angle:5.183627878423159,size:.4394142349769681,offset:8,opacity:1},{angle:5.201081170943102,size:.46186021808530575,offset:8,opacity:1},{angle:5.218534463463046,size:.29002729040435316,offset:8,opacity:1},{angle:5.235987755982989,size:0,offset:8,opacity:1},{angle:5.253441048502932,size:0,offset:8,opacity:1},{angle:5.270894341022875,size:0,offset:8,opacity:1},{angle:5.288347633542818,size:.2776981969296649,offset:8,opacity:1},{angle:5.305800926062762,size:0,offset:8,opacity:1},{angle:5.323254218582705,size:.3066397012962211,offset:8,opacity:1},{angle:5.340707511102648,size:.22441601610004672,offset:8,opacity:1},{angle:5.358160803622591,size:.25396857567416625,offset:8,opacity:1},{angle:5.3756140961425345,size:.5404051622973916,offset:8,opacity:1},{angle:5.393067388662478,size:.274204802103521,offset:8,opacity:1},{angle:5.410520681182422,size:.2565405657819229,offset:8,opacity:1},{angle:5.427973973702365,size:.3725761880437103,offset:8,opacity:1},{angle:5.445427266222309,size:0,offset:8,opacity:1},{angle:5.462880558742252,size:0,offset:8,opacity:1},{angle:5.480333851262195,size:.25731937569411467,offset:8,opacity:1},{angle:5.497787143782138,size:.3188635809253325,offset:8,opacity:1},{angle:5.515240436302081,size:.3356669541977725,offset:8,opacity:1},{angle:5.532693728822024,size:.20400657089323843,offset:8,opacity:1},{angle:5.550147021341967,size:0,offset:8,opacity:1},{angle:5.567600313861911,size:0,offset:8,opacity:1},{angle:5.585053606381854,size:0,offset:8,opacity:1},{angle:5.602506898901798,size:.3165635361425635,offset:8,opacity:1},{angle:5.619960191421741,size:.3235314722259551,offset:8,opacity:1},{angle:5.6374134839416845,size:.26584898777242194,offset:8,opacity:1},{angle:5.654866776461628,size:.21243062483790226,offset:8,opacity:1},{angle:5.672320068981571,size:.30724136900399,offset:8,opacity:1},{angle:5.689773361501514,size:.2501099291145934,offset:8,opacity:1},{angle:5.707226654021458,size:.39400265901275955,offset:8,opacity:1},{angle:5.724679946541401,size:.2093418399866516,offset:8,opacity:1},{angle:5.742133239061344,size:.32923152005471246,offset:8,opacity:1},{angle:5.759586531581287,size:0,offset:8,opacity:1},{angle:5.77703982410123,size:.56456531278707,offset:8,opacity:1},{angle:5.794493116621174,size:.20557753914211502,offset:8,opacity:1},{angle:5.811946409141117,size:.5684634070194285,offset:8,opacity:1},{angle:5.82939970166106,size:.20249056891565564,offset:8,opacity:1},{angle:5.846852994181004,size:.3190036119950446,offset:8,opacity:1},{angle:5.8643062867009474,size:0,offset:8,opacity:1},{angle:5.8817595792208905,size:.20533088638096786,offset:8,opacity:1},{angle:5.899212871740834,size:.3241677112796085,offset:8,opacity:1},{angle:5.916666164260777,size:.3624193559997502,offset:8,opacity:1},{angle:5.93411945678072,size:.40343678768322566,offset:8,opacity:1},{angle:5.951572749300664,size:.4188672613641715,offset:8,opacity:1},{angle:5.969026041820607,size:.2285210234832376,offset:8,opacity:1},{angle:5.98647933434055,size:0,offset:8,opacity:1},{angle:6.003932626860494,size:.48733967322430105,offset:8,opacity:1},{angle:6.021385919380437,size:.24186501745395364,offset:8,opacity:1},{angle:6.03883921190038,size:.4584926710071769,offset:8,opacity:1},{angle:6.056292504420323,size:.25479465338552776,offset:8,opacity:1},{angle:6.073745796940266,size:.2281956836229175,offset:8,opacity:1},{angle:6.09119908946021,size:.29373851820189983,offset:8,opacity:1},{angle:6.1086523819801535,size:.3027785858358294,offset:8,opacity:1},{angle:6.126105674500097,size:.39200941647226684,offset:8,opacity:1},{angle:6.14355896702004,size:.5167887646509625,offset:8,opacity:1},{angle:6.161012259539983,size:.48184476186013575,offset:8,opacity:1},{angle:6.178465552059926,size:.2170741818198825,offset:8,opacity:1},{angle:6.19591884457987,size:0,offset:8,opacity:1},{angle:6.213372137099813,size:.20735775148015026,offset:8,opacity:1},{angle:6.230825429619757,size:.38122080663892877,offset:8,opacity:1},{angle:6.2482787221397,size:.25023583869705845,offset:8,opacity:1},{angle:6.265732014659643,size:.2303276717210175,offset:8,opacity:1}],roughEdgeProbability:.3,roughEdgeShift:8,roughEdgePoints:360},H1={showRuler:!0,showFullRuler:!0,showCrossLine:!0,showDashLine:!0,showSideRuler:!0,showCurrentPositionText:!0},j1={svgPath:"M 0 -1 L 0.588 0.809 L -0.951 -0.309 L 0.951 -0.309 L -0.588 0.809 Z",drawStar:!1,starDiameter:14,starPositionY:0,scaleToSmallStar:!1},Y1={openSecurityPattern:!0,securityPatternWidth:.15,securityPatternLength:3,securityPatternCount:5,securityPatternAngleRange:40,securityPatternParams:[{angle:1.409748801283011,lineAngle:.1920074644466812},{angle:5.32114078029858,lineAngle:2.6432851725092466},{angle:4.1098611216984136,lineAngle:-2.5979078269198292},{angle:4.994408472565594,lineAngle:2.8406667666143948},{angle:2.962601457171679,lineAngle:-1.5064351132749016}]},B1={companyName:"印章绘制有限责任公司",compression:1,borderOffset:1,textDistributionFactor:5,fontFamily:"Songti SC",fontHeight:4.2,fontWeight:400,shape:"ellipse",adjustEllipseText:!1,adjustEllipseTextFactor:.5,startAngle:0,rotateDirection:"counterclockwise"},G1={code:"1234567890",compression:1,fontHeight:1.2,fontFamily:"Arial",borderOffset:1,fontWidth:1.2,textDistributionFactor:50,fontWeight:"normal"},X1=40,K1=40,J1={stampType:"印章类型",fontHeight:4.6,fontFamily:"Arial",fontWidth:3,compression:.75,letterSpacing:0,positionY:-3,fontWeight:"normal",lineSpacing:2},Q1="blue",q1=1,Z1=!1,ep=!1,tp={code:"",compression:.7,fontHeight:3.7,fontFamily:"Arial",fontWidth:1.3,letterSpacing:8,positionY:0,totalWidth:26,fontWeight:"normal"},sp={applyAging:!1,agingIntensity:50,agingEffectParams:[]},np={drawInnerCircle:!0,innerCircleLineWidth:.5,innerCircleLineRadiusX:16,innerCircleLineRadiusY:12},ip={drawInnerCircle:!0,innerCircleLineWidth:.2,innerCircleLineRadiusX:36,innerCircleLineRadiusY:27},op=!1,ap=[{stampType:"印章类型",fontHeight:4.6,fontFamily:"Arial",fontWidth:3,compression:.75,letterSpacing:0,positionY:.5,fontWeight:"normal",lineSpacing:2}],lp=[{companyName:"绘制印章有限责任公司",compression:1,borderOffset:1,textDistributionFactor:3,fontFamily:"SimSun",fontHeight:4.2,fontWeight:"normal",shape:"ellipse",adjustEllipseText:!0,adjustEllipseTextFactor:.5,startAngle:0,rotateDirection:"counterclockwise"}],rp=[],cp=[{imageUrl:"data:image/svg+xml;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************",imageWidth:16.5,imageHeight:16,positionX:0,positionY:-2,keepAspectRatio:!0}],fp=1,up=0,pp=0,gp=0,dp={drawInnerCircle:!0,innerCircleLineWidth:1,innerCircleLineRadiusX:20,innerCircleLineRadiusY:15},mp={roughEdge:$1,ruler:H1,drawStar:j1,securityPattern:Y1,company:B1,stampCode:G1,width:X1,height:K1,stampType:J1,primaryColor:Q1,borderWidth:q1,refreshSecurityPattern:Z1,refreshOld:ep,taxNumber:tp,agingEffect:sp,innerCircle:np,outThinCircle:ip,openManualAging:op,stampTypeList:ap,companyList:lp,innerCircleList:rp,imageList:cp,scale:fp,offsetX:up,offsetY:pp,mmToPixel:gp,outBorder:dp},hp={key:0,class:"legal-dialog-overlay"},yp={class:"legal-dialog"},_p={class:"legal-content"},vp={style:{"white-space":"pre-line"}},bp={class:"dialog-buttons"},Cp={class:"disclaimer-content"},Sp={class:"warning-text"},Tp={style:{"white-space":"pre-line"}},wp={class:"button-group",style:{position:"sticky",top:"0","z-index":"1000","background-color":"white",padding:"10px"}},Ep={class:"control-group"},zp={class:"group-content"},Ip={class:"checkbox-label"},Lp={class:"checkbox-label"},Pp={key:0},Mp={class:"control-group"},Ap={class:"group-content"},xp={class:"company-header"},Np=["onClick"],Dp=["onUpdate:modelValue"],Op={class:"font-input-group"},Rp=["onUpdate:modelValue"],Fp=["value"],kp=["onUpdate:modelValue","placeholder"],Up=["onUpdate:modelValue"],Wp=["onUpdate:modelValue"],Vp={value:"normal"},$p={value:"bold"},Hp=["onUpdate:modelValue"],jp=["onUpdate:modelValue"],Yp=["onUpdate:modelValue"],Bp={class:"control-group"},Gp={class:"group-content"},Xp={class:"stamp-type-header"},Kp=["onClick"],Jp=["onUpdate:modelValue"],Qp=["onUpdate:modelValue"],qp={class:"font-input-group"},Zp=["onUpdate:modelValue"],eg={id:"stampTypeFontList"},tg=["value"],sg=["onUpdate:modelValue"],ng=["onUpdate:modelValue"],ig=["onUpdate:modelValue"],og=["onUpdate:modelValue"],ag={class:"control-group"},lg={class:"group-content"},rg={class:"font-input-group"},cg=["value"],fg={value:"normal"},ug={value:"bold"},pg={class:"control-group"},gg={class:"group-content"},dg={class:"font-input-group"},mg=["value"],hg={class:"control-group"},yg={class:"group-content"},_g={class:"image-header"},vg=["onClick"],bg={key:0,class:"image-preview"},Cg=["src","alt"],Sg=["onChange"],Tg=["onUpdate:modelValue"],wg=["onUpdate:modelValue"],Eg=["onUpdate:modelValue"],zg=["onUpdate:modelValue"],Ig={class:"checkbox-label"},Lg=["onUpdate:modelValue"],Pg={class:"control-group"},Mg={class:"group-content"},Ag={class:"checkbox-label"},xg={key:0},Ng={class:"control-group"},Dg={class:"group-content"},Og={class:"control-group"},Rg={class:"group-content"},Fg={class:"checkbox-label"},kg={key:0},Ug={key:1},Wg={key:2},Vg={key:3},$g={key:4},Hg={class:"control-group"},jg={class:"group-content"},Yg={class:"checkbox-label"},Bg={class:"checkbox-label"},Gg={key:0},Xg={class:"control-group"},Kg={class:"group-content"},Jg={class:"inner-circle-header"},Qg=["onClick"],qg=["onUpdate:modelValue"],Zg=["onUpdate:modelValue"],ed=["onUpdate:modelValue"],td={class:"canvas-container"},sd={style:{display:"flex","flex-direction":"row","margin-top":"12px",gap:"12px"}},nd={class:"control-group"},id={class:"checkbox-label"},od={class:"checkbox-label"},ad={key:0},ld={class:"control-group"},rd={style:{"margin-top":"12px"}},cd={class:"template-panel"},fd={class:"template-header"},ud={class:"template-list"},pd={class:"template-category"},gd=["onClick"],dd={class:"template-preview"},md=["src","alt"],hd={class:"template-info"},yd={class:"template-name"},_d=10,vd=mn({__name:"DrawStampUtilsDemo",setup(e){const{t}=_n(),s=$(null),n=$(null),i=$(!0),o=$("绘制印章有限责任公司"),a=$("1234567890123"),l=$("000000000000000000"),r=$("Songti SC"),u=$(4.2),f=$("SimSun"),d=$(1.2),v=$(1.2),E=$(20),D=$(1),O=$("blue"),W=$(14),b=$(!1),z=$(!1),P=$(50),T=$(3),F=$(!1),H=$(.5),k=$(1),ee=$(1),ne=$(20),_e=$("合同专用章"),Ee=$("SimSun"),ve=$(4.6),Ze=$(3),ft=$(0),ut=$(0),ze=$(-5),ce=$(1),ae=$(400),Pe=$(400),Be=$(400),Me=$("Songti SC"),Ae=$(400),Tt=$(1),pt=$(1),wt=$(!0),yt=$(.5),et=$(.2),_t=$("#FF0000"),gt=$(5),Ge=$(2);$(!1);const st=$(!1),nt=$(1),m=$(.3),h=$(0),L=$(!0),U=$(.5),R=$(15),c=$(12),g=$(!0),S=$(.5),I=$(25),A=$(22);$(null);const V=$(!1),y=$(.2),w=$(5),Y=$(.5),X=$(8),se=$(360),ie=$(!1),pe=$([{stampType:"印章类型",fontHeight:4.6,fontFamily:"SimSun",compression:.75,letterSpacing:0,positionY:-3,fontWeight:"normal",lineSpacing:2,fontWidth:3}]),he=$([{companyName:"绘制印章有限责任公司",compression:1,borderOffset:1,textDistributionFactor:3,fontFamily:"SimSun",fontHeight:4.2,fontWeight:"normal",shape:"ellipse",adjustEllipseText:!1,adjustEllipseTextFactor:.5,startAngle:0,rotateDirection:"counterclockwise"}]),Ve=$(!1),ke=$(10),Nt=$(10),ys=$(!0),xe=$([{drawInnerCircle:!0,innerCircleLineWidth:.5,innerCircleLineRadiusX:36,innerCircleLineRadiusY:27},{drawInnerCircle:!0,innerCircleLineWidth:.5,innerCircleLineRadiusX:16,innerCircleLineRadiusY:12}]),it=$(null),Xe=$([{imageUrl:"",imageWidth:10,imageHeight:10,positionX:0,positionY:0,keepAspectRatio:!0}]),_s=$(!0),Fs=$(1),or=()=>{console.log("add new image",Xe.value),(Xe.value===void 0||Xe.value===null)&&(Xe.value=[]),Xe.value.length<10&&Xe.value.push({imageUrl:"",imageWidth:10,imageHeight:10,positionX:0,positionY:0,keepAspectRatio:!0})},ar=x=>{Xe.value.splice(x,1)},_o=()=>{const x=Xt.getDrawConfigs(),C=JSON.stringify(x,null,2),_=new Blob([C],{type:"application/json"}),Q=URL.createObjectURL(_),j=document.createElement("a");j.href=Q,j.download="stamp_template.json",document.body.appendChild(j),j.click(),document.body.removeChild(j),URL.revokeObjectURL(Q)},lr=()=>{var x;(x=it.value)==null||x.click()},rr=x=>{const C=x.target;if(C.files&&C.files[0]){const _=C.files[0],Q=new FileReader;Q.onload=j=>{var Ie;try{if((Ie=j.target)!=null&&Ie.result){const Ke=j.target.result,Ot=JSON.parse(Ke);Xt.setDrawConfigs(Ot),ni(),Dt()}}catch(Ke){console.error("加载模板失败:",Ke),alert("加载模板失败，请确保文件格式正确")}},Q.readAsText(_)}C.value=""},cr=(x,C)=>{const _=x.target;if(_.files&&_.files[0]){const Q=_.files[0],j=new FileReader;j.onload=Ie=>{var Ke;(Ke=Ie.target)!=null&&Ke.result&&(Xe.value[C].imageUrl=Ie.target.result,Dt())},j.readAsDataURL(Q)}},fr=()=>{let x=-3;if(pe.value.length>0){const C=pe.value[pe.value.length-1];x=C.positionY+C.fontHeight}pe.value.push({stampType:"新印章类型",fontHeight:4,fontFamily:"SimSun",compression:.75,letterSpacing:0,positionY:x,fontWeight:"normal",lineSpacing:2,fontWidth:3})},ur=x=>{pe.value.splice(x,1)},pr=()=>{let x=1;if(he.value.length>0){const C=he.value[he.value.length-1];x=C.borderOffset+C.fontHeight}he.value.push({companyName:"新公司名称",compression:1,borderOffset:x,textDistributionFactor:3,fontFamily:"SimSun",fontHeight:4.2,fontWeight:"normal",shape:"ellipse",adjustEllipseText:!1,adjustEllipseTextFactor:.5,startAngle:0,rotateDirection:"counterclockwise"})},gr=x=>{he.value.splice(x,1)},dr=()=>{ie.value=!0},ks=$(40),Us=$(30),si=$(1.2);let Xt;const mr=()=>{Xt=new Na(n.value,_d)},Dt=(x=!1,C=!1,_=!1)=>{Xt.refreshStamp(x,C,_)},hr=()=>{xe.value.push({drawInnerCircle:!0,innerCircleLineWidth:.5,innerCircleLineRadiusX:16,innerCircleLineRadiusY:12})},yr=x=>{xe.value.splice(x,1)},_r=()=>{const x=Xt.getDrawConfigs(),C=x.agingEffect;C.applyAging=b.value,C.agingIntensity=P.value,x.openManualAging=z.value;const _=x.securityPattern;_.openSecurityPattern=wt.value,_.securityPatternCount=gt.value,_.securityPatternWidth=et.value,_.securityPatternLength=Ge.value;const Q=x.company;Q.companyName=o.value,Q.textDistributionFactor=T.value,Q.borderOffset=k.value,Q.fontHeight=u.value,Q.fontFamily=r.value,Q.compression=ce.value,Q.fontWeight=ae.value,Q.adjustEllipseText=F.value,Q.adjustEllipseTextFactor=H.value;const j=x.taxNumber;j.code=l.value,j.compression=nt.value,j.positionY=h.value,j.letterSpacing=m.value,j.fontFamily=Me.value,j.fontWeight=Ae.value;const Ie=x.stampType;Ie.stampType=_e.value,Ie.fontFamily=Ee.value,Ie.fontHeight=ve.value,Ie.fontWidth=Ze.value,Ie.letterSpacing=ft.value,Ie.positionY=ze.value,Ie.compression=Tt.value,Ie.fontWeight=Pe.value,Ie.lineSpacing=si.value;const Ke=x.stampCode;Ke.code=a.value,Ke.compression=pt.value,Ke.fontFamily=f.value,Ke.fontHeight=d.value,Ke.fontWidth=v.value,Ke.borderOffset=ee.value,Ke.textDistributionFactor=ne.value,Ke.fontWeight=Be.value,x.primaryColor=O.value,x.borderWidth=D.value,x.width=ks.value,x.height=Us.value;const Ot=x.drawStar;Ot.drawStar=st.value,Ot.useImage=Ve.value,Ot.imageWidth=ke.value,Ot.imageHeight=Nt.value,Ot.keepAspectRatio=ys.value,Ot.starDiameter=W.value,Ot.starPositionY=ut.value;const vs=x.roughEdge;vs.drawRoughEdge=V.value,vs.roughEdgeWidth=y.value,vs.roughEdgeHeight=w.value,vs.roughEdgeProbability=Y.value,vs.roughEdgeShift=X.value,vs.roughEdgePoints=se.value;const vn=x.innerCircle;vn.drawInnerCircle=L.value,vn.innerCircleLineWidth=U.value,vn.innerCircleLineRadiusX=R.value,vn.innerCircleLineRadiusY=c.value;const bn=x.outThinCircle;bn.drawInnerCircle=g.value,bn.innerCircleLineWidth=S.value,bn.innerCircleLineRadiusX=I.value,bn.innerCircleLineRadiusY=A.value,x.stampTypeList=pe.value,x.companyList=he.value,x.innerCircleList=xe.value,x.imageList=Xe.value;const bo=x.outBorder;bo.drawInnerCircle=_s.value,bo.innerCircleLineWidth=Fs.value,Dt()},vr=()=>{ie.value=!1},br=()=>{ie.value=!1,Xt.saveStampAsPNG()},ni=()=>{const x=Xt.getDrawConfigs();b.value=x.agingEffect.applyAging,P.value=x.agingEffect.agingIntensity,z.value=x.openManualAging,wt.value=x.securityPattern.openSecurityPattern,gt.value=x.securityPattern.securityPatternCount,et.value=x.securityPattern.securityPatternWidth,Ge.value=x.securityPattern.securityPatternLength,V.value=x.roughEdge.drawRoughEdge,y.value=x.roughEdge.roughEdgeWidth,w.value=x.roughEdge.roughEdgeHeight,Y.value=x.roughEdge.roughEdgeProbability,X.value=x.roughEdge.roughEdgeShift,se.value=x.roughEdge.roughEdgePoints,ks.value=x.width,Us.value=x.height,D.value=x.borderWidth,O.value=x.primaryColor,o.value=x.company.companyName,u.value=x.company.fontHeight,ce.value=x.company.compression,T.value=x.company.textDistributionFactor,k.value=x.company.borderOffset,he.value=x.companyList;const C=x.stampCode;a.value=C.code,d.value=C.fontHeight,v.value=C.fontWidth,ne.value=C.textDistributionFactor,ee.value=C.borderOffset,f.value=C.fontFamily,Be.value=C.fontWeight,pt.value=C.compression;const _=x.taxNumber;l.value=_.code,nt.value=_.compression,m.value=_.letterSpacing,h.value=_.positionY,Me.value=_.fontFamily,Ae.value=_.fontWeight;const Q=x.stampType;_e.value=Q.stampType,ve.value=Q.fontHeight,Ze.value=Q.fontWidth,ft.value=Q.letterSpacing,ze.value=Q.positionY,Ee.value=Q.fontFamily,Pe.value=Q.fontWeight,Tt.value=Q.compression,si.value=Q.lineSpacing,pe.value=x.stampTypeList,st.value=x.drawStar.drawStar,Ve.value=x.drawStar.useImage,ke.value=x.drawStar.imageWidth,Nt.value=x.drawStar.imageHeight,ys.value=x.drawStar.keepAspectRatio,W.value=x.drawStar.starDiameter,ut.value=x.drawStar.starPositionY,L.value=x.innerCircle.drawInnerCircle,U.value=x.innerCircle.innerCircleLineWidth,R.value=x.innerCircle.innerCircleLineRadiusX,c.value=x.innerCircle.innerCircleLineRadiusY,xe.value=x.innerCircleList,g.value=x.outThinCircle.drawInnerCircle,S.value=x.outThinCircle.innerCircleLineWidth,I.value=x.outThinCircle.innerCircleLineRadiusX,A.value=x.outThinCircle.innerCircleLineRadiusY,Xe.value=x.imageList||[],_s.value=x.outBorder.drawInnerCircle,Fs.value=x.outBorder.innerCircleLineWidth},Ws=$([]),Cr=async()=>{Ws.value=await g1()};Js(async()=>{await Cr(),mr(),ni(),Dt(),document.querySelectorAll(".font-select, .font-input").forEach(x=>{x instanceof HTMLElement&&as({target:x})})}),Yt([o,r,a,u,d,E,D,O,W,ne,T,k,ee,P,_e,Ee,ve,ft,ze,l,b,P,ce,Tt,pt,ft,_t,yt,_t,wt,gt,Ge,et,ks,Us,st,ut,nt,Me,m,h,W,L,U,R,c,S,I,A,g,z,V,y,w,Y,X,se,ae,Pe,Be,f,Ae,F,H,si,pe,he,Ve,ke,Nt,ys,xe,Xe,_s,Fs],()=>{_r()},{deep:!0});const ii=$([{id:"contract",name:"合同专用章",text:"合同专用章",fontSize:4.6,letterSpacing:0,lineSpacing:1.2,positionY:-5,compression:1},{id:"invoice",name:"印章类型",text:`发票专章
增值税专用`,fontSize:4.2,letterSpacing:0,lineSpacing:1.5,positionY:-4,compression:.9},{id:"finance",name:"财务专用章",text:`财务专用章
仅限报销使用`,fontSize:4,letterSpacing:0,lineSpacing:1.8,positionY:-3,compression:.85}]),Sr=()=>{localStorage.setItem("stampTypePresets",JSON.stringify(ii.value))},Tr=()=>{const x=localStorage.getItem("stampTypePresets");x&&(ii.value=JSON.parse(x))};Js(()=>{Tr()}),Yt(ii,()=>{Sr()},{deep:!0});const wr=()=>{window.open("https://xxss0903.github.io/extractstamp/","_blank")},as=x=>{var Q,j;const C=x.target,_=(C.tagName==="SELECT",C.value);if(C.style.setProperty("--current-font",_),C.tagName==="SELECT"){const Ie=(Q=C.parentElement)==null?void 0:Q.querySelector(".font-input");Ie&&(Ie.value=_,Ie.style.setProperty("--current-font",_))}if(C.tagName==="INPUT"){const Ie=(j=C.parentElement)==null?void 0:j.querySelector(".font-select");Ie&&(Ie.value=_,Ie.style.setProperty("--current-font",_))}},we=$({basic:!1,company:!1,stampType:!1,code:!1,taxNumber:!1,star:!1,security:!1,roughEdge:!1,aging:!1,innerCircle:!1,images:!1}),vt=x=>{we.value[x]=!we.value[x]},vo=$(-1),Er=async()=>{_o()},zr=x=>{try{const C=JSON.parse(JSON.stringify(x.config));C.ruler.showRuler=!0,C.ruler.showFullRuler=!0,C.ruler.showSideRuler=!0,C.ruler.showCrossLine=!0,C.ruler.showCurrentPositionText=!0,C.ruler.showDashLine=!0,C.company.startAngle=x.config.company.startAngle,C.company.rotateDirection=x.config.company.rotateDirection,console.log("load template",x,C),Xt.setDrawConfigs(C),ni(),Dt(),vo.value=-1-oi.findIndex(_=>_===x)}catch(C){console.error("加载默认模板失败:",C),alert("加载默认模板失败")}},Ir=()=>{oi.forEach(async x=>{const C=document.createElement("canvas");C.width=500,C.height=500;const _=new Na(C,8);x.config.ruler.showRuler=!1,_.setDrawConfigs(x.config),_.refreshStamp(),x.preview=C.toDataURL("image/png")})};Js(()=>{Ir()});const oi=[{name:"印章1",preview:"",config:V1},{name:"印章2",preview:"",config:mp}],Vs=$(localStorage.getItem("showSecurityWarning")!=="false");return Yt(Vs,x=>{localStorage.setItem("showSecurityWarning",String(x))}),(x,C)=>(le(),fe(Le,null,[ie.value?(le(),fe("div",hp,[p("div",yp,[p("h3",null,"⚠️ "+M(N(t)("legal.title")),1),p("div",_p,[p("p",null,[p("strong",null,M(N(t)("legal.warning")),1)]),p("p",null,[p("span",vp,M(N(t)("legal.securityItems")),1)])]),p("div",bp,[p("button",{onClick:vr,class:"cancel-button"},M(N(t)("legal.cancel")),1),p("button",{onClick:br,class:"confirm-button"},M(N(t)("legal.confirm")),1)])])])):dt("",!0),p("div",{class:Re(["container",{"has-warning":Vs.value}])},[Vs.value?(le(),fe("div",{key:0,class:Re(["legal-disclaimer",{hidden:!Vs.value}])},[p("div",Cp,[C[57]||(C[57]=p("div",{class:"warning-icon"},"⚠️",-1)),p("div",Sp,[p("h3",null,M(N(t)("legal.securityWarning")),1),p("p",null,[p("strong",null,M(N(t)("legal.securityNotice")),1)]),p("p",null,[p("span",Tp,M(N(t)("legal.securityItems")),1)]),p("button",{class:"close-warning",onClick:C[0]||(C[0]=_=>Vs.value=!1)},"×")])])],2)):dt("",!0),p("div",{class:"editor-controls",ref_key:"editorControls",ref:s},[p("div",wp,[p("button",{onClick:dr},M(N(t)("stamp.save")),1),p("button",{onClick:_o},M(N(t)("stamp.saveTemplate")),1),p("input",{type:"file",ref_key:"templateFileInput",ref:it,style:{display:"none"},accept:".json",onChange:rr},null,544),p("button",{onClick:lr},M(N(t)("stamp.loadTemplate")),1)]),p("div",Ep,[p("div",{class:"group-header",onClick:C[1]||(C[1]=_=>vt("basic"))},[p("h3",null,M(N(t)("stamp.basic.title")),1),p("span",{class:Re(["expand-icon",{expanded:we.value.basic}])},"▼",2)]),B(p("div",zp,[p("label",Ip,[B(p("input",{type:"checkbox","onUpdate:modelValue":C[2]||(C[2]=_=>i.value=_)},null,512),[[zt,i.value]]),K(" "+M(N(t)("stamp.basic.extractCircle")),1)]),p("label",null,[K(M(N(t)("stamp.basic.width"))+": ",1),B(p("input",{type:"number","onUpdate:modelValue":C[3]||(C[3]=_=>ks.value=_),min:"1",max:"50",step:"1"},null,512),[[Z,ks.value,void 0,{number:!0}]])]),p("label",null,[K(M(N(t)("stamp.basic.height"))+": ",1),B(p("input",{type:"number","onUpdate:modelValue":C[4]||(C[4]=_=>Us.value=_),min:"1",max:"50",step:"1"},null,512),[[Z,Us.value,void 0,{number:!0}]])]),p("label",null,[K(M(N(t)("stamp.basic.borderWidth"))+": ",1),B(p("input",{type:"number",step:"0.1","onUpdate:modelValue":C[5]||(C[5]=_=>D.value=_)},null,512),[[Z,D.value,void 0,{number:!0}]])]),p("label",null,[K(M(N(t)("stamp.basic.color"))+": ",1),B(p("input",{type:"color","onUpdate:modelValue":C[6]||(C[6]=_=>O.value=_)},null,512),[[Z,O.value]])]),p("label",Lp,[B(p("input",{type:"checkbox","onUpdate:modelValue":C[7]||(C[7]=_=>_s.value=_)},null,512),[[zt,_s.value]]),K(" "+M(N(t)("stamp.outBorder.enable")),1)]),_s.value?(le(),fe("label",Pp,[K(M(N(t)("stamp.outBorder.lineWidth"))+": ",1),B(p("input",{type:"number","onUpdate:modelValue":C[8]||(C[8]=_=>Fs.value=_),min:"0.1",max:"5",step:"0.1"},null,512),[[Z,Fs.value,void 0,{number:!0}]])])):dt("",!0)],512),[[bt,we.value.basic]])]),p("div",Mp,[p("div",{class:"group-header",onClick:C[9]||(C[9]=_=>vt("company"))},[p("h3",null,M(N(t)("stamp.company.title")),1),p("span",{class:Re(["expand-icon",{expanded:we.value.company}])},"▼",2)]),B(p("div",Ap,[(le(!0),fe(Le,null,Ft(he.value,(_,Q)=>(le(),fe("div",{key:Q,class:"company-item"},[p("div",xp,[p("span",null,M(N(t)("stamp.common.line",{index:Q+1})),1),p("button",{class:"small-button delete-button",onClick:j=>gr(Q)},M(N(t)("stamp.common.delete")),9,Np)]),p("label",null,[K(M(N(t)("stamp.company.name"))+": ",1),B(p("input",{type:"text","onUpdate:modelValue":j=>_.companyName=j},null,8,Dp),[[Z,_.companyName]])]),p("label",null,[K(M(N(t)("stamp.company.font"))+": ",1),p("div",Op,[B(p("select",{"onUpdate:modelValue":j=>_.fontFamily=j,class:"font-select",onChange:as},[(le(!0),fe(Le,null,Ft(Ws.value,j=>(le(),fe("option",{key:j,value:j,style:Is({fontFamily:j})},M(j),13,Fp))),128))],40,Rp),[[cs,_.fontFamily]]),B(p("input",{type:"text","onUpdate:modelValue":j=>_.fontFamily=j,class:"font-input",onInput:as,placeholder:N(t)("stamp.common.fontPlaceholder")},null,40,kp),[[Z,_.fontFamily]])])]),p("label",null,[K(M(N(t)("stamp.company.fontSize"))+": ",1),B(p("input",{type:"number","onUpdate:modelValue":j=>_.fontHeight=j,min:"1",max:"10",step:"0.1"},null,8,Up),[[Z,_.fontHeight,void 0,{number:!0}]])]),p("label",null,[K(M(N(t)("stamp.company.fontWeight"))+": ",1),B(p("select",{"onUpdate:modelValue":j=>_.fontWeight=j},[p("option",Vp,M(N(t)("stamp.common.fontWeight.normal")),1),p("option",$p,M(N(t)("stamp.common.fontWeight.bold")),1),C[58]||(C[58]=In('<option value="100">100</option><option value="200">200</option><option value="300">300</option><option value="400">400</option><option value="500">500</option><option value="600">600</option><option value="700">700</option><option value="800">800</option><option value="900">900</option>',9))],8,Wp),[[cs,_.fontWeight]])]),p("label",null,[K(M(N(t)("stamp.company.compression"))+": ",1),B(p("input",{type:"range","onUpdate:modelValue":j=>_.compression=j,min:"0.5",max:"1.5",step:"0.05"},null,8,Hp),[[Z,_.compression,void 0,{number:!0}]]),p("span",null,M(_.compression.toFixed(2)),1)]),p("label",null,[K(M(N(t)("stamp.company.distribution"))+": ",1),B(p("input",{type:"range","onUpdate:modelValue":j=>_.textDistributionFactor=j,min:"0",max:"50",step:"0.1"},null,8,jp),[[Z,_.textDistributionFactor,void 0,{number:!0}]]),p("span",null,M(_.textDistributionFactor.toFixed(2)),1)]),p("label",null,[K(M(N(t)("stamp.company.margin"))+": ",1),B(p("input",{type:"number","onUpdate:modelValue":j=>_.borderOffset=j,min:"-10",max:"10",step:"0.05"},null,8,Yp),[[Z,_.borderOffset,void 0,{number:!0}]])])]))),128)),p("button",{class:"add-button",onClick:pr},M(N(t)("stamp.common.addNew")),1)],512),[[bt,we.value.company]])]),p("div",Bp,[p("div",{class:"group-header",onClick:C[10]||(C[10]=_=>vt("stampType"))},[p("h3",null,M(N(t)("stamp.stampType.title")),1),p("span",{class:Re(["expand-icon",{expanded:we.value.stampType}])},"▼",2)]),B(p("div",Gp,[(le(!0),fe(Le,null,Ft(pe.value,(_,Q)=>(le(),fe("div",{key:Q,class:"stamp-type-item"},[p("div",Xp,[p("span",null,M(N(t)("stamp.stampType.line",{index:Q+1})),1),p("button",{class:"small-button delete-button",onClick:j=>ur(Q)},M(N(t)("stamp.stampType.delete")),9,Kp)]),p("label",null,[K(M(N(t)("stamp.stampType.type"))+": ",1),B(p("input",{type:"text","onUpdate:modelValue":j=>_.stampType=j},null,8,Jp),[[Z,_.stampType]])]),p("label",null,[K(M(N(t)("stamp.stampType.fontSize"))+": ",1),B(p("input",{type:"number","onUpdate:modelValue":j=>_.fontHeight=j,min:"1",max:"10",step:"0.1"},null,8,Qp),[[Z,_.fontHeight,void 0,{number:!0}]])]),p("label",null,[K(M(N(t)("stamp.stampType.font"))+": ",1),p("div",qp,[B(p("input",{type:"text","onUpdate:modelValue":j=>_.fontFamily=j,list:"stampTypeFontList",class:"font-input"},null,8,Zp),[[Z,_.fontFamily]]),p("datalist",eg,[(le(!0),fe(Le,null,Ft(Ws.value,j=>(le(),fe("option",{key:j,value:j},M(j),9,tg))),128))])])]),p("label",null,[K(M(N(t)("stamp.stampType.fontWeight"))+": ",1),B(p("select",{"onUpdate:modelValue":j=>_.fontWeight=j},C[59]||(C[59]=[In('<option value="normal">正常</option><option value="bold">粗体</option><option value="100">100</option><option value="200">200</option><option value="300">300</option><option value="400">400</option><option value="500">500</option><option value="600">600</option><option value="700">700</option><option value="800">800</option><option value="900">900</option>',11)]),8,sg),[[cs,_.fontWeight]])]),p("label",null,[K(M(N(t)("stamp.stampType.compression"))+": ",1),B(p("input",{type:"range","onUpdate:modelValue":j=>_.compression=j,min:"0.1",max:"1.5",step:"0.05"},null,8,ng),[[Z,_.compression,void 0,{number:!0}]]),p("span",null,M(_.compression.toFixed(2)),1)]),p("label",null,[K(M(N(t)("stamp.stampType.letterSpacing"))+": ",1),B(p("input",{type:"range","onUpdate:modelValue":j=>_.letterSpacing=j,min:"-1",max:"10",step:"0.05"},null,8,ig),[[Z,_.letterSpacing,void 0,{number:!0}]]),p("span",null,M(_.letterSpacing.toFixed(2)),1)]),p("label",null,[K(M(N(t)("stamp.stampType.verticalPosition"))+": ",1),B(p("input",{type:"number","onUpdate:modelValue":j=>_.positionY=j,min:"-20",max:"20",step:"0.5"},null,8,og),[[Z,_.positionY,void 0,{number:!0}]])])]))),128)),p("button",{class:"add-button",onClick:fr},M(N(t)("stamp.stampType.addNew")),1)],512),[[bt,we.value.stampType]])]),p("div",ag,[p("div",{class:"group-header",onClick:C[11]||(C[11]=_=>vt("code"))},[p("h3",null,M(N(t)("stamp.code.title")),1),p("span",{class:Re(["expand-icon",{expanded:we.value.code}])},"▼",2)]),B(p("div",lg,[p("label",null,[K(M(N(t)("stamp.code.code"))+": ",1),B(p("input",{"onUpdate:modelValue":C[12]||(C[12]=_=>a.value=_)},null,512),[[Z,a.value]])]),p("label",null,[K(M(N(t)("stamp.code.font"))+": ",1),p("div",rg,[B(p("select",{"onUpdate:modelValue":C[13]||(C[13]=_=>f.value=_),class:"font-select",onChange:as},[(le(!0),fe(Le,null,Ft(Ws.value,_=>(le(),fe("option",{key:_,value:_,style:Is({fontFamily:_})},M(_),13,cg))),128))],544),[[cs,f.value]]),B(p("input",{type:"text","onUpdate:modelValue":C[14]||(C[14]=_=>f.value=_),class:"font-input",onInput:as,placeholder:"输入字体名称"},null,544),[[Z,f.value]])])]),p("label",null,[K(M(N(t)("stamp.code.fontSize"))+": ",1),B(p("input",{type:"number","onUpdate:modelValue":C[15]||(C[15]=_=>d.value=_),step:"0.1"},null,512),[[Z,d.value,void 0,{number:!0}]])]),p("label",null,[K(M(N(t)("stamp.code.fontWeight"))+": ",1),B(p("select",{"onUpdate:modelValue":C[16]||(C[16]=_=>Be.value=_)},[p("option",fg,M(N(t)("stamp.common.fontWeight.normal")),1),p("option",ug,M(N(t)("stamp.common.fontWeight.bold")),1),C[60]||(C[60]=In('<option value="100">100</option><option value="200">200</option><option value="300">300</option><option value="400">400</option><option value="500">500</option><option value="600">600</option><option value="700">700</option><option value="800">800</option><option value="900">900</option>',9))],512),[[cs,Be.value]])]),p("label",null,[p("span",null,M(N(t)("stamp.common.compression",{value:pt.value.toFixed(2)})),1),B(p("input",{type:"range","onUpdate:modelValue":C[17]||(C[17]=_=>pt.value=_),min:"0.0",max:"3",step:"0.01"},null,512),[[Z,pt.value,void 0,{number:!0}]])]),p("label",null,[p("span",null,M(N(t)("stamp.common.distribution",{value:ne.value.toFixed(1)})),1),B(p("input",{type:"range","onUpdate:modelValue":C[18]||(C[18]=_=>ne.value=_),min:"0",max:"100",step:"0.5"},null,512),[[Z,ne.value,void 0,{number:!0}]])]),p("label",null,[K(M(N(t)("stamp.code.margin"))+": ",1),B(p("input",{type:"number","onUpdate:modelValue":C[19]||(C[19]=_=>ee.value=_),min:"-10",max:"20",step:"0.05"},null,512),[[Z,ee.value,void 0,{number:!0}]])])],512),[[bt,we.value.code]])]),p("div",pg,[p("div",{class:"group-header",onClick:C[20]||(C[20]=_=>vt("taxNumber"))},[p("h3",null,M(N(t)("stamp.taxNumber.title")),1),p("span",{class:Re(["expand-icon",{expanded:we.value.taxNumber}])},"▼",2)]),B(p("div",gg,[p("label",null,[K(M(N(t)("stamp.taxNumber.number"))+": ",1),B(p("input",{"onUpdate:modelValue":C[21]||(C[21]=_=>l.value=_)},null,512),[[Z,l.value]])]),p("label",null,[K(M(N(t)("stamp.taxNumber.font"))+": ",1),p("div",dg,[B(p("select",{"onUpdate:modelValue":C[22]||(C[22]=_=>Me.value=_),class:"font-select",onChange:as},[(le(!0),fe(Le,null,Ft(Ws.value,_=>(le(),fe("option",{key:_,value:_,style:Is({fontFamily:_})},M(_),13,mg))),128))],544),[[cs,Me.value]]),B(p("input",{type:"text","onUpdate:modelValue":C[23]||(C[23]=_=>Me.value=_),class:"font-input",onInput:as,placeholder:"输入字体名称"},null,544),[[Z,Me.value]])])]),p("label",null,[K(M(N(t)("stamp.taxNumber.fontWeight"))+": ",1),B(p("select",{"onUpdate:modelValue":C[24]||(C[24]=_=>Ae.value=_)},C[61]||(C[61]=[In('<option value="normal">正常</option><option value="bold">粗体</option><option value="100">100</option><option value="200">200</option><option value="300">300</option><option value="400">400</option><option value="500">500</option><option value="600">600</option><option value="700">700</option><option value="800">800</option><option value="900">900</option>',11)]),512),[[cs,Ae.value]])]),p("label",null,[p("span",null,M(N(t)("stamp.common.compression",{value:nt.value.toFixed(2)})),1),B(p("input",{type:"range","onUpdate:modelValue":C[25]||(C[25]=_=>nt.value=_),min:"0.0",max:"3",step:"0.01"},null,512),[[Z,nt.value,void 0,{number:!0}]])]),p("label",null,[p("span",null,M(N(t)("stamp.common.letterSpacing",{value:m.value.toFixed(2)})),1),B(p("input",{type:"range","onUpdate:modelValue":C[26]||(C[26]=_=>m.value=_),min:"-1",max:"20",step:"0.05"},null,512),[[Z,m.value,void 0,{number:!0}]])]),p("label",null,[p("span",null,M(N(t)("stamp.common.verticalPosition",{value:h.value.toFixed(1)})),1),B(p("input",{type:"range","onUpdate:modelValue":C[27]||(C[27]=_=>h.value=_),min:"-10",max:"10",step:"0.1"},null,512),[[Z,h.value,void 0,{number:!0}]])])],512),[[bt,we.value.taxNumber]])]),p("div",hg,[p("div",{class:"group-header",onClick:C[28]||(C[28]=_=>vt("images"))},[p("h3",null,M(N(t)("stamp.images.title")),1),p("span",{class:Re(["expand-icon",{expanded:we.value.images}])},"▼",2)]),B(p("div",yg,[(le(!0),fe(Le,null,Ft(Xe.value,(_,Q)=>(le(),fe("div",{key:Q,class:"image-item"},[p("div",_g,[p("span",null,M(N(t)("stamp.images.image",{index:Q+1})),1),p("button",{class:"small-button delete-button",onClick:j=>ar(Q)},"删除",8,vg)]),_.imageUrl?(le(),fe("div",bg,[p("img",{src:_.imageUrl,alt:N(t)("stamp.common.preview")},null,8,Cg)])):dt("",!0),p("label",null,[K(M(N(t)("stamp.images.select"))+": ",1),p("input",{type:"file",onChange:j=>cr(j,Q),accept:"image/*"},null,40,Sg)]),p("label",null,[K(M(N(t)("stamp.images.width"))+": ",1),B(p("input",{type:"number","onUpdate:modelValue":j=>_.imageWidth=j,min:"1",max:"100",step:"0.5"},null,8,Tg),[[Z,_.imageWidth,void 0,{number:!0}]])]),p("label",null,[K(M(N(t)("stamp.images.height"))+": ",1),B(p("input",{type:"number","onUpdate:modelValue":j=>_.imageHeight=j,min:"1",max:"100",step:"0.5"},null,8,wg),[[Z,_.imageHeight,void 0,{number:!0}]])]),p("label",null,[K(M(N(t)("stamp.images.positionX"))+": ",1),B(p("input",{type:"number","onUpdate:modelValue":j=>_.positionX=j,min:"-20",max:"20",step:"0.5"},null,8,Eg),[[Z,_.positionX,void 0,{number:!0}]])]),p("label",null,[K(M(N(t)("stamp.images.positionY"))+": ",1),B(p("input",{type:"number","onUpdate:modelValue":j=>_.positionY=j,min:"-20",max:"20",step:"0.5"},null,8,zg),[[Z,_.positionY,void 0,{number:!0}]])]),p("label",Ig,[B(p("input",{type:"checkbox","onUpdate:modelValue":j=>_.keepAspectRatio=j},null,8,Lg),[[zt,_.keepAspectRatio]]),K(" "+M(N(t)("stamp.images.keepRatio")),1)])]))),128)),p("button",{class:"add-button",onClick:or},M(N(t)("stamp.common.addNew")),1)],512),[[bt,we.value.images]])]),p("div",Pg,[p("div",{class:"group-header",onClick:C[29]||(C[29]=_=>vt("star"))},[p("h3",null,M(N(t)("stamp.star.title")),1),p("span",{class:Re(["expand-icon",{expanded:we.value.star}])},"▼",2)]),B(p("div",Mg,[p("label",Ag,[B(p("input",{type:"checkbox","onUpdate:modelValue":C[30]||(C[30]=_=>st.value=_)},null,512),[[zt,st.value]]),K(" "+M(N(t)("stamp.star.enable")),1)]),st.value?(le(),fe("div",xg,[p("label",null,[K(M(N(t)("stamp.star.diameter"))+": ",1),B(p("input",{type:"number","onUpdate:modelValue":C[31]||(C[31]=_=>W.value=_),step:"0.1"},null,512),[[Z,W.value,void 0,{number:!0}]])]),p("label",null,[K(M(N(t)("stamp.star.verticalPosition"))+": ",1),B(p("input",{type:"number","onUpdate:modelValue":C[32]||(C[32]=_=>ut.value=_),min:"-10",max:"10",step:"0.1"},null,512),[[Z,ut.value,void 0,{number:!0}]])])])):dt("",!0)],512),[[bt,we.value.star]])]),p("div",Ng,[p("div",{class:"group-header",onClick:C[33]||(C[33]=_=>vt("security"))},[p("h3",null,M(N(t)("stamp.security.title")),1),p("span",{class:Re(["expand-icon",{expanded:we.value.security}])},"▼",2)]),B(p("div",Dg,[p("label",null,[K(M(N(t)("stamp.security.enable"))+": ",1),B(p("input",{type:"checkbox","onUpdate:modelValue":C[34]||(C[34]=_=>wt.value=_)},null,512),[[zt,wt.value]])]),p("button",{onClick:C[35]||(C[35]=_=>Dt(!0,!1))},M(N(t)("stamp.security.refresh")),1),p("label",null,[K(M(N(t)("stamp.security.count"))+": ",1),B(p("input",{type:"range","onUpdate:modelValue":C[36]||(C[36]=_=>gt.value=_),min:"1",max:"100",step:"1"},null,512),[[Z,gt.value,void 0,{number:!0}]])]),p("label",null,[K(M(N(t)("stamp.security.length"))+": ",1),B(p("input",{type:"range","onUpdate:modelValue":C[37]||(C[37]=_=>Ge.value=_),min:"0.1",max:"100",step:"0.1"},null,512),[[Z,Ge.value,void 0,{number:!0}]])]),p("label",null,[K(M(N(t)("stamp.security.width"))+": ",1),B(p("input",{type:"range","onUpdate:modelValue":C[38]||(C[38]=_=>et.value=_),min:"0.05",max:"0.5",step:"0.05"},null,512),[[Z,et.value,void 0,{number:!0}]])])],512),[[bt,we.value.security]])]),p("div",Og,[p("div",{class:"group-header",onClick:C[39]||(C[39]=_=>vt("roughEdge"))},[p("h3",null,M(N(t)("stamp.roughEdge.title")),1),p("span",{class:Re(["expand-icon",{expanded:we.value.roughEdge}])},"▼",2)]),B(p("div",Rg,[p("label",Fg,[B(p("input",{type:"checkbox","onUpdate:modelValue":C[40]||(C[40]=_=>V.value=_)},null,512),[[zt,V.value]]),K(" "+M(N(t)("stamp.roughEdge.enable")),1)]),V.value?(le(),fe("label",kg,[K(M(N(t)("stamp.roughEdge.width"))+": ",1),B(p("input",{type:"range","onUpdate:modelValue":C[41]||(C[41]=_=>y.value=_),min:"0.05",max:"0.5",step:"0.05"},null,512),[[Z,y.value,void 0,{number:!0}]]),p("span",null,M(y.value.toFixed(2)),1)])):dt("",!0),V.value?(le(),fe("label",Ug,[K(M(N(t)("stamp.roughEdge.height"))+": ",1),B(p("input",{type:"range","onUpdate:modelValue":C[42]||(C[42]=_=>w.value=_),min:"0.1",max:"5",step:"0.1"},null,512),[[Z,w.value,void 0,{number:!0}]]),p("span",null,M(w.value.toFixed(1)),1)])):dt("",!0),V.value?(le(),fe("label",Wg,[K(M(N(t)("stamp.roughEdge.probability"))+": ",1),B(p("input",{type:"range","onUpdate:modelValue":C[43]||(C[43]=_=>Y.value=_),min:"0",max:"1",step:"0.01"},null,512),[[Z,Y.value,void 0,{number:!0}]]),p("span",null,M(Y.value.toFixed(2)),1)])):dt("",!0),V.value?(le(),fe("label",Vg,[K(M(N(t)("stamp.roughEdge.shift"))+": ",1),B(p("input",{type:"range","onUpdate:modelValue":C[44]||(C[44]=_=>X.value=_),min:"-10",max:"10",step:"0.01"},null,512),[[Z,X.value,void 0,{number:!0}]]),p("span",null,M(X.value.toFixed(2)),1)])):dt("",!0),V.value?(le(),fe("label",$g,[K(M(N(t)("stamp.roughEdge.points"))+": ",1),B(p("input",{type:"range","onUpdate:modelValue":C[45]||(C[45]=_=>se.value=_),min:"100",max:"1000",step:"10"},null,512),[[Z,se.value,void 0,{number:!0}]]),p("span",null,M(se.value),1)])):dt("",!0),p("button",{onClick:C[46]||(C[46]=_=>Dt(!1,!1,!0))},M(N(t)("stamp.roughEdge.refresh")),1)],512),[[bt,we.value.roughEdge]])]),p("div",Hg,[p("div",{class:"group-header",onClick:C[47]||(C[47]=_=>vt("aging"))},[p("h3",null,M(N(t)("stamp.aging.title")),1),p("span",{class:Re(["expand-icon",{expanded:we.value.aging}])},"▼",2)]),B(p("div",jg,[p("label",Yg,[B(p("input",{type:"checkbox","onUpdate:modelValue":C[48]||(C[48]=_=>b.value=_)},null,512),[[zt,b.value]]),K(" "+M(N(t)("stamp.aging.enable")),1)]),p("label",Bg,[B(p("input",{type:"checkbox","onUpdate:modelValue":C[49]||(C[49]=_=>z.value=_)},null,512),[[zt,z.value]]),K(" "+M(N(t)("stamp.aging.manual")),1)]),b.value?(le(),fe("label",Gg,[K(M(N(t)("stamp.aging.intensity"))+": ",1),B(p("input",{type:"range","onUpdate:modelValue":C[50]||(C[50]=_=>P.value=_),min:"0",max:"100",step:"1"},null,512),[[Z,P.value,void 0,{number:!0}]])])):dt("",!0),p("button",{onClick:C[51]||(C[51]=_=>Dt(!1,!0))},M(N(t)("stamp.aging.refresh")),1)],512),[[bt,we.value.aging]])]),p("div",Xg,[p("div",{class:"group-header",onClick:C[52]||(C[52]=_=>vt("innerCircle"))},[p("h3",null,M(N(t)("stamp.innerCircle.title")),1),p("span",{class:Re(["expand-icon",{expanded:we.value.innerCircle}])},"▼",2)]),B(p("div",Kg,[p("button",{onClick:hr},M(N(t)("stamp.innerCircle.addNew")),1),(le(!0),fe(Le,null,Ft(xe.value,(_,Q)=>(le(),fe("div",{key:Q,class:"inner-circle-item"},[p("div",Jg,[p("span",null,"第 "+M(Q+1)+" 行",1),p("button",{class:"small-button delete-button",onClick:j=>yr(Q)},"删除",8,Qg)]),p("label",null,[K(M(N(t)("stamp.innerCircle.lineWidth"))+": ",1),B(p("input",{type:"number","onUpdate:modelValue":j=>_.innerCircleLineWidth=j,min:"0.05",max:"0.5",step:"0.05"},null,8,qg),[[Z,_.innerCircleLineWidth,void 0,{number:!0}]])]),p("label",null,[K(M(N(t)("stamp.innerCircle.radiusX"))+": ",1),B(p("input",{type:"number","onUpdate:modelValue":j=>_.innerCircleLineRadiusX=j,min:"1",max:"50",step:"0.1"},null,8,Zg),[[Z,_.innerCircleLineRadiusX,void 0,{number:!0}]])]),p("label",null,[K(M(N(t)("stamp.innerCircle.radiusY"))+": ",1),B(p("input",{type:"number","onUpdate:modelValue":j=>_.innerCircleLineRadiusY=j,min:"1",max:"50",step:"0.1"},null,8,ed),[[Z,_.innerCircleLineRadiusY,void 0,{number:!0}]])])]))),128))],512),[[bt,we.value.innerCircle]])])],512),p("div",td,[p("div",sd,[p("div",nd,[p("h3",null,M(N(t)("stamp.aging.title")),1),p("label",id,[B(p("input",{type:"checkbox","onUpdate:modelValue":C[53]||(C[53]=_=>b.value=_)},null,512),[[zt,b.value]]),K(" "+M(N(t)("stamp.aging.enable")),1)]),p("label",od,[B(p("input",{type:"checkbox","onUpdate:modelValue":C[54]||(C[54]=_=>z.value=_)},null,512),[[zt,z.value]]),K(" "+M(N(t)("stamp.aging.manual")),1)]),b.value?(le(),fe("label",ad,[K(M(N(t)("stamp.aging.intensity"))+": ",1),B(p("input",{type:"range","onUpdate:modelValue":C[55]||(C[55]=_=>P.value=_),min:"0",max:"100",step:"1"},null,512),[[Z,P.value,void 0,{number:!0}]])])):dt("",!0),p("button",{onClick:C[56]||(C[56]=_=>Dt(!1,!0))},M(N(t)("stamp.aging.refresh")),1)]),p("div",ld,[p("h3",null,M(N(t)("stamp.extract.title")),1),p("button",{onClick:wr},M(N(t)("stamp.extract.tool")),1)])]),p("div",rd,[p("canvas",{ref_key:"stampCanvas",ref:n,width:"600",height:"600"},null,512)])]),p("div",cd,[p("div",fd,[p("h3",null,M(N(t)("stamp.template.title")),1),p("button",{class:"add-template",onClick:Er},[C[62]||(C[62]=p("span",null,"+",-1)),K(" "+M(N(t)("stamp.template.save")),1)])]),p("div",ud,[p("div",pd,[p("h4",null,M(N(t)("stamp.template.defaultTitle")),1),(le(),fe(Le,null,Ft(oi,(_,Q)=>p("div",{key:"default-"+Q,class:Re(["template-item",{active:vo.value===-1-Q}]),onClick:j=>zr(_)},[p("div",dd,[p("img",{src:_.preview,alt:N(t)("stamp.template.preview")},null,8,md)]),p("div",hd,[p("span",yd,M(N(t)("stamp.template.name"))+": "+M(_.name),1)])],10,gd)),64))])])])],2)],64))}}),bd={class:"language-switch"},Cd=mn({__name:"App",setup(e){const{locale:t}=_n(),s=$(t.value),n=i=>{t.value=i,s.value=i};return(i,o)=>(le(),fe(Le,null,[p("div",bd,[p("button",{onClick:o[0]||(o[0]=a=>n("zh")),class:Re({active:s.value==="zh"})},"中文",2),p("button",{onClick:o[1]||(o[1]=a=>n("en")),class:Re({active:s.value==="en"})},"English",2)]),je(vd)],64))}}),Sd=(e,t)=>{const s=e.__vccOpts||e;for(const[n,i]of t)s[n]=i;return s},Td=Sd(Cd,[["__scopeId","data-v-2421fd2e"]]),wd={zh:{menu:{home:"首页",about:"关于",contact:"联系我们"},legal:{title:"法律提示",warning:"请确认您已知悉并同意以下内容：",securityWarning:"安全警告",securityNotice:"本项目仅供学习和参考！严禁用于任何非法用途！",securityItems:`1. 本项目开源代码仅用于技术学习和交流。
2. 使用本项目生成的任何图片请勿用于任何非法用途。
3. 因违法使用本项目造成的任何法律责任和损失，需自行承担，与本项目无关。
4. 如果使用本项目请遵守相关法律法规。`,cancel:"取消",confirm:"我已知悉并同意"},stamp:{save:"保存印章",saveTemplate:"保存模板",loadTemplate:"加载模板",basic:{title:"印章基本设置",extractCircle:"提取圆形印章",width:"印章宽度 (mm)",height:"印章高度 (mm)",borderWidth:"圆形边框宽度 (mm)",color:"印章颜色"},company:{title:"公司名称列表设置",name:"公司名称",font:"字体",fontSize:"字体大小 (mm)",fontWeight:"字体粗细",compression:"压缩比例",distribution:"分布因子",margin:"边距 (mm)"},stampType:{title:"印章类型列表设置",type:"文字内容",fontSize:"字体大小 (mm)",font:"字体",fontWeight:"字体粗细",compression:"压缩比例",letterSpacing:"字符间距 (mm)",verticalPosition:"垂直位置 (mm)",addNew:"添加新行",delete:"删除",line:"第 {index} 行"},star:{title:"五角星设置",enable:"绘制五角星",diameter:"五角星直径 (mm)",verticalPosition:"垂直位置 (mm)"},security:{title:"防伪纹路设置",enable:"启用防伪纹路",refresh:"刷新纹路",count:"纹路数量",length:"纹路长度 (mm)",width:"纹路宽度 (mm)"},roughEdge:{title:"毛边效果设置",enable:"启用毛边效果",width:"毛边宽度 (mm)",height:"毛边高度 (mm)",probability:"毛边概率",shift:"毛边偏移 (mm)",points:"毛边点数",refresh:"刷新毛边"},aging:{title:"做旧效果",enable:"启用做旧效果",manual:"手动做旧",intensity:"做旧强度",refresh:"刷新做旧"},extract:{title:"提取印章",tool:"提取印章工具"},template:{title:"常用模板",save:"保存当前为模板",defaultTitle:"默认模板",preview:"预览",name:"模板名称"},common:{line:"第 {index} 行",delete:"删除",addNew:"添加新行",fontPlaceholder:"输入字体名称",fontWeightNormal:"正常",fontWeightBold:"粗体",fontWeight:{normal:"正常",bold:"粗体",light:"细体",medium:"中等",semibold:"半粗"},compression:"压缩比例：{value}",distribution:"分布因子：{value}",letterSpacing:"字符间距：{value} mm",verticalPosition:"垂直位置：{value} mm",preview:"预览图片"},code:{title:"印章编码设置",code:"印章编码",font:"字体",fontSize:"字体大小 (mm)",fontWeight:"字体粗细",compression:"压缩比例",distribution:"分布因子",margin:"边距 (mm)"},taxNumber:{title:"中间数字设置",number:"税号",font:"字体",fontSize:"字体大小 (mm)",fontWeight:"字体粗细",compression:"压缩比例",letterSpacing:"字符间距 (mm)",verticalPosition:"垂直位置调整 (mm)"},images:{title:"图片列表设置",image:"图片 {index}",select:"选择图片",width:"图片宽度 (mm)",height:"图片高度 (mm)",positionX:"水平位置 (mm)",positionY:"垂直位置 (mm)",keepRatio:"保持宽高比",preview:"预览"},innerCircle:{addNew:"新增",title:"内圈圆形设置",lineWidth:"内圈圆线宽 (mm)",radiusX:"内圈圆半径X (mm)",radiusY:"内圈圆半径Y (mm)"},outBorder:{enable:"显示外圈边框",lineWidth:"边框宽度 (mm)"}}},en:{menu:{home:"Home",about:"About",contact:"Contact"},legal:{title:"Legal Notice",warning:"Please confirm that you understand and agree to the following:",securityWarning:"Security Warning",securityNotice:"This project is for learning and reference only! It is strictly prohibited for any illegal use!",securityItems:`1. This project's open source code is only for technical learning and communication.
2. Do not use any images generated by this project for any illegal purposes.
3. You are responsible for any legal liability and losses caused by illegal use of this project.
4. Please comply with relevant laws and regulations when using this project.`,cancel:"Cancel",confirm:"I understand and agree"},stamp:{save:"Save Stamp",saveTemplate:"Save Template",loadTemplate:"Load Template",basic:{title:"Basic Stamp Settings",extractCircle:"Extract Circle Stamp",width:"Stamp Width (mm)",height:"Stamp Height (mm)",borderWidth:"Circle Border Width (mm)",color:"Stamp Color"},company:{title:"Company Name List Settings",name:"Company Name",font:"Font",fontSize:"Font Size (mm)",fontWeight:"Font Weight",compression:"Compression Ratio",distribution:"Distribution Factor",margin:"Margin (mm)"},stampType:{title:"Stamp Type List Settings",type:"Text Content",fontSize:"Font Size (mm)",font:"Font",fontWeight:"Font Weight",compression:"Compression Ratio",letterSpacing:"Letter Spacing (mm)",verticalPosition:"Vertical Position (mm)",addNew:"Add New",delete:"Delete",line:"Line {index}"},star:{title:"Star Settings",enable:"Draw Star",diameter:"Star Diameter (mm)",verticalPosition:"Vertical Position (mm)"},security:{title:"Security Pattern Settings",enable:"Enable Security Pattern",refresh:"Refresh Pattern",count:"Pattern Count",length:"Pattern Length (mm)",width:"Pattern Width (mm)"},roughEdge:{title:"Rough Edge Settings",enable:"Enable Rough Edge",width:"Edge Width (mm)",height:"Edge Height (mm)",probability:"Edge Probability",shift:"Edge Shift (mm)",points:"Edge Points",refresh:"Refresh Edge"},aging:{title:"Aging Effect",enable:"Enable Aging",manual:"Manual Aging",intensity:"Aging Intensity",refresh:"Refresh Aging"},extract:{title:"Extract Stamp",tool:"Extract Stamp Tool"},template:{title:"Common Templates",save:"Save Current as Template",defaultTitle:"Default Templates",preview:"Preview",name:"Template Name"},common:{line:"Line {index}",delete:"Delete",addNew:"Add New",fontPlaceholder:"Enter font name",fontWeightNormal:"Normal",fontWeightBold:"Bold",fontWeight:{normal:"Normal",bold:"Bold",light:"Light",medium:"Medium",semibold:"Semi Bold"},compression:"Compression: {value}",distribution:"Distribution: {value}",letterSpacing:"Letter Spacing: {value} mm",verticalPosition:"Vertical Position: {value} mm",preview:"Preview Image"},code:{title:"Stamp Code Settings",code:"Stamp Code",font:"Font",fontSize:"Font Size (mm)",fontWeight:"Font Weight",compression:"Compression Ratio",distribution:"Distribution Factor",margin:"Margin (mm)"},taxNumber:{title:"Center Number Settings",number:"Tax Number",font:"Font",fontSize:"Font Size (mm)",fontWeight:"Font Weight",compression:"Compression Ratio",letterSpacing:"Letter Spacing (mm)",verticalPosition:"Vertical Position (mm)"},images:{title:"Image List Settings",image:"Image {index}",select:"Select Image",width:"Image Width (mm)",height:"Image Height (mm)",positionX:"Horizontal Position (mm)",positionY:"Vertical Position (mm)",keepRatio:"Keep Aspect Ratio",preview:"Preview"},innerCircle:{addNew:"Add New",title:"Inner Circle Settings",lineWidth:"Circle Line Width (mm)",radiusX:"Circle Radius X (mm)",radiusY:"Circle Radius Y (mm)"},outBorder:{enable:"Show Outer Border",lineWidth:"Border Width (mm)"}}}},Ed=K0({locale:"zh",fallbackLocale:"en",messages:wd}),ir=Jf(Td);ir.use(Ed);ir.mount("#app");
